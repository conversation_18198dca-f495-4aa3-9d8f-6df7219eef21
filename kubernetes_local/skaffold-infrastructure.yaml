apiVersion: skaffold/v3
kind: Config
metadata:
  name: infrastructure
build:
  tagPolicy:
    sha256: { }
  local:
    useDockerCLI: false
    useBuildkit: true
  artifacts:
    - image: redis-image
      context: ../
      docker:
        dockerfile: kubernetes_local/images/redis.Dockerfile
deploy:
  helm:
    releases:
      - name: dev-deploy-infrastructure
        chartPath: helm-infrastructure
        valuesFiles:
          - helm-infrastructure/values.yaml
          - helm-infrastructure/values.dev.yaml
        namespace: juno-dev