# Default values for helm.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

hostname: juno.cpdev.ca
environment: prod
timezone: America/Vancouver
province: BC

juno:
  name: juno
  instanceName: juno
  cluster:
    port:
      container: 8080
      service: 8080
  local:
    port: 8080
    domain: host.minikube.internal
  adminUserName:
  adminPassword:
  adminPin:
  defaultUserName:
  defaultUserPassword:
  defaultUserPin:
  encryptionKey:
  drugrefUrl: https://drugref.apps.osdev.internal.cloudpractice.ca/drugref/DrugrefService
  trustedProxies: 10\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}|192\\.168\\.\\d{1,3}\\.\\d{1,3}
  faxingEnabled: true
  javaToolOptions: |
    --illegal-access=permit
    -Dfile.encoding=UTF8
    -Djava.io.tmpdir=/opt/juno/tmp
    -Djuno.propertiesFilename=/opt/juno/juno.properties
    -Dlogging.config=/opt/app-root/src/properties/logback.xml
    -Dserver.http2.enabled=true
    -Dlog4j2.formatMsgNoLookups=true
    -XX:+UseZGC
    -XX:ZCollectionInterval=30
    -XX:ZUncommitDelay=30
    -XX:MinRAMPercentage=20.0
    -XX:MaxRAMPercentage=40.0
    -XX:+HeapDumpOnOutOfMemoryError
    -XX:HeapDumpPath=/opt/juno/dumps
    -Dspring.profiles.active=osdev
