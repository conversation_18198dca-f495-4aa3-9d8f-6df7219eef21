kind: ConfigMap
apiVersion: v1
metadata:
  name: juno-env-config
  labels:
    name: juno-env-config
data:
  OPENSHIFT_INSTANCE_NAME: "{{ .Values.juno.instanceName }}"
  JUNO_INSTANCE_NAME: "{{ .Values.juno.instanceName }}"
  MYSQL_DATABASE: "{{ .Values.mysql.common.database }}"
  MYSQL_HOST: "{{ .Values.mysql.common.name }}"
  REDIS_ENDPOINT: "{{ .Values.redis.name }}:{{ .Values.redis.port.service }}"
  OSCARHOST_SETUP_DIR: "/opt/juno/create_instance"
  DRUGREF_URL: "{{ .Values.juno.drugrefUrl }}"
  TRUSTED_PROXIES: "{{ .Values.juno.trustedProxies }}"
  FAXING_ENABLED: "{{ .Values.juno.faxingEnabled }}"
  ENVIRONMENT: "{{ .Values.environment }}"
  CLINICAID_URL: ""
  JUNO_ERRORS_CHANNEL: ""
  JAVA_TOOL_OPTIONS: "{{ .Values.juno.javaToolOptions }}"
  PROVINCE: "{{ .Values.province }}"
  TZ: "{{ .Values.timezone }}"


