apiVersion: v1
kind: Secret
metadata:
  name: juno-secrets
type: Opaque
stringData:
  METRICS_PASSWORD: "{{ .Values.mysql.common.password }}"
  ADMIN_USER_LOGIN: "{{ .Values.juno.adminUserName }}"
  OSCARHOST_PASSWORD: "{{ .Values.juno.adminPassword }}"
  OSCARHOST_PIN: "{{ .Values.juno.adminPin }}"
  USER_LOGIN: "{{ .Values.juno.defaultUserName }}"
  USER_PASSWORD: "{{ .Values.juno.defaultUserPassword }}"
  USER_PIN: "{{ .Values.juno.defaultUserPin }}"
  ENCRYPTION_KEY: "{{ .Values.juno.encryptionKey }}"
  REDIS_PASSWORD: "{{ .Values.redis.password }}"