apiVersion: v1
kind: Service
metadata:
  name: juno
spec:
  {{ if eq .Values.juno.deployTarget "local" }}
  type: ExternalName
  externalName: {{ .Values.juno.local.domain }}
  ports:
    - name: web
      protocol: TCP
      port: {{ .Values.juno.local.port }}
      targetPort: {{ .Values.juno.local.port }}
  {{- else }}
  type: ClusterIP
  ports:
    - name: juno
      protocol: TCP
      port: {{ .Values.juno.cluster.port.service }}
      targetPort: {{ .Values.juno.cluster.port.container }}
  selector:
    name: juno
  {{- end }}
