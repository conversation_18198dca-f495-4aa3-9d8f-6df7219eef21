{{- if ne .Values.juno.deployTarget "local" -}}
kind: Deployment
apiVersion: apps/v1
metadata:
  name: juno
spec:
  replicas: 1
  selector:
    matchLabels:
      name: juno
  template:
    metadata:
      labels:
        name: juno
    spec:
      restartPolicy: Always
      schedulerName: default-scheduler
      initContainers:
        - resources: { }
          terminationMessagePath: /dev/termination-log
          name: copy-files
          imagePullPolicy: IfNotPresent
          terminationMessagePolicy: File
          image: juno
          command:
            - bash
          args:
            - '-c'
            - >-
              rm -rf /opt/juno/create_instance && mkdir -p
              /opt/juno/create_instance && cp -R
              /workspace/application/WEB-INF/classes/create_instance /opt/juno/ && echo
              'Copied create_instance folder to /opt/juno'
          volumeMounts:
            - name: juno-data
              mountPath: /opt/juno/
        - resources: { }
          terminationMessagePath: /dev/termination-log
          name: initialize-instance
          command:
            - ruby
          envFrom:
            - configMapRef:
                name: juno-env-config
            - secretRef:
                name: juno-secrets
            - secretRef:
                name: mariadb-secrets
          imagePullPolicy: IfNotPresent
          volumeMounts:
            - name: juno-configmap-volume
              mountPath: /usr/src/app/scripts/initialize_instance.rb
              subPath: initialize_instance.rb
            - name: juno-data
              mountPath: /opt/juno/
          terminationMessagePolicy: File
          image: juno-util
          args:
            - /usr/src/app/scripts/initialize_instance.rb
      containers:
        - resources:
            limits:
              cpu: '2'
              memory: 6096Mi
            requests:
              cpu: 10m
              memory: 800Mi
          terminationMessagePath: /dev/termination-log
          lifecycle:
            preStop:
              exec:
                command:
                  - /bin/bash
                  - '-c'
                  - sleep 20
          name: juno
          {{/* disable probes for dev use to prevent restart loops etc. */}}
          {{- if ne .Values.juno.environment "dev" -}}
          readinessProbe:
            httpGet:
              path: /juno/actuator/health/readiness
              port: {{ .Values.juno.cluster.port.container }}
              scheme: HTTP
            initialDelaySeconds: 60
            timeoutSeconds: 10
            periodSeconds: 15
            successThreshold: 1
            failureThreshold: 12
          livenessProbe:
            httpGet:
              path: /juno/actuator/health/liveness
              port: {{ .Values.juno.cluster.port.container }}
              scheme: HTTP
            initialDelaySeconds: 60
            timeoutSeconds: 10
            periodSeconds: 15
            successThreshold: 1
            failureThreshold: 12
          {{- end }}
          envFrom:
            - configMapRef:
                name: juno-env-config
            - secretRef:
                name: juno-secrets
            - secretRef:
                name: mariadb-secrets
          ports:
            - name: juno
              containerPort: {{ .Values.juno.cluster.port.container }}
              protocol: TCP
          imagePullPolicy: IfNotPresent
          volumeMounts:
            - name: juno-data
              mountPath: /opt/juno/
            - name: juno-configmap-volume
              mountPath: /usr/local/tomcat/scripts/initialize_instance.rb
              subPath: initialize_instance.rb
            - name: juno-configmap-volume
              mountPath: /opt/app-root/src/.my.cnf
              subPath: .my.cnf
            - name: juno-configmap-volume
              mountPath: /root/.bashrc
              subPath: .bashrc
            - name: juno-configmap-volume
              mountPath: /opt/app-root/src/properties/logback.xml
              subPath: logback.xml
          terminationMessagePolicy: File
          image: juno
      volumes:
        - name: juno-data
          persistentVolumeClaim:
            claimName: juno-pvc
        - name: juno-configmap-volume
          configMap:
            name: juno-config
            defaultMode: 493
{{- end }}
