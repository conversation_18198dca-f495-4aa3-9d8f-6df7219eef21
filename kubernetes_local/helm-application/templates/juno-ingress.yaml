kind: Ingress
apiVersion: networking.k8s.io/v1
metadata:
  name: juno-ingress
  annotations:
    cert-manager.io/cluster-issuer: ca-issuer
spec:
  rules:
    - host: {{ .Values.hostname }}
      http:
        paths:
          - path: /
            pathType: ImplementationSpecific
            backend:
              service:
                name: juno
                port:
                  number: {{ .Values.juno.cluster.port.service }}
  tls:
    - hosts:
        - {{ .Values.hostname }}
      secretName: tomcat-ingress-cert
