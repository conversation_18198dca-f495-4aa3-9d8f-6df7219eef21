# Kubernetes Dev Setup

Contents:
- [Setup](#setup)
- [Deployment](#deployment)
- [Local Development](#local-development-intellij)
- [Troubleshooting](#troubleshooting)

## Setup

This setup requires the
[Standard Kubernetes Developer Environment](https://well-health.atlassian.net/wiki/x/tISeMQE)

### Create Juno specific mounted folders
ensure the following paths/folders exist inside your OS specific cluster-mount folder:\
`cluster-mount/juno/secrets/`

### Add Juno Entry In Hosts File
#### Docker Desktop (windows):
add the entry to your host file:\
`127.0.0.1 juno.cpdev.ca`
#### Minikube (linux):
run `minikube ip` and add the entry to your host file: \
`{minikube_ip_address} juno.cpdev.ca`

### Create Project Namespace
create juno-dev namespace in kubernetes\
`kubectl create namespace juno-dev`\
change active namespace to the new namespace:\
`kubens juno-dev`
<hr>

## Deployment

For development, we will use skaffold to deploy the application to the kubernetes cluster.

Ensure you are in the correct directory\
`cd kubernetes_local`

###### Deploy static infrastructure.
This will only need to be run on a new cluster or if you have deleted something and want to recreate it.\
`skaffold run -f skaffold-static-infrastructure.yaml`

###### Deploy infrastructure.
This deploys things that may change occasionally but are not likely to change often, such as the database.
This only needs to be rerun when there are relevant changes.\
`skaffold run -f skaffold-infrastructure.yaml`

###### Deploy application(s).
This deploys the main application.\
`skaffold dev` or `skaffold run`

Note: an initial deployment to the cluster is required for juno to set up a database and properties file, etc.
Ensure this deploys successfully before switching to the local-development profile. 
See [Local Deployment Use](#local-deployment-use)

### Cheat Sheet & FAQ

Please refer to the
[Kubernetes Developer Cheat Sheet](https://well-health.atlassian.net/wiki/x/J4OhMQE)
for common commands and questions.

### Application Access
Access the application on your browser at:\
https://juno.cpdev.ca/juno/index.jsp
<hr>

## Local Development (intelliJ)

To speed up development, Juno can be run locally.
To do this, we will replace the containerized version of the project with a local version and
redirect the cluster traffic to it.

#### Local Properties Configuration

Local Development requires a separate properties file configuration.
This is an unfortunate requirement due to the reliance on the old properties file system.

- Create a copy of the properties file in your cluster-mount/juno folder.
- Name the copy juno-local.properties
- Update the database connection string to `***********************************/`

#### Local Intellij Setup & Configuration Settings
Ensure the local juno setup is configured as follows.

##### Docker run target

Go to Settings > Build,Execution,Deployment > Run Targets

Create a new Docker run target with the following configuration:
- Dockerfile: `kubernetes_local/images/juno.Dockerfile`
- Context folder: `.`
- Project path on target: `/app`
- Optional:
	- Image tag: `juno:dev`
	- build options:
      - `--target=image-setup`
    - run options:
      - Windows:\
        `--rm -p 8080:8080 -v $USER_HOME$/cluster-mount/juno:/opt/juno`
      - Linux:\
        `--rm --network=host -v $USER_HOME$/cluster-mount/juno:/opt/juno`
- Uncheck `Rebuild image automatically before running`


##### Docker run configuration

There should be a `Juno Application (Docker)` String Boot configuration included in the project.

The run config should have the following configuration:
- Run on target: docker (setup above)
- Use java 16 (from docker)
- VM options: `--illegal-access=permit -Djuno.propertiesFilename=/opt/juno/juno.properties`
- active profiles: `osdev,dev,localhost`

#### Local Deployment Use

To switch traffic to the local juno project,
run skaffold with the `juno-local` profile set:\
`skaffold run -p juno-local`\
To return to the containerized version, re-run without the local profile:\
`skaffold run`

###### OMD, HRM, and OLIS setup
Most of the cluster should be pre-configured to work with the PST environment.\
However, some manual setup is required to get OMD, HRM, and OLIS working.
 - Ensure that PST environment secrets are present in the mounted directory under \
`secrets/ehr/`, `secrets/hrm/` etc. as these can not be committed to the repo. \
See application.properties for the expected filenames.
 - Set the signing-certificate-password ENV variable or override it in your application-localhost.properties file.
 - Host file entries will need to be set on your local machine as per the PST environment instructions from OMD.
<hr>

## Troubleshooting

### Generic Cluster Troubleshooting

Please refer to the troubleshooting section of the cluster setup pages:
- [Windows](https://well-health.atlassian.net/wiki/x/Y4AYKgE)
- [Linux](https://well-health.atlassian.net/wiki/x/ewAUKgE)

### Juno Specific Troubleshooting

#### Juno Not Starting

##### Unable to create tempDir

When attempting to start Juno, you may encounter the following error:
`org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is org.springframework.boot.web.server.WebServerException: Unable to create tempDir. java.io.tmpdir is set to /opt/juno/tmp`

You may need to also create the tmp folder in the juno folder. `cluster-mount/juno/tmp`

