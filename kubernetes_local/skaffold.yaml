apiVersion: skaffold/v3
kind: Config
metadata:
  name: juno-application
build:
  tagPolicy:
    sha256: { }
  local:
    useDockerCLI: false
    useBuildkit: true
  artifacts:
    - image: juno
      context: ../
      docker:
        dockerfile: kubernetes_local/images/juno.Dockerfile
        buildArgs:
          SOURCE_REPO: .
    - image: juno-util
      context: ../../../openshift/openshift-tools/juno-images/juno-util/
      docker:
        dockerfile: ../../../openshift/openshift-tools/juno-images/juno-util/Dockerfile
deploy:
 helm:
   releases:
     - name: dev-deploy-application
       chartPath: helm-application
       valuesFiles:
         # load the mysql values first, as we require some shared values
         - helm-infrastructure/values.yaml
         - helm-infrastructure/values.dev.yaml
         - helm-application/values.yaml
         - helm-application/values.dev.yaml
       setValues:
         juno.deployTarget: cluster
       namespace: juno-dev

profiles:
  - name: juno-local
    patches:
      - op: replace
        path: /deploy/helm/releases/0/setValues/juno.deployTarget
        value: local
      - op: remove
        path: /build/artifacts/0