apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis-deployment
spec: # specification for deployment resource
  replicas: 1
  selector:
    matchLabels:
      app: {{ .Values.redis.name }}
  template: # blueprint for pods
    metadata:
      labels:
        app: {{ .Values.redis.name }} # service will look for this label
    spec: # specification for pods
      containers:
      - name: redis
        image: redis-image
        ports:
        - containerPort: {{ .Values.redis.port.container }}
        env:
        - name: ENVIRONMENT
          value: {{ .Values.environment }}
        volumeMounts:
          - name: redis-data
            mountPath: /var/lib/redis/data
          - name: redis-configmap-volume
            mountPath: /etc/redis.conf
            subPath: redis.conf
      volumes:
        - name: redis-data
          persistentVolumeClaim:
            claimName: redis-pvc
        - name: redis-configmap-volume
          configMap:
           name: redis-config
