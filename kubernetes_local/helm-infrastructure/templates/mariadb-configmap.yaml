kind: ConfigMap
apiVersion: v1
metadata:
  name: mariadb-config
  labels:
    name: mariadb-config
data:
  .bashrc: |-
    # .bashrc

    # User specific aliases and functions

    alias rm='rm -i'
    alias cp='cp -i'
    alias mv='mv -i'
    alias vi='vim'
    alias ll='ls -l --color=auto'
    alias mysql='mysql --i-am-a-dummy'

    export EDITOR=vim
    if [ "$ENVIRONMENT" == "prod" ]; then
      export PS1="\[\e[41m\]$PRACTICE_ID $ENVIRONMENT\[\e[0m\] \[\e[0;31m\]\h\[\e[m\] \$ "
    else
      export PS1="\[\e[42m\]$PRACTICE_ID $ENVIRONMENT\[\e[0m\] \[\e[0;32m\]\h\[\e[m\] \$ "
    fi

    # Source global definitions
    if [ -f /etc/bashrc ]; then
      . /etc/bashrc
    fi
  
