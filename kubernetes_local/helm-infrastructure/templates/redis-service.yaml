apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.redis.name }}
spec:
  type: ClusterIP
  ports:
    - name: redis-port
      protocol: TCP
      port: {{ .Values.redis.port.service }}
      targetPort: {{ .Values.redis.port.container }}
  selector:
    app: {{ .Values.redis.name }}
---
# create a nodeport to allow access to the service from localhost
apiVersion: v1
kind: Service
metadata:
  name: redis-nodeport
spec:
  type: NodePort
  selector:
    app: {{ .Values.redis.name }}
  ports:
    - port: {{ .Values.redis.port.service }}
      targetPort: {{ .Values.redis.port.container }}
      name: redis-port
      # external programs can access the deployment on this port
      nodePort: {{ .Values.redis.port.external }}