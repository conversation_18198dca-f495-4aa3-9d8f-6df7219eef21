apiVersion: v1
kind: Service
metadata:
  name: mariadb
spec:
  type: ClusterIP
  ports:
    - name: mariadb-port
      protocol: TCP
      port: {{ .Values.mysql.common.port.service }}
      targetPort: {{ .Values.mysql.common.port.container }}
  selector:
    app: mariadb
---
# create a nodeport to allow access to the service from localhost
apiVersion: v1
kind: Service
metadata:
  name: mariadb-nodeport
spec:
  type: NodePort
  selector:
    app: mariadb
  ports:
    - port: {{ .Values.mysql.common.port.service }}
      targetPort: {{ .Values.mysql.common.port.container }}
      name: mariadb-port
      # localhost programs can access the database service on this port
      nodePort: {{ .Values.mysql.common.port.external }}