apiVersion: apps/v1
kind: Deployment # what to create?
metadata:
  name: mariadb-deployment
spec: # specification for deployment resource
  replicas: 1 # how many replicas of pods we want to create
  selector:
    matchLabels:
      app: {{ .Values.mysql.common.name }}
  template: # blueprint for pods
    metadata:
      labels:
        app: {{ .Values.mysql.common.name }} # service will look for this label
    spec: # specification for pods
      containers:
      - name: mariadb
        image: mariadb:10.5
        ports:
        - containerPort: {{ .Values.mysql.common.port.container }}
        envFrom:
          - secretRef:
              name: mariadb-secrets
        env:
        - name: ENVIRONMENT
          value: {{ .Values.environment }}
        - name: MYSQL_DATABASE
          value: {{ .Values.mysql.common.database }}
        - name: TZ
          value: {{ .Values.timezone }}
        volumeMounts:
          - name: mariadb-data
            mountPath: /var/lib/mysql/
          - name: mysql-configmap-volume
            mountPath: /root/.bashrc
            subPath: .bashrc
      volumes:
        - name: mariadb-data
          persistentVolumeClaim:
            claimName: mariadb-pvc
        - name: mysql-configmap-volume
          configMap:
            name: mariadb-config
