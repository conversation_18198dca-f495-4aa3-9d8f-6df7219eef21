# define a local persistent volume so that files can be accessed from the host machine and shared with local programs
apiVersion: v1
kind: PersistentVolume
metadata:
  name: juno-local-pv
spec:
  accessModes:
  - ReadWriteOnce
  capacity:
    storage: 5Gi
  persistentVolumeReclaimPolicy: Retain
  storageClassName: {{ .Values.cluster.localStorageClass }}
  hostPath:
    path: {{ .Values.localhost.mountPath }}
