# Docker file for building juno development images
FROM adoptopenjdk:16.0.1_9-jdk-hotspot-focal AS image-setup

WORKDIR /tmp
RUN apt update -y && apt install -y wget xfonts-75dpi openssl vim git
# wkhtmltopdf installation
RUN wget https://github.com/cloudpracticeinc/binaries/releases/download/wkhtmltox_0.12.2-1/wkhtmltox_0.12.2-1.focal_amd64.deb
RUN apt install -y ./wkhtmltox_0.12.2-1.focal_amd64.deb
# ghostscript installation
RUN wget https://github.com/ArtifexSoftware/ghostpdl-downloads/releases/download/gs927/ghostscript-9.27-linux-x86_64.tgz
RUN tar xvf ghostscript-9.27-linux-x86_64.tgz && cp ghostscript-9.27-linux-x86_64/gs-927-linux-x86_64 /usr/bin/gs

FROM maven:3.6.3-openjdk-16 AS builder

ARG SOURCE_REPO=../..

ENV TZ=America/Vancouver
WORKDIR /app
COPY ${SOURCE_REPO} .

# unset MAVEN_CONFIG due to issue: https://github.com/carlossg/docker-maven/issues/301
ENV MAVEN_CONFIG=''
RUN --mount=type=cache,target=/root/.m2 ./build.sh --no-transfer-progress

# tomcat run stage
FROM image-setup as image

WORKDIR /workspace
COPY --from=builder /app/target/*.war ./juno.war
RUN java -Djarmode=layertools -jar juno.war extract

EXPOSE 8080
CMD ["java", "-jar", "juno.war"]