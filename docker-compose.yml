version: '3.3'

services:
  local_mariadb:
    image: mariadb:10.4
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: admin
      MYSQL_DATABASE: juno_db  # Create a database named 'juno_db'
      MYSQL_USER: juno_u  # Create a user named 'juno_u'
      MYSQL_PASSWORD: juno_u_password  # Set password for user 'juno_u'
    ports:
      - "13007:3306"  # Map container port 3306 to host port 13006
    volumes:
      - ./data:/var/lib/mysql_juno
      - ./juno_db.dump.sql:/docker-entrypoint-initdb.d/juno_db.dump.sql  # Mount SQL dump file
    command: ['--character-set-server=utf8mb4', '--collation-server=utf8mb4_unicode_ci', '--log-output=NONE', '--slow-query-log=0', '--general-log=0', '--innodb-flush-log-at-trx-commit=2', '--innodb-log-buffer-size=3M', '--innodb-buffer-pool-size=180M']  # Specify DB config options
    #healthcheck:
    #  test: ["CMD", "mysqladmin", "ping", "-p${MYSQL_ROOT_PASSWORD}"]
    #  interval: 10s
    #  timeout: 5s
    #  retries: 3
