<?xml version="1.0" encoding="UTF-8"?>
<schema xmlns="http://www.w3.org/2001/XMLSchema" targetNamespace="http://www.bornontario.ca/18MEWBV" xmlns:tns="http://www.bornontario.ca/18MEWBV" elementFormDefault="qualified">

    <complexType name="BORN18MEWBV_Batch">
    	<sequence>
    		<element name="PatientInfo" type="tns:PatientInfo" minOccurs="1" maxOccurs="unbounded"></element>
    	</sequence>
    </complexType>

	<element name="BORN18MEWBV_Batch" type="tns:BORN18MEWBV_Batch"></element>

	<simpleType name="YesNoUnknown">
		<restriction base="string">
			<enumeration value="Y"></enumeration>
			<enumeration value="N"></enumeration>
			<enumeration value="U"></enumeration>
		</restriction>
	</simpleType>
	<simpleType name="YesNoNotDiscussed">
		<restriction base="int">
			<enumeration value="0"></enumeration>
			<enumeration value="1"></enumeration>
			<enumeration value="2"></enumeration>
		</restriction>
	</simpleType>
	<simpleType name="DiscussedConcernNotDiscussed">
		<restriction base="int">
			<enumeration value="0"></enumeration>
			<enumeration value="1"></enumeration>
			<enumeration value="2"></enumeration>
		</restriction>
	</simpleType>
	<simpleType name="AttainedNotAttainedUnknown">
		<restriction base="int">
			<enumeration value="0"></enumeration>
			<enumeration value="1"></enumeration>
			<enumeration value="2"></enumeration>
		</restriction>
	</simpleType>
	<simpleType name="NDDSQuestionNum">
		<restriction base="int">
			<enumeration value="1"></enumeration>
			<enumeration value="2"></enumeration>
			<enumeration value="3"></enumeration>
			<enumeration value="4"></enumeration>
			<enumeration value="5"></enumeration>
			<enumeration value="6"></enumeration>
			<enumeration value="7"></enumeration>
			<enumeration value="8"></enumeration>
			<enumeration value="9"></enumeration>
			<enumeration value="10"></enumeration>
			<enumeration value="11"></enumeration>
			<enumeration value="12"></enumeration>
			<enumeration value="13"></enumeration>
			<enumeration value="14"></enumeration>
			<enumeration value="15"></enumeration>
			<enumeration value="16"></enumeration>
			<enumeration value="17"></enumeration>
		</restriction>
	</simpleType>
	<simpleType name="NormalAbnormalUnknown">
		<restriction base="int">
			<enumeration value="0"></enumeration>
			<enumeration value="1"></enumeration>
			<enumeration value="2"></enumeration>
		</restriction>
	</simpleType>
	<simpleType name="HealthCardType">
		<restriction base="int">
			<enumeration value="1"></enumeration>
			<enumeration value="2"></enumeration>
			<enumeration value="0"></enumeration>
		</restriction>
	</simpleType>
	<simpleType name="Vaccine">
		<restriction base="int">
			<enumeration value="1"></enumeration>
			<enumeration value="2"></enumeration>
			<enumeration value="3"></enumeration>
			<enumeration value="4"></enumeration>
			<enumeration value="5"></enumeration>
			<enumeration value="6"></enumeration>
			<enumeration value="7"></enumeration>
			<enumeration value="8"></enumeration>
			<enumeration value="9"></enumeration>
			<enumeration value="10"></enumeration>
			<enumeration value="11"></enumeration>
			<enumeration value="12"></enumeration>
			<enumeration value="13"></enumeration>
		</restriction>
	</simpleType>
	<simpleType name="APGAR">
		<restriction base="int">
			<enumeration value="0"></enumeration>
			<enumeration value="1"></enumeration>
			<enumeration value="2"></enumeration>
			<enumeration value="3"></enumeration>
			<enumeration value="4"></enumeration>
			<enumeration value="5"></enumeration>
			<enumeration value="6"></enumeration>
			<enumeration value="7"></enumeration>
			<enumeration value="8"></enumeration>
			<enumeration value="9"></enumeration>
			<enumeration value="10"></enumeration>
			<enumeration value="-1"></enumeration>
		</restriction>
	</simpleType>
	<simpleType name="Gender">
		<restriction base="string">
			<enumeration value="M"></enumeration>
			<enumeration value="F"></enumeration>
			<enumeration value="O"></enumeration>
			<enumeration value="U"></enumeration>
		</restriction>
	</simpleType>
	<simpleType name="CountryProvince">
		<restriction base="string">
			<enumeration value="CA-AB"></enumeration>
			<enumeration value="CA-BC"></enumeration>
			<enumeration value="CA-MB"></enumeration>
			<enumeration value="CA-NB"></enumeration>
			<enumeration value="CA-NL"></enumeration>
			<enumeration value="CA-NT"></enumeration>
			<enumeration value="CA-NS"></enumeration>
			<enumeration value="CA-NU"></enumeration>
			<enumeration value="CA-ON"></enumeration>
			<enumeration value="CA-PE"></enumeration>
			<enumeration value="CA-QC"></enumeration>
			<enumeration value="CA-SK"></enumeration>
			<enumeration value="CA-YT"></enumeration>
			<enumeration value="USA"></enumeration>
			<enumeration value="OUTP"></enumeration>
			<enumeration value="OUTC"></enumeration>
			<enumeration value="UNKN"></enumeration>
		</restriction>
	</simpleType>
	<simpleType name="ProblemsAndPlans">
		<restriction base="string">
			<enumeration value="UNKN"></enumeration>
			<enumeration value="OEYC"></enumeration>
			<enumeration value="PSLP"></enumeration>
			<enumeration value="PH"></enumeration>
			<enumeration value="HBHC"></enumeration>
			<enumeration value="PAED"></enumeration>
			<enumeration value="DS"></enumeration>
			<enumeration value="FSP"></enumeration>
			<enumeration value="SCHL"></enumeration>
			<enumeration value="CPRP"></enumeration>
			<enumeration value="IHP"></enumeration>
			<enumeration value="SHI"></enumeration>
			<enumeration value="AIS"></enumeration>
			<enumeration value="CMHS"></enumeration>
			<enumeration value="CTC"></enumeration>
			<enumeration value="CCAC"></enumeration>
			<enumeration value="SPDD"></enumeration>
			<enumeration value="IDP"></enumeration>
			<enumeration value="SMS"></enumeration>
			<enumeration value="BLVP"></enumeration>
			<enumeration value="SVO"></enumeration>
			<enumeration value="CC"></enumeration>
			<enumeration value="SCCP"></enumeration>
			<enumeration value="CPS"></enumeration>
		</restriction>
	</simpleType>
	<simpleType name="unsignedDecimal21">
		<restriction base="decimal">
			<pattern value="\d{1,2}(\.\d)?"></pattern>
		</restriction>
	</simpleType>
	<simpleType name="unsignedDecimal31">
		<restriction base="decimal">
			<pattern value="\d{1,3}(\.\d)?"></pattern>
		</restriction>
	</simpleType>
	<simpleType name="unsignedInt5">
		<restriction base="unsignedInt">
			<maxInclusive value="99999"/>
		</restriction>
	</simpleType>
	<simpleType name="string50">
		<restriction base="string">
			<maxLength value="50"/>
		</restriction>
	</simpleType>
	<simpleType name="string250">
		<restriction base="string">
			<maxLength value="250"/>
		</restriction>
	</simpleType>
	<simpleType name="string2500">
		<restriction base="string">
			<maxLength value="2500"/>
		</restriction>
	</simpleType>

	<complexType name="PatientInfo">
    	<sequence>
			<element name="OrganizationID">
    			<simpleType>
    				<restriction base="tns:string50">
						<minLength value="1"/>
    				</restriction>
    			</simpleType>
			</element>
			<element name="HealthCardType" type="tns:HealthCardType"></element>
			<element name="HealthCardNum">
    			<simpleType>
    				<restriction base="string">
						<minLength value="1"/>
						<maxLength value="20"/>
    				</restriction>
    			</simpleType>
			</element>
			<element name="ChartNumber">
    			<simpleType>
    				<restriction base="string">
						<minLength value="1"/>
						<maxLength value="15"/>
    				</restriction>
    			</simpleType>
			</element>
			<element name="UniqueVendorIDSequence">
    			<simpleType>
    				<restriction base="string">
						<minLength value="1"/>
						<maxLength value="20"/>
    				</restriction>
    			</simpleType>
			</element>
			<element name="LastName">
    			<simpleType>
    				<restriction base="tns:string250">
						<minLength value="1"/>
    				</restriction>
    			</simpleType>
			</element>
			<element name="FirstName">
    			<simpleType>
    				<restriction base="tns:string250">
						<minLength value="1"/>
    				</restriction>
    			</simpleType>
			</element>
			<element name="DOB" type="date"></element>
			<element name="Gender" type="tns:Gender"></element>
			<element name="ResidentAddressLine1" type="tns:string250" minOccurs="0"></element>
			<element name="ResidentAddressLine2" type="tns:string250" minOccurs="0"></element>
			<element name="ResidentCity" minOccurs="0">
    			<simpleType>
    				<restriction base="string">
						<maxLength value="100"/>
    				</restriction>
    			</simpleType>
			</element>
			<element name="ResidentPostalCode" minOccurs="0">
    			<simpleType>
    				<restriction base="string">
    					<pattern value="[A-Z][0-9][A-Z][0-9][A-Z][0-9]"></pattern>
    				</restriction>
    			</simpleType>
			</element>
			<element name="ResidentCountryProvince" type="tns:CountryProvince" minOccurs="0"></element>
			<element name="PastProblemsRiskFactor" type="tns:string250" minOccurs="0"></element>
			<element name="FamilyHistory" type="tns:string250" minOccurs="0"></element>
			<element name="GestationalAge" minOccurs="0">
				<simpleType>
					<restriction base="unsignedInt">
						<maxInclusive value="999"/>
					</restriction>
				</simpleType>
			</element>
			<element name="BirthLength" type="tns:unsignedDecimal21" minOccurs="0"></element>
			<element name="BirthWeight">
				<simpleType>
					<restriction base="unsignedInt">
						<maxInclusive value="9999"/>
					</restriction>
				</simpleType>
			</element>
			<element name="BirthHeadCirc" type="tns:unsignedDecimal21" minOccurs="0"></element>
			<element name="DischargeWeight" type="tns:unsignedInt5" minOccurs="0"></element>
			<element name="ProviderNumber" minOccurs="0">
    			<simpleType>
    				<restriction base="string">
						<maxLength value="6"/>
    				</restriction>
    			</simpleType>
			</element>
			<element name="CPSONumber" minOccurs="0">
    			<simpleType>
    				<restriction base="string">
						<maxLength value="5"/>
    				</restriction>
    			</simpleType>
			</element>
			<element name="BillingDate" type="date" minOccurs="0"></element>
			<element name="BillingCode" minOccurs="0">
    			<simpleType>
    				<restriction base="string">
						<maxLength value="6"/>
    				</restriction>
    			</simpleType>
			</element>
			<element name="NDDS" type="tns:NDDS" minOccurs="0"></element>
			<element name="RBR" type="tns:RBR" minOccurs="0"></element>
			<element name="M18MARKERS" type="tns:M18MARKERS" minOccurs="0"></element>
		</sequence>
    </complexType>

	<complexType name="NDDS">
    	<sequence>
			<element name="LastUpdateDateTime" type="dateTime"></element>
			<element name="NDDSQ01" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="NDDSQ02" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="NDDSQ03" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="NDDSQ04" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="NDDSQ05" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="NDDSQ06" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="NDDSQ07" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="NDDSQ08" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="NDDSQ09" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="NDDSQ10" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="NDDSQ11" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="NDDSQ12" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="NDDSQ13" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="NDDSQ14" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="NDDSQ15" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="NDDSQ16" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="NDDSQ17" type="tns:YesNoUnknown" minOccurs="0"></element>
    	</sequence>
    </complexType>

	<complexType name="M18MARKERS">
    	<sequence>
			<element name="LastUpdateDateTime" type="dateTime"></element>
			<element name="FSA" minOccurs="0">
    			<simpleType>
    				<restriction base="string">
    					<pattern value="[A-Z][0-9][A-Z]"></pattern>
    				</restriction>
    			</simpleType>
			</element>
			<element name="Premature" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="HighRisk" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="NoConcerns" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="APGAR1" type="tns:APGAR" minOccurs="0"></element>
			<element name="APGAR5" type="tns:APGAR" minOccurs="0"></element>
			<element name="SecondHandSmokeExposureInUtero" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="SecondHandSmokeExposureSinceBirth" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="SecondHandSmokeExposureNoExposure" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="SubstanceAbuseInUteroYes" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="SubstanceAbuseInUteroNo" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="SubstanceAbuseDrugs" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="SubstanceAbuseAlcohol" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="MoreThanOneDevAreaAffectedYes" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="MoreThanOneDevAreaAffectedNo" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="NeedForAddAssessmentYes" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="NeedForAddAssessmentNo" type="tns:YesNoUnknown" minOccurs="0"></element>
    	</sequence>
    </complexType>

	<complexType name="RBR">
    	<sequence>
			<element name="LastUpdateDate" type="dateTime"></element>
			<element name="RBRW01" type="tns:RBRW01" minOccurs="0"></element>
			<element name="RBRW02" type="tns:RBRW02" minOccurs="0"></element>
			<element name="RBRM01" type="tns:RBRM01" minOccurs="0"></element>
			<element name="RBRW01W02M01" type="tns:RBRW01W02M01" minOccurs="0"></element>
			<element name="RBRM02" type="tns:RBRM02" minOccurs="0"></element>
			<element name="RBRM04" type="tns:RBRM04" minOccurs="0"></element>
			<element name="RBRM06" type="tns:RBRM06" minOccurs="0"></element>
			<element name="RBRM02M04M06" type="tns:RBRM02M04M06" minOccurs="0"></element>
			<element name="RBRM09" type="tns:RBRM09" minOccurs="0"></element>
			<element name="RBRM12-13" type="tns:RBRM12-13" minOccurs="0"></element>
			<element name="RBRM09M12-13" type="tns:RBRM09M12-13" minOccurs="0"></element>
			<element name="RBRM15" type="tns:RBRM15" minOccurs="0"></element>
			<element name="RBRM09M12-13M15" type="tns:RBRM09M12-13M15" minOccurs="0"></element>
			<element name="RBRM18" type="tns:RBRM18" minOccurs="0"></element>
			<element name="RBRY2-3" type="tns:RBRY2-3" minOccurs="0"></element>
			<element name="RBRY4-5" type="tns:RBRY4-5" minOccurs="0"></element>
			<element name="RBRY2-3Y4-5" type="tns:RBRY2-3Y4-5" minOccurs="0"></element>
			<element name="IMMUNIZATION" type="tns:IMMUNIZATION" minOccurs="0" maxOccurs="unbounded"></element>
    	</sequence>
    </complexType>

	<complexType name="RBRW01">
    	<sequence>
			<element name="VisitDate" type="date" minOccurs="0"></element>
			<element name="Height" type="tns:unsignedDecimal31" minOccurs="0"></element>
			<element name="Weight" type="tns:unsignedInt5" minOccurs="0"></element>
			<element name="HeadCirc" type="tns:unsignedDecimal21" minOccurs="0"></element>
			<element name="ParentalCaregiverConcerns" type="tns:string2500" minOccurs="0"></element>
			<element name="NutritionBreastfeeding" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="NutritionFormulaFeeding" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="NutritionStoolUrine" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="PhysicalExamSkin" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamFontanelles" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamEyes" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamEarsHearingScreening" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamHeart" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamUmbilicus" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamFemoralPulses" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamHips" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamMuscleTone" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamTesticles" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamMaleUrinary" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="ProblemsAndPlans" type="tns:ProblemsAndPlans" minOccurs="0" maxOccurs="unbounded"></element>
			<element name="ProblemsAndPlansOther" type="tns:string2500" minOccurs="0"></element>
			<element name="InvestigationsImmunizationPKUThyroid" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="InvestigationsImmunizationHemoglobinopathyScreen " type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="InvestigationsImmunizationUNHS" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="InvestigationsImmunizationHBsAgPosParentVaccine1" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="InvestigationsImmunizationRecordVaccines" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="Signature" type="tns:string250" minOccurs="0"></element>
    	</sequence>
    </complexType>

	<complexType name="RBRW02">
    	<sequence>
			<element name="VisitDate" type="date" minOccurs="0"></element>
			<element name="Height" type="tns:unsignedDecimal31" minOccurs="0"></element>
			<element name="Weight" type="tns:unsignedInt5" minOccurs="0"></element>
			<element name="HeadCirc" type="tns:unsignedDecimal21" minOccurs="0"></element>
			<element name="ParentalCaregiverConcerns" type="tns:string2500" minOccurs="0"></element>
			<element name="NutritionBreastfeeding" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="NutritionFormulaFeeding" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="NutritionStoolUrine" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="DevelopmentSucksWellOnNipple" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentNoParentCaregiverConcerns" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="PhysicalExamSkin" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamFontanelles" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamEyes" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamEarsHearingScreening" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamHeart" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamUmbilicus" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamFemoralPulses" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamHips" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamMuscleTone" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamTesticles" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamMaleUrinary" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="ProblemsAndPlans" type="tns:ProblemsAndPlans" minOccurs="0" maxOccurs="unbounded"></element>
			<element name="ProblemsAndPlansOther" type="tns:string2500" minOccurs="0"></element>
			<element name="InvestigationsImmunizationRecordVaccines" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="Signature" type="tns:string250" minOccurs="0"></element>
    	</sequence>
    </complexType>

	<complexType name="RBRM01">
    	<sequence>
			<element name="VisitDate" type="date" minOccurs="0"></element>
			<element name="Height" type="tns:unsignedDecimal31" minOccurs="0"></element>
			<element name="Weight" type="tns:unsignedInt5" minOccurs="0"></element>
			<element name="HeadCirc" type="tns:unsignedDecimal21" minOccurs="0"></element>
			<element name="ParentalCaregiverConcerns" type="tns:string2500" minOccurs="0"></element>
			<element name="NutritionBreastfeeding" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="NutritionFormulaFeeding" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="NutritionStoolUrine" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="DevelopmentFocusesGaze" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentStartlesLoudNoise" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentCalmsWhenComforted" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentSucksWellOnNipple" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentNoParentCaregiverConcerns" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="PhysicalExamSkin" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamFontanelles" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamEyes" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalCornealLightReflex" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamHearingScreening" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamHeart" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamHips" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamMuscleTone" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="ProblemsAndPlans" type="tns:ProblemsAndPlans" minOccurs="0" maxOccurs="unbounded"></element>
			<element name="ProblemsAndPlansOther" type="tns:string2500" minOccurs="0"></element>
			<element name="InvestigationsImmunizationHBsAgPosParentVaccine2" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="InvestigationsImmunizationRecordVaccines" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="Signature" type="tns:string250" minOccurs="0"></element>
    	</sequence>
    </complexType>

	<complexType name="RBRW01W02M01">
    	<sequence>
			<element name="EducationAdviceInjuryPrevCarSeat" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceInjuryPrevSleepRoomBed" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceInjuryPrevCribSafety" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceInjuryPrevFirearmSafety" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceInjuryPrevCarbonMonoxideSmokeDet" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceInjuryPrevHotWater" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceInjuryPrevChokingSafeToys" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceBehaviourSleepingCrying" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceBehaviourSoothabilityResponsiveness " type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceBehaviourHighRiskInfants" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceBehaviourParentingBonding " type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceBehaviourParentalFatigue" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceBehaviourFamilyConflicts" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceBehaviourSiblings" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceOtherSecondHandSmoke" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceOtherNoOTCCoughColdMeds" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceOtherComplementaryAlternMedicine" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceOtherCounselPacifierUse" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceOtherTempControl" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceOtherSunExposure" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceOtherFeverAdvice" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
    	</sequence>
    </complexType>

	<complexType name="RBRM02">
    	<sequence>
			<element name="VisitDate" type="date" minOccurs="0"></element>
			<element name="Height" type="tns:unsignedDecimal31" minOccurs="0"></element>
			<element name="Weight" type="tns:unsignedInt5" minOccurs="0"></element>
			<element name="HeadCirc" type="tns:unsignedDecimal21" minOccurs="0"></element>
			<element name="ParentalCaregiverConcerns" type="tns:string2500" minOccurs="0"></element>
			<element name="NutritionBreastfeeding" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="NutritionFormulaFeeding" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="DevelopmentFollowsMovementWithEyes" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentCoos" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentLiftsHead" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentComforted" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentTwoOrMoreSucks" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentSmiles" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentNoParentCaregiverConcerns" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="PhysicalExamFontanelles" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamEyes" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalCornealLightReflex" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamHearingScreening" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamHeart" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamHips" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamMuscleTone" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="ProblemsAndPlans" type="tns:ProblemsAndPlans" minOccurs="0" maxOccurs="unbounded"></element>
			<element name="ProblemsAndPlansOther" type="tns:string2500" minOccurs="0"></element>
			<element name="InvestigationsImmunizationRecordVaccines" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="Signature" type="tns:string250" minOccurs="0"></element>
    	</sequence>
    </complexType>

	<complexType name="RBRM04">
    	<sequence>
			<element name="VisitDate" type="date" minOccurs="0"></element>
			<element name="Height" type="tns:unsignedDecimal31" minOccurs="0"></element>
			<element name="Weight" type="tns:unsignedInt5" minOccurs="0"></element>
			<element name="HeadCirc" type="tns:unsignedDecimal21" minOccurs="0"></element>
			<element name="ParentalCaregiverConcerns" type="tns:string2500" minOccurs="0"></element>
			<element name="NutritionBreastfeeding" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="NutritionFormulaFeeding" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="DevelopmentFollowsMovingToyPerson" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentRespondsWithExcitement" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentHoldsHead" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentHoldsObject" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentLaughsSmiles" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentNoParentCaregiverConcerns" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="PhysicalExamAnteriorFontanelles" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamEyes" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalCornealLightReflex" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamHearingScreening" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamHips" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamMuscleTone" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="ProblemsAndPlans" type="tns:string2500" minOccurs="0"></element>
			<element name="InvestigationsImmunizationRecordVaccines" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="Signature" type="tns:string250" minOccurs="0"></element>
    	</sequence>
    </complexType>

	<complexType name="RBRM06">
    	<sequence>
			<element name="VisitDate" type="date" minOccurs="0"></element>
			<element name="Height" type="tns:unsignedDecimal31" minOccurs="0"></element>
			<element name="Weight" type="tns:unsignedInt5" minOccurs="0"></element>
			<element name="HeadCirc" type="tns:unsignedDecimal21" minOccurs="0"></element>
			<element name="ParentalCaregiverConcerns" type="tns:string2500" minOccurs="0"></element>
			<element name="NutritionBreastfeeding" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="NutritionFormulaFeeding" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="NutritionNoBottlesInBed" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="NutritionAvoidSweetJuicesLiquids" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="NutritionIronContainingFoods" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="NutritionFruitsAndVegetables" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="NutritionNoEggWhiteNutHoney" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="NutritionChokingSafeFood" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="DevelopmentTurnsHead" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentMakesSounds" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentVocalizes" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentRolls" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentSits" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentReaches" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentNoParentCaregiverConcerns" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="PhysicalExamAnteriorFontanelles" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamEyes" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalCornealLightReflex" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamHearingScreening" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamHips" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamMuscleTone" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="ProblemsAndPlans" type="tns:ProblemsAndPlans" minOccurs="0" maxOccurs="unbounded"></element>
			<element name="ProblemsAndPlansOther" type="tns:string2500" minOccurs="0"></element>
			<element name="InvestigationsImmunizationInquireTBRiskFactors" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="InvestigationsImmunizationHBsAgPosParentVaccine3" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="InvestigationsImmunizationRecordVaccines" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="Signature" type="tns:string250" minOccurs="0"></element>
    	</sequence>
    </complexType>

	<complexType name="RBRM02M04M06">
    	<sequence>
			<element name="EducationAdviceInjuryPrevCarSeat" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceInjuryPrevSleepRoomBed" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceInjuryPrevPoisons" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceInjuryPrevFirearmSafety" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceInjuryPrevElectricPlugsCords" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceInjuryPrevCarbonMonoxideSmokeDet" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceInjuryPrevHotWaterBathSafety" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceInjuryPrevFalls " type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceInjuryPrevChokingSafeToys" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceBehaviourSleepingCryingNightWaking" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceBehaviourSoothabilityResponsiveness " type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceBehaviourHighRiskInfants" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceBehaviourSiblings" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceBehaviourParentingBonding " type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceBehaviourParentalFatigue" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceBehaviourFamilyConflicts" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceBehaviourChildCareReturnToWork" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceOtherSecondHandSmoke" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceOtherTeethingDentalCleaningFluoride" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceOtherNoOTCCoughColdMeds" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceOtherFeverAdvice" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceOtherTempControl" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceOtherComplementaryAlternMedicine" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceOtherEncourageReading" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceOtherSunExposure" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceOtherPesticideExposure" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceOtherPacifierUse" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
    	</sequence>
    </complexType>

	<complexType name="RBRM09">
    	<sequence>
			<element name="VisitDate" type="date" minOccurs="0"></element>
			<element name="Height" type="tns:unsignedDecimal31" minOccurs="0"></element>
			<element name="Weight" type="tns:unsignedInt5" minOccurs="0"></element>
			<element name="HeadCirc" type="tns:unsignedDecimal21" minOccurs="0"></element>
			<element name="ParentalCaregiverConcerns" type="tns:string2500" minOccurs="0"></element>
			<element name="NutritionBreastfeeding" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="NutritionFormulaFeeding" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="NutritionAvoidSweetJuicesLiquids" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="NutritionIronEncourageChangeBottleToCup" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="NutritionNoBottlesInBed" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="NutritionCerealMeatFruitsVegetables" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="NutritionCowMilkProducts" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="NutritionNoEggWhiteNutHoney" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="NutritionChokingSafeFood" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="DevelopmentLooksForHiddenObjects" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentBabbles" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentRespondsDiffToDiffPeople" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentMakesSoundsToGetAttention" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentSitsWithoutSupport" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentStandsWithSupport" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentOpposesThumbsaAndFingersWithGrasps" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentPlaySocialGames" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentCriesForAttention" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentNoParentCaregiverConcerns" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="PhysicalExamAnteriorFontanelles" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamEyes" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalCornealLightReflex" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamHearingScreening" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamHips" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="ProblemsAndPlans" type="tns:ProblemsAndPlans" minOccurs="0" maxOccurs="unbounded"></element>
			<element name="ProblemsAndPlansOther" type="tns:string2500" minOccurs="0"></element>
			<element name="InvestigationsImmunizationRecordVaccines" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="Signature" type="tns:string250" minOccurs="0"></element>
    	</sequence>
    </complexType>

	<complexType name="RBRM12-13">
    	<sequence>
			<element name="VisitDate" type="date" minOccurs="0"></element>
			<element name="Height" type="tns:unsignedDecimal31" minOccurs="0"></element>
			<element name="Weight" type="tns:unsignedInt5" minOccurs="0"></element>
			<element name="HeadCirc" type="tns:unsignedDecimal21" minOccurs="0"></element>
			<element name="ParentalCaregiverConcerns" type="tns:string2500" minOccurs="0"></element>
			<element name="NutritionBreastfeeding" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="NutritionHomogenizedMilk" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="NutritionAvoidSweetJuicesLiquids" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="NutritionIronPromoteCup" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="NutritionAppetiteReduced" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="NutritionChokingSafeFood" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="NutritionVegeterianDiets" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="DevelopmentRespondsToOwnName" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentUnderstandsSimpleRequests" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentMakesOneConsonantVowelComb" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentSaysThreeWords" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentCrawls" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentPullsToStandWalks" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentShowsDistressWhenSeparated" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentFollowsYourGazeToReferenceObjects" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentNoParentCaregiverConcerns" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="PhysicalExamAnteriorFontanelles" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamEyes" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalCornealLightReflex" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamHearingScreening" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamSnoringTonsil" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamTeeth" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamHips" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="ProblemsAndPlans" type="tns:ProblemsAndPlans" minOccurs="0" maxOccurs="unbounded"></element>
			<element name="ProblemsAndPlansOther" type="tns:string2500" minOccurs="0"></element>
			<element name="InvestigationsImmunizationRecordVaccines" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="Signature" type="tns:string250" minOccurs="0"></element>
    	</sequence>
    </complexType>

	<complexType name="RBRM09M12-13">
    	<sequence>
			<element name="InvestigationsImmunizationHBsAgPosMotherCheckAntibodies" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="InvestigationsImmunizationHBsAgPosParentVaccine3" type="tns:YesNoUnknown" minOccurs="0"></element>
    	</sequence>
    </complexType>

	<complexType name="RBRM15">
    	<sequence>
			<element name="VisitDate" type="date" minOccurs="0"></element>
			<element name="Height" type="tns:unsignedDecimal31" minOccurs="0"></element>
			<element name="Weight" type="tns:unsignedInt5" minOccurs="0"></element>
			<element name="HeadCirc" type="tns:unsignedDecimal21" minOccurs="0"></element>
			<element name="ParentalCaregiverConcerns" type="tns:string2500" minOccurs="0"></element>
			<element name="NutritionBreastfeeding" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="NutritionHomogenizedMilk" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="NutritionAvoidSweetJuicesLiquids" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="NutritionIronPromoteCup" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="NutritionChokingSafeFood" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="NutritionVegeterianDiets" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="DevelopmentSaysFiveWords" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentPicksUpEatsFingerFood" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentWalkSidewaysHolding" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentShowsFearStangePeople" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentCrawlsFewStairsSteps" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentTriesSquat" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentNoParentCaregiverConcerns" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="PhysicalExamAnteriorFontanelles" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamEyes" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalCornealLightReflex" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamHearingScreening" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamSnoringTonsil" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamTeeth" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamHips" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="ProblemsAndPlans" type="tns:ProblemsAndPlans" minOccurs="0" maxOccurs="unbounded"></element>
			<element name="ProblemsAndPlansOther" type="tns:string2500" minOccurs="0"></element>
			<element name="InvestigationsImmunizationRecordVaccines" type="tns:YesNoUnknown" minOccurs="0"></element>
			<element name="Signature" type="tns:string250" minOccurs="0"></element>
    	</sequence>
    </complexType>

	<complexType name="RBRM09M12-13M15">
    	<sequence>
			<element name="EducationAdviceInjuryPrevCarSeat" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceInjuryPrevPoisons" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceInjuryPrevFirearmSafety" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceInjuryPrevCarbonMonoxideSmokeDet" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceInjuryPrevHotWaterBathSafety" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceInjuryPrevElectricPlugsCords" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceInjuryPrevFalls " type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceInjuryPrevChokingSafeToys" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceBehaviourSleepingCryingNightWaking" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceBehaviourSoothabilityResponsiveness " type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceBehaviourHighRiskInfants" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceBehaviourSiblings" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceBehaviourParentingBonding " type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceBehaviourParentalFatigue" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceBehaviourFamilyConflicts" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceBehaviourChildCareReturnToWork" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceOtherSecondHandSmoke" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceOtherTeethingDentalCleaningFluoride" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceOtherComplementaryAlternMedicine" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceOtherNoOTCCoughColdMeds" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceOtherFeverAdvice" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceOtherActiveHealthyLiving" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceOtherEncourageReading" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceOtherPacifierUse" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceOtherFootwear" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceOtherSunExposure" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceOtherSerumLead" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceOtherPesticideExposure" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
    	</sequence>
    </complexType>

	<complexType name="RBRM18">
    	<sequence>
			<element name="VisitDate" type="date" minOccurs="0"></element>
			<element name="Height" type="tns:unsignedDecimal31" minOccurs="0"></element>
			<element name="Weight" type="tns:unsignedInt5" minOccurs="0"></element>
			<element name="HeadCirc" type="tns:unsignedDecimal21" minOccurs="0"></element>
			<element name="ParentalCaregiverConcerns" type="tns:string2500" minOccurs="0"></element>


			<element name="NutritionBreastfeeding" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="NutritionHomogenizedMilk" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="NutritionAvoidSweetJuicesLiquids" type="tns:YesNoNotDiscussed" minOccurs="0"></element>
			<element name="NutritionNoBottles" type="tns:YesNoNotDiscussed" minOccurs="0"></element>


			<element name="EducationAdviceInjuryPrevCarSeat" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceInjuryPrevBathSafety" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceInjuryPrevChokingSafeToys" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceInjuryPrevFalls" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceInjuryPrevWeanFromPacifier" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>


			<element name="EducationAdviceBehaviourDisciplineParentingSkills" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceBehaviourParentChildInteraction" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceBehaviourHealthySleepHabits" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>


			<element name="EducationAdviceFamilyParentalFatigueStress" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceFamilyHighRiskChildren" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceFamilyHealthyActiveLiving" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceFamilyEncourageReading" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceFamilySocializing" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>


			<element name="EducationAdviceEnvironmentalHealthSecondHandSmoke" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceEnvironmentalHealthSerumLead" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceEnvironmentalHealthSunExposure" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceEnvironmentalHealthPesticideExposure" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>

			<element name="EducationAdviceOtherDentalCare" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceOtherToiletLearning" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>

			<element name="DevelopmentNDDSNotAttained18M01" type="tns:NDDSQuestionNum" minOccurs="0"></element>
			<element name="DevelopmentNDDSNotAttained18M02" type="tns:NDDSQuestionNum" minOccurs="0"></element>
			<element name="DevelopmentNDDSNotAttained18M03" type="tns:NDDSQuestionNum" minOccurs="0"></element>
			<element name="DevelopmentNDDSNotAttained18M04" type="tns:NDDSQuestionNum" minOccurs="0"></element>
			<element name="DevelopmentNDDSNotAttained18M05" type="tns:NDDSQuestionNum" minOccurs="0"></element>
			<element name="DevelopmentNDDSNotAttained18M06" type="tns:NDDSQuestionNum" minOccurs="0"></element>
			<element name="DevelopmentNDDSNotAttained18M07" type="tns:NDDSQuestionNum" minOccurs="0"></element>
			<element name="DevelopmentNDDSNotAttained18M08" type="tns:NDDSQuestionNum" minOccurs="0"></element>
			<element name="DevelopmentNDDSNotAttained18M09" type="tns:NDDSQuestionNum" minOccurs="0"></element>
			<element name="DevelopmentNDDSNotAttained18M10" type="tns:NDDSQuestionNum" minOccurs="0"></element>
			<element name="DevelopmentNDDSNotAttained18M11" type="tns:NDDSQuestionNum" minOccurs="0"></element>
			<element name="DevelopmentNDDSNotAttained18M12" type="tns:NDDSQuestionNum" minOccurs="0"></element>
			<element name="DevelopmentNDDSNotAttained18M13" type="tns:NDDSQuestionNum" minOccurs="0"></element>
			<element name="DevelopmentNDDSNotAttained18M14" type="tns:NDDSQuestionNum" minOccurs="0"></element>
			<element name="DevelopmentNDDSNotAttained18M15" type="tns:NDDSQuestionNum" minOccurs="0"></element>
			<element name="DevelopmentNDDSNotAttained18M16" type="tns:NDDSQuestionNum" minOccurs="0"></element>
			<element name="DevelopmentNDDSNotAttained18M17" type="tns:NDDSQuestionNum" minOccurs="0"></element>
			<element name="DevelopmentSocialEmotionalManagableBehaviour" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentSocialEmotionalInterestedInChildren" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentSocialEmotionalEasyToSoothe" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentSocialEmotionalComesForComfort" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentCommSkillsPointsToBodyParts" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentCommSkillsGetsAttention" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentCommSkillsRespondWhenCalled" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentCommSkillsPointsToWants" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentCommSkillsLooksForToys" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentCommSkillsImitatesSpeech" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentCommSkillsSays20Words" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentCommSkillsProduces4Cons" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentMotorSkillsWalksAlone" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="DevelopmentMotorSkillsFeedsSelf" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="AdaptiveSkillsRemovesHat" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="AdaptiveMotorSkillsNoParentConcerns" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="PhysicalExamAnteriorFontanelles" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamEyes" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalCornealLightReflex" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamHearingScreening" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamSnoringTonsil" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamTeeth" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="ProblemsAndPlans" type="tns:ProblemsAndPlans" minOccurs="0" maxOccurs="unbounded"></element>
			<element name="ProblemsAndPlansOther" type="tns:string2500" minOccurs="0"></element>
			<element name="Signature18M" type="tns:string250" minOccurs="0"></element>
			<element name="NeedForAddAssessmentNo" type="tns:YesNoUnknown" minOccurs="0"></element>
    	</sequence>
    </complexType>

	<complexType name="RBRY2-3">
    	<sequence>
			<element name="VisitDate" type="date" minOccurs="0"></element>
			<element name="Height" type="tns:unsignedDecimal31" minOccurs="0"></element>
			<element name="Weight" type="tns:unsignedInt5" minOccurs="0"></element>
			<element name="HeadCirc" type="tns:unsignedDecimal21" minOccurs="0"></element>
			<element name="ParentalCaregiverConcerns" type="tns:string2500" minOccurs="0"></element>
			<element name="NutritionMilk" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="NutritionTransitionToLowerFatDiet" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="NutritionVegeterianDiets" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="NutritionCanadaFoodGuide" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="Development2YCombines2Words" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="Development2YOneTwoStepDirection" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="Development2YWalksBackward" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="Development2YTriesToRun" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="Development2YPutsObjectsSmallCont" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="Development2YUseToysForPretendPlay" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="Development2YDevelopNewSkills" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="Development2YNoParentCaregiverConcerns" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="Development3YTwoThreeStepDirections" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="Development3YSentences5Words" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="Development3YWalksUpStairs" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="Development3YTwistsLids" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="Development3YSharing" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="Development3YMakeBelieveGames" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="Development3YTurnsPages" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="Development3YMusic" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="Development3YNoParentCaregiverConcerns" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="PhysicalExamBloodPressure" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamEyes" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalCornealLightReflex" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamHearingScreening" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamSnoringTonsil" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamTeeth" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="ProblemsAndPlans" type="tns:ProblemsAndPlans" minOccurs="0" maxOccurs="unbounded"></element>
			<element name="ProblemsAndPlansOther" type="tns:string2500" minOccurs="0"></element>
			<element name="Signature2T3Y" type="tns:string250" minOccurs="0"></element>
    	</sequence>
    </complexType>

	<complexType name="RBRY2-3Y4-5">
    	<sequence>
			<element name="EducationAdviceInjuryPrevCarSeat" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceInjuryPrevBikeHelmet" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceInjuryPrevFirearmSafety" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceInjuryPrevCarbonMonoxideSmokeDet" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceInjuryPrevMatches" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceInjuryPrevWaterSafety" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceBehaviourParentChildInteraction" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceBehaviourDisciplineParentingSkills" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceBehaviourHighRiskChildren" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceBehaviourParentalFatigue" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceBehaviourFamilyConflict" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceBehaviourSiblings" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceBehaviourChildCare" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceFamilySecondHandSmoke" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceFamilyDentalCare" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceFamilyNoPacifiers" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceFamilyComplementaryAltMedicine" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceFamilyToiletTraining" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceFamilyNoOTCCoughColdMeds" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceFamilyActiveLiving" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceFamilySocializing" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceFamilyEncourageReading" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceOtherSunExposure" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceOtherPesticideExposure" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="EducationAdviceOtherSerumLead" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
    	</sequence>
    </complexType>

	<complexType name="RBRY4-5">
    	<sequence>
			<element name="VisitDate" type="date" minOccurs="0"></element>
			<element name="Height" type="tns:unsignedDecimal31" minOccurs="0"></element>
			<element name="Weight" type="tns:unsignedInt5" minOccurs="0"></element>
			<element name="HeadCirc" type="tns:unsignedDecimal21" minOccurs="0"></element>
			<element name="ParentalCaregiverConcerns" type="tns:string2500" minOccurs="0"></element>
			<element name="NutritionMilk" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="NutritionVegeterianDiets" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="NutritionCanadaFoodGuide" type="tns:DiscussedConcernNotDiscussed" minOccurs="0"></element>
			<element name="Development4YUnderstandsThreePartDirections" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="Development4YAskAnswerLotsOfQuestions" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="Development4YWalksUpDownStairs" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="Development4YUndoesButtonsZippers" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="Development4YTriesToComfort" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="Development4YNoParentCaregiverConcerns" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="Development5YCountsOutLoudOnFingers" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="Development5YSpeaksClearly" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="Development5YThrowsAndCatchesBall" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="Development5YHops" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="Development5YDressesUndresses" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="Development5YCooperates" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="Development5YRetells" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="Development5YSeparates" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="Development5YNoParentCaregiverConcerns" type="tns:AttainedNotAttainedUnknown" minOccurs="0"></element>
			<element name="PhysicalExamBloodPressure" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamEyes" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalCornealLightReflex" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamHearingScreening" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamSnoringTonsil" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="PhysicalExamTeeth" type="tns:NormalAbnormalUnknown" minOccurs="0"></element>
			<element name="ProblemsAndPlans" type="tns:ProblemsAndPlans" minOccurs="0" maxOccurs="unbounded"></element>
			<element name="ProblemsAndPlansOther" type="tns:string2500" minOccurs="0"></element>
			<element name="Signature2T3Y" type="tns:string250" minOccurs="0"></element>
    	</sequence>
    </complexType>

	<complexType name="IMMUNIZATION">
    	<sequence>
			<element name="DateGiven" type="date"></element>
			<element name="VaccineName" type="tns:Vaccine"></element>
			<element name="VaccineNameOther" type="tns:string250" minOccurs="0"></element>
			<element name="DoseNum" minOccurs="0">
				<simpleType>
					<restriction base="unsignedInt">
						<minInclusive value="1"/>
						<maxInclusive value="4"/>
					</restriction>
				</simpleType>
			</element>
			<element name="InjectionSite" type="tns:string50" minOccurs="0"></element>
			<element name="LotNumber" type="tns:string50" minOccurs="0"></element>
			<element name="ExpiryDate" type="date" minOccurs="0"></element>
			<element name="Initials" type="tns:string50" minOccurs="0"></element>
			<element name="Comments" type="tns:string250" minOccurs="0"></element>
    	</sequence>
    </complexType>

</schema>
