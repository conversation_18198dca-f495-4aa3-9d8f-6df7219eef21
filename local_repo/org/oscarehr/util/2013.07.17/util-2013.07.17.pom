<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>org.oscarehr</groupId>
	<artifactId>util</artifactId>
	<packaging>jar</packaging>
	<version>2013.07.17</version>
	<name>util</name>
	<url>http://www.oscarcanada.org</url>

	<dependencies>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.4</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.puppycrawl.tools</groupId>
			<artifactId>checkstyle</artifactId>
			<version>5.3</version>
			<exclusions>
      				<exclusion>
        				<groupId>com.sun</groupId>
        				<artifactId>tools</artifactId>
      				</exclusion>
    			</exclusions>
		</dependency>
		<dependency>
			<groupId>log4j</groupId>
			<artifactId>log4j</artifactId>
			<version>1.2.16</version>
		</dependency>
		<dependency>
			<groupId>commons-lang</groupId>
			<artifactId>commons-lang</artifactId>
			<version>2.5</version>
		</dependency>
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>2.0.1</version>
		</dependency>
		<dependency>
			<groupId>commons-codec</groupId>
			<artifactId>commons-codec</artifactId>
			<!-- don't use 1.4 major CRLF bug on encode, fixed in 1.5 -->
			<version>1.3</version>
		</dependency>
		<dependency>
			<groupId>org.apache.tomcat</groupId>
			<artifactId>tomcat-servlet-api</artifactId>
			<version>7.0.20</version>
			<scope>provided</scope>
		</dependency>
		
		<!-- velocity -->
		<dependency>
			<groupId>org.apache.velocity</groupId>
			<artifactId>velocity</artifactId>
			<version>1.7</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.velocity</groupId>
			<artifactId>velocity-tools</artifactId>
			<version>2.0</version>
			<scope>provided</scope>
		</dependency>	
		
		<!-- ehcache -->
		<dependency>
			<groupId>net.sf.ehcache</groupId>
			<artifactId>ehcache-core</artifactId>
			<version>2.6.2</version>
			<scope>provided</scope>
		</dependency>
		
		<!-- JSF -->
		<dependency>
			<groupId>org.apache.myfaces.core</groupId>
			<artifactId>myfaces-api</artifactId>
			<version>2.1.6</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>javax.el</groupId>
			<artifactId>javax.el-api</artifactId>
			<version>2.2.1</version>
			<type>jar</type>
			<scope>provided</scope>
		</dependency>
				
		<!-- CXF -->
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>apache-cxf</artifactId>
			<version>2.7.4</version>
			<type>pom</type>
			<scope>provided</scope>
			<exclusions>
				<exclusion>
					<groupId>org.apache.geronimo.specs</groupId>
					<artifactId>geronimo-servlet_2.5_spec</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.geronimo.specs</groupId>
					<artifactId>geronimo-servlet_3.0_spec</artifactId>
				</exclusion>
			</exclusions>
		</dependency>		
		
		<!-- derby -->
		<dependency>
			<groupId>org.apache.derby</groupId>
			<artifactId>derby</artifactId>
			<version>10.10.1.1</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.derby</groupId>
			<artifactId>derbynet</artifactId>
			<version>10.10.1.1</version>
			<scope>provided</scope>
		</dependency>

		<!-- hbase / hadoop -->
		<dependency>
			<groupId>org.apache.hadoop</groupId>
			<artifactId>hadoop-client</artifactId>
			<version>1.0.4</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.hbase</groupId>
			<artifactId>hbase</artifactId>
			<version>0.94.5</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.zookeeper</groupId>
			<artifactId>zookeeper</artifactId>
			<version>3.4.5</version>
			<scope>provided</scope>
		</dependency>
		
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.0</version>
				<configuration>
					<source>1.6</source>
					<target>1.6</target>
				</configuration>
			</plugin>
			
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-dependency-plugin</artifactId>
				<executions>
					<execution>
						<phase>package</phase>
						<goals>
							<goal>copy-dependencies</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			
			<plugin>
				<groupId>com.mycila.maven-license-plugin</groupId>
				<artifactId>maven-license-plugin</artifactId>
				<version>1.10.b1</version>
				<configuration>
					<header>utils/headers/mcmaster_2013.txt</header>
					<validHeaders>
						<validHeader>utils/headers/mcmaster_2012.txt</validHeader>
					</validHeaders>
					<includes>
						<include>src/main/java/**/*.java</include>
					</includes>
					<strictCheck>true</strictCheck>
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>check</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-javadoc-plugin</artifactId>
				<version>2.9</version>
				<executions>
					<execution>
						<phase>verify</phase>
						<goals>
							<goal>javadoc</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
