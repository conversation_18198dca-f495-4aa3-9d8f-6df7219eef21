<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>org.oscarehr</groupId>
	<artifactId>myoscar_client_utils</artifactId>
	<packaging>jar</packaging>
	<version>2013.07.22.TLS</version>
	<name>myoscar_client_utils</name>
	<url>http://www.oscarcanada.org</url>

	<properties>
	    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>

	<repositories>
		<repository>
			<id>oscar_repo</id>
			<url>http://oscarmcmaster.sourceforge.net/m2</url>
		</repository>
	</repositories>

	<pluginRepositories>
       	<pluginRepository>
			<id>oscar_repo</id>
			<url>http://oscarmcmaster.sourceforge.net/m2</url>
       	</pluginRepository>
	</pluginRepositories>
	
	<dependencies>
		<dependency>
			<groupId>log4j</groupId>
			<artifactId>log4j</artifactId>
			<version>1.2.14</version>
		</dependency>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.4</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.tomcat</groupId>
			<artifactId>tomcat-servlet-api</artifactId>
			<version>7.0.34</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
			<version>4.2.3</version>
		</dependency>
		
		<!-- JSF -->
		<dependency>
			<groupId>org.apache.myfaces.core</groupId>
			<artifactId>myfaces-api</artifactId>
			<version>2.1.6</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.myfaces.core</groupId>
			<artifactId>myfaces-impl</artifactId>
			<version>2.1.6</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>jstl</artifactId>
			<version>1.2</version>
			<scope>provided</scope>
		</dependency>		 
		<dependency>
			<groupId>javax.el</groupId>
			<artifactId>javax.el-api</artifactId>
			<version>2.2.1</version>
			<type>jar</type>
			<scope>provided</scope>
		</dependency>
		
		<!-- MyOscarServer client Stubs -->
		<dependency>
			<groupId>org.oscarehr.myoscar_server</groupId>
			<artifactId>myoscar_server_client_stubs</artifactId>
			<version>2013.07.22</version>
		</dependency>		
		<dependency>
			<groupId>org.oscarehr</groupId>
			<artifactId>myoscar_client_server_commons</artifactId>
			<version>2013.07.18</version>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.0</version>
				<configuration>
					<source>1.7</source>
					<target>1.7</target>
				</configuration>
			</plugin>
 
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-checkstyle-plugin</artifactId>
				<version>2.6</version>
				<configuration>
					<configLocation>checkstyle.xml</configLocation>
					<failsOnError>true</failsOnError>
					<consoleOutput>true</consoleOutput>
					<linkXRef>false</linkXRef>
				</configuration>
				<executions>
					<execution>
						<phase>process-sources</phase>
						<goals>
							<goal>checkstyle</goal>
						</goals>
					</execution>
				</executions>
			</plugin>



			<plugin>
				<groupId>com.mycila.maven-license-plugin</groupId>
				<artifactId>maven-license-plugin</artifactId>
				<version>1.10.b1</version>
				<configuration>
					<header>utils/headers/mcmaster_2013.txt</header>
					<validHeaders>
						<validHeader>utils/headers/mcmaster_2012.txt</validHeader>
					</validHeaders>
					<includes>
						<include>src/main/java/**/*.java</include>
						<include>src/test/java/**/*.java</include>
					</includes>
					<excludes>
						<exclude>src/**/*.txt</exclude>
						<exclude>src/**/*.js</exclude>
						<exclude>**/.gitignore</exclude>
					</excludes>
					<strictCheck>true</strictCheck>
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>check</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-dependency-plugin</artifactId>
				<version>2.7</version>
				<executions>
					<execution>
						<phase>package</phase>
						<goals>
							<goal>copy-dependencies</goal>
						</goals>
					</execution>
				</executions>
			</plugin>			
		</plugins>
	</build>
</project>
