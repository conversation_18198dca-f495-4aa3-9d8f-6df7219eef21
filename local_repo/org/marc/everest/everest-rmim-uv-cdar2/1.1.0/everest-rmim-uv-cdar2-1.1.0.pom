<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>org.marc.everest</groupId>
	<artifactId>everest-rmim-uv-cdar2</artifactId>
	<version>1.1.0</version>
	<name>org.marc.everest.rmim.uv.cdar2</name>
	<description>HL7 Version 3 Clinical Document Architecture Release 2 Message Structures. Autogenerated by GPMR 1.1.4909.19766</description>
	<properties>
	    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	  </properties>

	<dependencies>
		<dependency>
			<groupId>org.marc.everest</groupId>
			<artifactId>everest-core</artifactId>
			<version>1.1.0</version>
		</dependency>
	</dependencies>
	<organization>
		<name>Mohawk College of Applied Arts and Technology. Based on models Copyright (C) Health Level 7 International</name>
	</organization>
	<scm>
		<url>https://fisheye.marc-hi.ca/svn/jEverest/branches/1.1.0/org.marc.everest.rmim.uv.cdar2</url>
	</scm>
	<repositories>
		<repository>
			<id>marc-te-main</id>
			<url>http://te.marc-hi.ca/mvn</url>
		</repository>
	</repositories>
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>1.6</source>
					<target>1.6</target>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-javadoc-plugin</artifactId>
				<configuration>
				    <charset>utf8</charset>
					<links>
						<link>http://docs.oracle.com/javase/6/docs/api/</link>
						<link>http://te.marc-hi.ca/library/en/jdoc/jev/</link>
					</links>
				</configuration>
			</plugin>	
		</plugins>
	</build>
	  <distributionManagement>
       		<site>
          <id>marc-te-main-doc-distro</id>
          <url>file://M:/org/marc/everest/everest-rmim-uv-cdar2</url>
      </site>
      <repository>
          <id>marc-te-main-distro</id>
          <url>file://M:/</url>
      </repository>

  </distributionManagement>
</project>