<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>org.marc.everest</groupId>
	<artifactId>everest-core</artifactId>
	<version>1.1.0</version>
	<name>org.marc.everest</name>
	<description>MARC-HI Everest Framework 1.1</description>
	<issueManagement>
	    <url>http://te.marc-hi.ca/issue/default.aspx?project=6e2de429beae44359a1b23975a90a71b</url>
    </issueManagement>
	<developers>
	    <developer>
	        <name><PERSON></name>
	        <organization>Mohawk College of Applied Arts and Technology</organization>
	        <email>justin_dot_fyfe1_at_mohawkcollege_dot_ca</email>
	    </developer>
	</developers>
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>1.6</source>
					<target>1.6</target>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-javadoc-plugin</artifactId>
				<configuration>
					<links>
						<link>http://docs.oracle.com/javase/6/docs/api/</link>
					</links>
				</configuration>
			</plugin>
			
		</plugins>
	</build>
	<dependencies>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.11</version>
			<scope>test</scope>
		</dependency>
	</dependencies>
	
	<scm>

		<url>https://fisheye.marc-hi.ca/svn/jEverest/tags/1.1.0/org.marc.everest</url>
	</scm>
	<repositories>
		<repository>
			<id>marc-te-main</id>
			<url>http://te.marc-hi.ca/mvn</url>
		</repository>
	</repositories>
	<organization>
		<name>Mohawk College of Applied Arts and Technology</name>
		<url>http://everest.marc-hi.ca</url>
	</organization>
  <distributionManagement>
      <site>
          <id>marc-te-main-doc-distro</id>
          <url>file://M:/org/marc/everest/everest-core</url>
      </site>
      <repository>
          <id>marc-te-main-distro</id>
          <url>file://M:/</url>
      </repository>
      
  </distributionManagement>
	
</project>