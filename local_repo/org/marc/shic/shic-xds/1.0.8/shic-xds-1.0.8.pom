<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>org.marc.shic</groupId>
    <artifactId>shic-xds</artifactId>
    <version>1.0.8</version>
    <packaging>jar</packaging>
    <name>org.marc.shic.xds</name>
    <description>SHIC - Cross-Enterprise Document Sharing (XDS) - A library that handles all IHE XDS Profile interactions.</description>
    <organization>
        <name>Mohawk College of Applied Arts and Technology</name>
        <url>http://te.marc-hi.ca</url>
    </organization>
    <licenses>
        <license>
            <name>Apache License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>
    <scm>
        <connection>scm:git:http://fisheye.marc-hi.ca/git/SharedHealthComponents.git</connection>
        <developerConnection>scm:git:http://fisheye.marc-hi.ca/git/SharedHealthComponents.git</developerConnection>
        <tag>${project.version}</tag>
    </scm>
    <issueManagement>
        <system>jira</system>
        <url>http://jira.marc-hi.ca/browse/SHC</url>
    </issueManagement>
    <developers>
        <developer>
            <name>Justin Fyfe</name>
            <organization>Mohawk College of Applied Arts and Technology</organization>
            <email>justin_dot_fyfe1_at_mohawkcollege_dot_ca</email>
        </developer>
        <developer>
            <name>Paul Brown</name>
            <organization>Mohawk College of Applied Arts and Technology</organization>
            <email>paul_dot_brown9_at_mohawkcollege_dot_ca</email>
        </developer>
        <developer>
            <name>Garrett Tyler</name>
            <organization>Mohawk College of Applied Arts and Technology</organization>
            <email>garrett_dot_tyler_at_mohawkcollege_dot_ca</email>
        </developer>
        <developer>
            <name>Mohamed Ibrahim</name>
            <organization>Mohawk College of Applied Arts and Technology</organization>
            <email>mohamed_dot_ibrahim1_at_mohawkcollege_dot_ca</email>
        </developer>
    </developers>
    <distributionManagement>
        <site>
            <id>marc-te-main-shic-doc-distro</id>
            <url>file://O:/org/marc/shic/shic-xds</url>
        </site>
        <repository>
            <id>marc-te-main-distro</id>
            <url>file://O:/</url>
        </repository>
    </distributionManagement>
    <build>
    	<resources>
            <resource>
                <targetPath>META-INF</targetPath>
                <directory>src</directory>
                <includes>
                    <include>jax-ws-catalog.xml</include>
                    <include>wsdl/**</include>
                    <include>schema/**</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>2.3.2</version>
                <configuration>
                    <source>1.6</source>
                    <target>1.6</target>
                    <compilerArguments>
                        <endorseddirs>${basedir}/src/main/resources/endorsed</endorseddirs>
                    </compilerArguments>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jvnet.jax-ws-commons</groupId>
                <artifactId>jaxws-maven-plugin</artifactId>
                <version>2.2.1</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>wsimport</goal>
                        </goals>
                        <configuration>
                            <wsdlFiles>
                                <wsdlFile>XDS.b_DocumentRegistry.wsdl</wsdlFile>
                            </wsdlFiles>
                            <packageName>org.marc.shic.xds.registry</packageName>
                            <wsdlDirectory>src/main/resources/ihe/wsdl</wsdlDirectory>
                            <wsdlLocation>META-INF/wsdl/XDS.b_DocumentRegistry.wsdl</wsdlLocation>
                        </configuration>
                        <id>wsimport-generate-XDS.b_DocumentRegistry</id>
                        <phase>generate-sources</phase>
                    </execution>
                    <execution>
                        <goals>
                            <goal>wsimport</goal>
                        </goals>
                        <configuration>
                            <wsdlFiles>
                                <wsdlFile>XDS.b_DocumentRepository.wsdl</wsdlFile>
                            </wsdlFiles>
                            <packageName>org.marc.shic.xds.repository</packageName>
                            <wsdlDirectory>src/main/resources/ihe/wsdl</wsdlDirectory>
                            <wsdlLocation>META-INF/wsdl/XDS.b_DocumentRepository.wsdl</wsdlLocation>
                        </configuration>
                        <id>wsimport-generate-XDS.b_DocumentRepository</id>
                        <phase>generate-sources</phase>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>javax.xml</groupId>
                        <artifactId>webservices-api</artifactId>
                        <version>2.0</version>
                    </dependency>
                </dependencies>
                <configuration>
                    <sourceDestDir>${project.build.directory}/generated-sources/jaxws-wsimport</sourceDestDir>
                    <xnocompile>true</xnocompile>
                    <verbose>true</verbose>
                    <extension>true</extension>
                    <catalog>${basedir}/src/jax-ws-catalog.xml</catalog>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>2.0.2</version>
                <configuration>
                    <webResources>
                        <resource>
                            <directory>src</directory>
                            <targetPath>WEB-INF</targetPath>
                            <includes>
                                <include>jax-ws-catalog.xml</include>
                                <include>wsdl/**</include>
                                <include>schema/**</include>
                            </includes>
                        </resource>
                    </webResources>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <repositories>
        <repository>
            <id>marc-te-main</id>
            <name>MARC-HI Technology Exchange Private Maven Repository</name>
            <url>http://te.marc-hi.ca/mvn</url>
        </repository>
		<repository>
            <id>shibboleth-opensaml</id>
            <name>Shibboleth</name>
            <url>https://build.shibboleth.net/nexus/content/repositories/releases</url>
        </repository>
    </repositories>
    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.10</version>
            <scope>test</scope>
            <type>jar</type>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>shic-pix</artifactId>
            <version>${project.version}</version>
        </dependency>
		<dependency>
            <groupId>org.opensaml</groupId>
            <artifactId>opensaml</artifactId>
            <version>2.6.1</version>
        </dependency>
        <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-simple</artifactId>
                <version>1.7.6</version>
        </dependency>
        <dependency>
            <groupId>org.codehaus.woodstox</groupId>
            <artifactId>wstx-asl</artifactId>
            <version>4.0.6</version>
        </dependency>
    </dependencies>
</project>
