<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>org.marc.shic</groupId>
    <artifactId>shic-core</artifactId>
    <version>1.0.8</version>
    <packaging>jar</packaging>
    <name>org.marc.shic</name>        
    <description>The MARC-HI Shared Health Integration Components (SHIC) Library - A general purpose library for the generation, parsing and exchange of IHE profile interactions and CDA.</description>
    <organization>
        <name>Mohawk College of Applied Arts and Technology</name>
        <url>http://te.marc-hi.ca</url>
    </organization>
    <licenses>
        <license>
            <name>Apache License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>
    <scm>
        <connection>scm:git:http://fisheye.marc-hi.ca/git/SharedHealthComponents.git</connection>
        <developerConnection>scm:git:http://fisheye.marc-hi.ca/git/SharedHealthComponents.git</developerConnection>
        <tag>${project.version}</tag>
    </scm>
    <issueManagement>
        <system>jira</system>
        <url>http://jira.marc-hi.ca/browse/SHC</url>
    </issueManagement>
    <developers>
        <developer>
            <name>Justin Fyfe</name>
            <organization>Mohawk College of Applied Arts and Technology</organization>
            <email>justin_dot_fyfe1_at_mohawkcollege_dot_ca</email>
        </developer>
        <developer>
            <name>Paul Brown</name>
            <organization>Mohawk College of Applied Arts and Technology</organization>
            <email>paul_dot_brown9_at_mohawkcollege_dot_ca</email>
        </developer>
        <developer>
            <name>Garrett Tyler</name>
            <organization>Mohawk College of Applied Arts and Technology</organization>
            <email>garrett_dot_tyler_at_mohawkcollege_dot_ca</email>
        </developer>
        <developer>
            <name>Mohamed Ibrahim</name>
            <organization>Mohawk College of Applied Arts and Technology</organization>
            <email>mohamed_dot_ibrahim1_at_mohawkcollege_dot_ca</email>
        </developer>
    </developers>
    <distributionManagement>
        <site>
            <id>marc-te-main-shic-doc-distro</id>
            <url>file://O:/org/marc/shic/shic-core</url>
        </site>
        <repository>
            <id>marc-te-main-distro</id>
            <url>file://O:/</url>
        </repository>
    </distributionManagement>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>2.3.2</version>
                <configuration>
                    <source>1.6</source>
                    <target>1.6</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <repositories>
        <repository>
            <id>marc-te-main</id>
            <name>MARC-HI Technology Exchange Private Maven Repository</name>
            <url>http://te.marc-hi.ca/mvn</url>
        </repository>
    </repositories>
    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.10</version>
            <scope>test</scope>
            <type>jar</type>
        </dependency>
        <dependency>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
            <version>1.2.14</version>
        </dependency>
        <dependency>
            <groupId>org.marc.everest</groupId>
            <artifactId>everest-core</artifactId>
            <version>1.1.0</version>
            <type>jar</type>
        </dependency>
        <dependency>
            <groupId>org.marc.everest</groupId>
            <artifactId>everest-rmim-uv-cdar2</artifactId>
            <version>1.1.0</version>
            <type>jar</type>
        </dependency>
        <dependency>
            <groupId>org.marc.everest</groupId>
            <artifactId>everest-formatters-xml-its1</artifactId>
            <version>1.1.0</version>
            <type>jar</type>
        </dependency>
        <dependency>
            <groupId>org.marc.everest</groupId>
            <artifactId>everest-formatters-xml-dt-r1</artifactId>
            <version>1.1.0</version>
            <type>jar</type>
        </dependency>
        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
            <version>2.0</version>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcpkix-jdk15on</artifactId>
            <version>1.51</version>
            <type>jar</type>
        </dependency>
        <dependency>
            <groupId>org.apache.cxf</groupId>
            <artifactId>apache-cxf</artifactId>
            <version>2.7.4</version>
            <type>pom</type>
        </dependency>
    </dependencies>
</project>
