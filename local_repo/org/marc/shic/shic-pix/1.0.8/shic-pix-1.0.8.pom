<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>org.marc.shic</groupId>
    <artifactId>shic-pix</artifactId>
    <version>1.0.8</version>
    <packaging>jar</packaging>
    <name>org.marc.shic.pix</name>
    <description>SHIC - Patient Identifier Cross-Referencing (PIX) - A library that handles all IHE PIX Profile interactions.</description>
    <organization>
        <name>Mohawk College of Applied Arts and Technology</name>
        <url>http://te.marc-hi.ca</url>
    </organization>
    <licenses>
        <license>
            <name>Apache License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>
    <scm>
        <connection>scm:git:http://fisheye.marc-hi.ca/git/SharedHealthComponents.git</connection>
        <developerConnection>scm:git:http://fisheye.marc-hi.ca/git/SharedHealthComponents.git</developerConnection>
        <tag>${project.version}</tag>
    </scm>
    <issueManagement>
        <system>jira</system>
        <url>http://jira.marc-hi.ca/browse/SHC</url>
    </issueManagement>
    <developers>
        <developer>
            <name>Justin Fyfe</name>
            <organization>Mohawk College of Applied Arts and Technology</organization>
            <email>justin_dot_fyfe1_at_mohawkcollege_dot_ca</email>
        </developer>
        <developer>
            <name>Paul Brown</name>
            <organization>Mohawk College of Applied Arts and Technology</organization>
            <email>paul_dot_brown9_at_mohawkcollege_dot_ca</email>
        </developer>
        <developer>
            <name>Garrett Tyler</name>
            <organization>Mohawk College of Applied Arts and Technology</organization>
            <email>garrett_dot_tyler_at_mohawkcollege_dot_ca</email>
        </developer>
        <developer>
            <name>Mohamed Ibrahim</name>
            <organization>Mohawk College of Applied Arts and Technology</organization>
            <email>mohamed_dot_ibrahim1_at_mohawkcollege_dot_ca</email>
        </developer>
    </developers>
    <distributionManagement>
        <site>
            <id>marc-te-main-shic-doc-distro</id>
            <url>file://O:/org/marc/shic/shic-pix</url>
        </site>
        <repository>
            <id>marc-te-main-distro</id>
            <url>file://O:/</url>
        </repository>
    </distributionManagement>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>2.3.2</version>
                <configuration>
                    <source>1.6</source>
                    <target>1.6</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <repositories>
        <repository>
            <id>hapi-sf</id>
            <name>HAPI Sourceforge Repository</name>
            <url>http://hl7api.sourceforge.net/m2</url>
        </repository>
        <repository>
            <id>marc-te-main</id>
            <name>MARC-HI Technology Exchange Private Maven Repository</name>
            <url>http://te.marc-hi.ca/mvn</url>
        </repository>
    </repositories>
    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.10</version>
            <scope>test</scope>
            <type>jar</type>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>shic-atna</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>ca.uhn.hapi</groupId>
            <artifactId>hapi-base</artifactId>
            <version>1.0.1</version>
            <type>jar</type>
        </dependency>
        <dependency>
            <groupId>ca.uhn.hapi</groupId>
            <artifactId>hapi-structures-v231</artifactId>
            <version>1.0.1</version>
            <type>jar</type>
        </dependency>
        <dependency>
            <groupId>ca.uhn.hapi</groupId>
            <artifactId>hapi-structures-v25</artifactId>
            <version>1.0.1</version>
            <type>jar</type>
        </dependency>
        <dependency>
            <groupId>ca.uhn.hapi</groupId>
            <artifactId>hapi-examples</artifactId>
            <version>1.0.1</version>
            <type>jar</type>
        </dependency>
    </dependencies>
</project>
