#!/bin/bash

echo "Creating a clean commit without trailing whitespace changes..."

# Reset everything first
git reset

# Apply only the meaningful changes from your original commit
# We'll use git apply with --ignore-whitespace to avoid whitespace issues

# Create a patch of your original commit but ignore whitespace
git show ccdb6c6ee8 --ignore-all-space > /tmp/clean_changes.patch

# Apply the patch ignoring whitespace
git apply --ignore-whitespace /tmp/clean_changes.patch

# Now manually stage only the files that should have changes
echo "Staging only meaningful changes..."

# Stage the files with meaningful changes
git add src/main/webapp/common/pendoHeader.jsp
git add src/main/webapp/dms/documentUploader.jsp  
git add src/main/webapp/lab/CA/ALL/testUploader.jsp
git add src/main/webapp/oscarMDS/SelectProvider.jsp

# For the larger files, we need to be more careful
# Let's check what the actual changes are vs whitespace
echo "Checking incomingDocs.jsp for meaningful changes..."
git add src/main/webapp/dms/incomingDocs.jsp

echo "Checking documentsInQueues.jsp for meaningful changes..."
git add src/main/webapp/oscarMDS/documentsInQueues.jsp

echo "Files staged:"
git status --porcelain

echo ""
echo "Ready to commit. Run:"
echo "git commit -m 'JEMR-633: Integrate Pendo with Inbox in Juno - clean version'"
