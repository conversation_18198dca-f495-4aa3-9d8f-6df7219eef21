< ? x m l   v e r s i o n = " 1 . 0 "   e n c o d i n g = " U T F - 1 6 "   s t a n d a l o n e = " y e s " ? > 
 
 < x s d : s c h e m a   x m l n s : x s d = " h t t p : / / w w w . w 3 . o r g / 2 0 0 1 / X M L S c h e m a "   e l e m e n t F o r m D e f a u l t = " q u a l i f i e d " > 
 
 
 
 	 < ! - -   
 
 	 = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = 
 
 	 P r o g r a m   N a m e 	 :   o c a n _ v 1 . 0 . 0 . x s d   
 
 	 D e s c r i p t i o n 	 :   X M L   s c h e m a   f o r   t h e   f i l e   s u b m i s s i o n   o f   O C A N   a s s e s s m e n t   t o o l   f o r   N E   L H I N   p i l o t 
 
 	 D e v e l o p e d   b y 	 :   K h a l e d   A w a d 
 
 	 H i s t o r y 	 	 :   2 3 - F e b - 2 0 0 9   i n i t i a l   v e r s i o n 
 
 	 	 	     1 3 - M a r - 2 0 0 9   r e v i e w e d   o p t i o n a l   v s .   m a n d a t o r y 
 
 	 = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = 
 
 	 - - > 
 
 
 
 	 < x s d : e l e m e n t   n a m e = " O C A N S u b m i s s i o n F i l e " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 
 
 	 	 	 	 	 < x s d : s e q u e n c e   m a x O c c u r s = " u n b o u n d e d " > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " O C A N S u b m i s s i o n R e c o r d "   / > 
 
 	 	 	 	 	 < / x s d : s e q u e n c e > 
 
 
 
 	 	 	 	 	 < ! - -   O C A N   T o o l   V e r s i o n   N u m b e r :   i t   h a s   t o   b e   a   c o n s t a n t   1 . 0 . 0   - - > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " v e r s i o n "   u s e = " r e q u i r e d " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 . 0 . 0 "   / > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 	 < ! - -   F i l e   I D   - - > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " I D "   u s e = " r e q u i r e d " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : m a x L e n g t h   v a l u e = " 6 4 " > 
 
 	 	 	 	 	 	 	 	 < / x s d : m a x L e n g t h > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 	 < ! - -   T i m e s t a m p :   o n   w h i c h   t h e   F i l e   i s   g e n e r a t e d   Y Y Y Y - M M - D D T h h : m i : s s Z   - - > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " t i m e s t a m p "   u s e = " r e q u i r e d " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : d a t e T i m e " > 
 
 	 	 	 	 	 	 	 	 < x s d : p a t t e r n   v a l u e = " ( ( 0 0 0 [ 1 - 9 ] ) | ( 0 0 [ 1 - 9 ] [ 0 - 9 ] ) | ( 0 [ 1 - 9 ] [ 0 - 9 ] { 2 } ) | ( [ 1 - 9 ] [ 0 - 9 ] { 3 } ) ) - ( ( 0 [ 1 - 9 ] ) | ( 1 [ 0 1 2 ] ) ) - ( ( 0 [ 1 - 9 ] ) | ( [ 1 2 ] [ 0 - 9 ] ) | ( 3 [ 0 1 ] ) ) T ( ( [ 0 1 ] [ 0 - 9 ] ) | ( 2 [ 0 - 3 ] ) ) ( : [ 0 - 5 ] [ 0 - 9 ] ) { 2 } ( \ . [ 0 - 9 ] + ) ? ( ( [ \ + | \ - ] ( ( 0 [ 0 - 9 ] ) | ( 1 [ 0 - 2 ] ) ) ( : [ 0 - 5 ] [ 0 - 9 ] ) ) | ( \ + 1 3 ( : [ 0 - 5 ] [ 0 - 9 ] ) ( : [ 0 - 5 ] [ 0 - 9 ] ) ) | \ + 1 4 : 0 0 | Z ) "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : m i n I n c l u s i v e   v a l u e = " 1 9 0 0 - 0 1 - 0 1 T 0 0 : 0 0 : 0 0 Z "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : m a x I n c l u s i v e   v a l u e = " 2 0 7 9 - 0 6 - 0 6 T 0 0 : 0 0 : 0 0 Z "   / > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < x s d : e l e m e n t   n a m e = " O C A N S u b m i s s i o n R e c o r d " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 
 
 	 	 	 	 	 < x s d : s e q u e n c e > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " o r g a n i z a t i o n R e c o r d "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " c l i e n t R e c o r d "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " O C A N D o m a i n s "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " a d d i t i o n a l E l e m e n t s "   / > 
 
 	 	 	 	 	 < / x s d : s e q u e n c e > 
 
 
 
 	 	 	 	 	 < ! - -   
 
 	 	 	 	 	 	 T h e r e   i s   a   l i m i t a t i o n   r e g a r d i n g   t y p e s   s u c h   a s   x s d : d a t e T i m e   a n d   x s d : d a t e   i n   S Q L   S e r v e r   2 0 0 5   
 
 	 	 	 	 	 	 a s   w e l l   a s   t h e i r   d e r i v e d   t y p e s .   S Q L   S e r v e r   2 0 0 5   a l w a y s   r e q u i r e s   a   t i m e z o n e   ( w h e r e a s   t h e   X S D   
 
 	 	 	 	 	 	 s t a n d a r d   s a y s   v a l u e s   w i t h   o r   w i t h o u t   a   t i m e   z o n e   s h o u l d   b e   a c c e p t e d ) . 
 
 	 	 	 	 	 	 F o r   t h i s   r e a s o n   t h e r e   a r e   t h r e e   f i e l d s   i n   t h i s   s c h e m a   a r e   d e f i n e d   a s   d a t e ,   a n d   i n   t h e   X M L 
 
 	 	 	 	 	 	 f e e d ,   t h e i r   v a l u e s   M U S T   B E   s u f f i x e d   b y   Z .   t h e   f i e l d s   a r e   s t a r t D a t e ,   c o m p l e t i o n D a t e ,   c l i e n t D O B 
 
 	 	 	 	 	 - - > 
 
 	 	 	 	 	 
 
 	 	 	 	 	 < ! - -   A s s e s s m e n t   S t a r t   D a t e :   Y Y Y Y - M M - D D Z   n o t e   t h e   e x i s t e n c e   o f   t h e   l e t t e r   Z   a t   t h e   e n d   o f   d a t e   v a l u e   - - > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " s t a r t D a t e "   u s e = " r e q u i r e d " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : d a t e " > 
 
 	 	 	 	 	 	 	 	 < x s d : p a t t e r n   v a l u e = " ( ( 0 0 0 [ 1 - 9 ] ) | ( 0 0 [ 1 - 9 ] [ 0 - 9 ] ) | ( 0 [ 1 - 9 ] [ 0 - 9 ] { 2 } ) | ( [ 1 - 9 ] [ 0 - 9 ] { 3 } ) ) - ( ( 0 [ 1 - 9 ] ) | ( 1 [ 0 1 2 ] ) ) - ( ( 0 [ 1 - 9 ] ) | ( [ 1 2 ] [ 0 - 9 ] ) | ( 3 [ 0 1 ] ) ) ( Z ) "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : m i n I n c l u s i v e   v a l u e = " 1 9 0 0 - 0 1 - 0 1 Z "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : m a x I n c l u s i v e   v a l u e = " 2 0 7 9 - 0 6 - 0 6 Z "   / > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 	 < ! - -   A s s e s s m e n t   C o m p l e t i o n   D a t e :   Y Y Y Y - M M - D D Z   n o t e   t h e   e x i s t e n c e   o f   t h e   l e t t e r   Z   a t   t h e   e n d   o f   d a t e   v a l u e   - - > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " c o m p l e t i o n D a t e "   u s e = " r e q u i r e d " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : d a t e " > 
 
 	 	 	 	 	 	 	 	 < x s d : p a t t e r n   v a l u e = " ( ( 0 0 0 [ 1 - 9 ] ) | ( 0 0 [ 1 - 9 ] [ 0 - 9 ] ) | ( 0 [ 1 - 9 ] [ 0 - 9 ] { 2 } ) | ( [ 1 - 9 ] [ 0 - 9 ] { 3 } ) ) - ( ( 0 [ 1 - 9 ] ) | ( 1 [ 0 1 2 ] ) ) - ( ( 0 [ 1 - 9 ] ) | ( [ 1 2 ] [ 0 - 9 ] ) | ( 3 [ 0 1 ] ) ) ( Z ) "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : m i n I n c l u s i v e   v a l u e = " 1 9 0 0 - 0 1 - 0 1 Z "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : m a x I n c l u s i v e   v a l u e = " 2 0 7 9 - 0 6 - 0 6 Z "   / > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 	 < ! - -   A s s e s s m e n t   I D :   i t   h a s   t o   b e   u n i q u e   a c c r o s s   t h e   f i l e   - - > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " a s s e s s m e n t I D "   u s e = " r e q u i r e d " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : m a x L e n g t h   v a l u e = " 6 4 " > 
 
 	 	 	 	 	 	 	 	 < / x s d : m a x L e n g t h > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 	 < ! - -   A s s e s s m e n t   S t a t u s :   o n l y   C o m p l e t e d   a s s e s s m e n t s   w i l l   b e   i n c l u d e d   i n   d a t a   s u b m i s s i o n   - - > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " a s s e s s m e n t S t a t u s "   u s e = " r e q u i r e d " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C o m p l e t e "   / > 	 < ! - -   C o m p l e t e d   - - > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < x s d : e l e m e n t   n a m e = " o r g a n i z a t i o n R e c o r d " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 	 	 	 	 	 < x s d : s e q u e n c e > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " s e r v i c e O r g "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " p r o g r a m "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " M I S F u n c t i o n "   / > 
 
 	 	 	 	 	 < / x s d : s e q u e n c e > 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < x s d : e l e m e n t   n a m e = " s e r v i c e O r g " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 
 
 	 	 	 	 	 < x s d : s e q u e n c e   / > 
 
 
 
 	 	 	 	 	 < ! - -   S e r v i c e   O r g a n i z a t i o n   N a m e   - - > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " n a m e "   u s e = " r e q u i r e d " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : m a x L e n g t h   v a l u e = " 1 2 8 " > 
 
 	 	 	 	 	 	 	 	 < / x s d : m a x L e n g t h > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 	 < ! - -   S e r v i c e   O r g a n i z a t i o n   N u m b e r   - - > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " n u m b e r "   u s e = " r e q u i r e d " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : m a x L e n g t h   v a l u e = " 4 " > 
 
 	 	 	 	 	 	 	 	 < / x s d : m a x L e n g t h > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < x s d : e l e m e n t   n a m e = " p r o g r a m " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 
 
 	 	 	 	 	 < x s d : s e q u e n c e   / > 
 
 
 
 	 	 	 	 	 < ! - -   P r o g r a m   N a m e   - - > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " n a m e "   u s e = " r e q u i r e d " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : m a x L e n g t h   v a l u e = " 1 2 8 " > 
 
 	 	 	 	 	 	 	 	 < / x s d : m a x L e n g t h > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 	 < ! - -   P r o g r a m   N u m b e r   - - > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " n u m b e r "   u s e = " r e q u i r e d " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : m a x L e n g t h   v a l u e = " 6 4 " > 
 
 	 	 	 	 	 	 	 	 < / x s d : m a x L e n g t h > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   M I S   F u n c t i o n a l   C e n t r e   ( C D S   Q u e s t i o n )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " M I S F u n c t i o n " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 
 
 	 	 	 	 	 < x s d : s e q u e n c e   / > 
 
 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " v a l u e " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 7 2 5   0 9   7 6 "   / > 	 	 < ! - -   C M   -   C a s e   M a n a g e m e n t   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 7 2 5   1 0   7 6   1 2 "   / > 	 < ! - -   C T   -   C o u n s e l i n g   a n d   T r e a t m e n t   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 7 2 5   1 0   7 6   2 0 "   / > 	 < ! - -   A C T   -   A s s e r t i v e   C o m m u n i t y   T r e a t m e n t   T e a m s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 7 2 5   1 0   7 6   3 0 "   / > 	 < ! - -   C M H   -   C o m m u n i t y   M e n t a l   H e a l t h   C l i n i c   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 7 2 5   1 0   7 6   4 0 "   / > 	 < ! - -   E M P   -   V o c a t i o n a l ,   E m p l o y m e n t   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 7 2 5   1 0   7 6   4 1 "   / > 	 < ! - -   C L U   -   C l u b h o u s e s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 7 2 5   1 0   7 6   4 5 "   / > 	 < ! - -   C O N   -   C o n c u r r e n t   D i s o r d e r s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 7 2 5   1 0   7 6   5 0 "   / > 	 < ! - -   C H I   -   C h i l d ,   A d o l e s c e n t   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 7 2 5   1 0   7 6   5 1 "   / > 	 < ! - -   E A R   -   E a r l y   I n t e r v e n t i o n   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 7 2 5   1 0   7 6   5 5 "   / > 	 < ! - -   F O R   -   F o r e n s i c   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 7 2 5   1 0   7 6   5 6 "   / > 	 < ! - -   D C S   -   D i v e r s i o n   a n d   C o u r t   S u p p o r t   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 7 2 5   1 0   7 6   6 0 "   / > 	 < ! - -   A S   -   A b u s e   S e r v i c e s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 7 2 5   1 0   7 6   7 0 "   / > 	 < ! - -   E A T   -   E a t i n g   D i s o r d e r   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 7 2 5   1 0   7 6   8 1 "   / > 	 < ! - -   S R   -   S o c i a l   R e h a b i l i t a t i o n ,   R e c r e a t i o n   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 7 2 5   1 0   7 6   9 5 "   / > 	 < ! - -   D D x   -   D u a l   D i a g n o s i s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 7 2 5   1 0   7 6   9 6 "   / > 	 < ! - -   G E R   -   P s y c h o - g e r i a t r i c   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 7 2 5   1 5   7 6 "   / > 	 	 < ! - -   C R I   -   M e n t a l   H e a l t h   C r i s i s   I n t e r v e n t i o n   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 7 2 5   2 0   7 6 "   / > 	 	 < ! - -   D N   -   P r i m a r y   D a y   a n d   N i g h t   C a r e   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 7 2 5   4 0   7 6   1 0 "   / > 	 < ! - -   H S C   -   H o m e s   f o r   S p e c i a l   C a r e   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 7 2 5   4 0   7 6   3 0 "   / > 	 < ! - -   S H   -   S u p p o r t i v e   w i t h i n   H o u s i n g   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 7 2 5   4 0   7 6   6 0 "   / > 	 < ! - -   C S B   -   S h o r t   t e r m   R e s . C r i s i s   S u p p o r t   B e d s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 7 2 5   7 0   1 1 "   / > 	 	 < ! - -   I R   -   C o m m u n i t y   S e r v i c e   I n f o r m a t i o n   a n d   R e f e r r a l   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 7 2 5   5 0   7 6   1 0 "   / > 	 < ! - -   H P A   -   H e a l t h   P r o m o t i o n ,   E d u c a t i o n   -   A w a r e n e s s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 7 2 5   5 0   7 6   4 0 "   / > 	 < ! - -   C D   -   C o m m u n i t y   D e v e l o p m e n t   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 7 2 5   5 1   7 6   1 1 "   / > 	 < ! - -   P S H   -   P e e r ,   S e l f - h e l p   I n i t i a t i v e s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 7 2 5   5 1   7 6   1 2 "   / > 	 < ! - -   A B   -   A l t e r n a t i v e   B u s i n e s s e s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 7 2 5   5 1   7 6   2 0 "   / > 	 < ! - -   F I   -   F a m i l y   I n i t i a t i v e s   - - > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < x s d : e l e m e n t   n a m e = " c l i e n t R e c o r d " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 	 	 	 	 	 < x s d : s e q u e n c e > 
 
 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " c l i e n t I D "   / > 
 
 
 
 	 	 	 	 	 	 < ! - -   B e g i n   P H I   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " c l i e n t N a m e "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " c l i e n t A d d r e s s "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " c l i e n t P h o n e "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " c l i e n t O H I P "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " c l i e n t C u l t u r e "   / > 
 
 	 	 	 	 	 	 < ! - -   E n d   P H I   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " r e a s o n F o r A s s e s s m e n t "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " c l i e n t C o n t a c t "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " s e r v i c e R e c i p i e n t L o c a t i o n "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " s e r v i c e R e c i p i e n t L H I N "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " s e r v i c e D e l i v e r y L H I N "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " c l i e n t D O B "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " g e n d e r "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " m a r i t a l S t a t u s "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " c l i e n t C a p a c i t y "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " r e f e r r a l S o u r c e "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " a b o r i g i n a l O r i g i n "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " c i t i z e n s h i p S t a t u s "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " t i m e L i v e d I n C a n a d a "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " i m m i g E x p L i s t "   m i n O c c u r s = " 0 "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " d i s c r i m E x p L i s t "   m i n O c c u r s = " 0 "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " p r e f L a n g "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " s e r v i c e L a n g "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " l e g a l I s s u e s "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " l e g a l S t a t u s L i s t "   m i n O c c u r s = " 0 "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " e x i t D i s p o s i t i o n "   / > 
 
 
 
 	 	 	 	 	 < / x s d : s e q u e n c e > 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   C l i e n t   I D :   t h i s   s h o u l d   b e   u n i q u e   w i t h i n   t h e   o r g a n i z a t i o n   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " c l i e n t I D " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 	 	 	 	 	 < x s d : s e q u e n c e   / > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " o r g C l i e n t I D "   u s e = " r e q u i r e d " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : m a x L e n g t h   v a l u e = " 6 4 " > 
 
 	 	 	 	 	 	 	 	 < / x s d : m a x L e n g t h > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   B e g i n   P H I   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 < ! - -   
 
 	 	   N o t e :   
 
 	 	 	 A l l   P H I   e l e m e n t s   a r e   n o t   s u p p o s e d   t o   b e   c o l l e c t e d   d u r i n g   t h i s   p h a s e . 
 
 	 	 	 T h e r e   i s   a   p l a n   t o   c o l l e c t   t h e m   i n   t h e   f u t u r e ,   s o ,   t h e   p l a c e h o l d e r   
 
 	 	 	 i . e .   t h e   T A G s   w i l l   b e   t h e r e   b u t   t h e y   a r e   r e s t r i c t e d   w i t h   m a x L e n g t h = 0 
 
 	 	 	 i n   o r d e r   t o   e n s u r e   n o   P H I   d a t a   i s   s e n t   a t   t h i s   t i m e . 
 
 	 	 	 P H I   T A G s   w i l l   b e   i n c l u d e d   i n   t h e   d a t a   s u b m i s s i o n   f i l e   b u t   w i t h   e m p t y   v a l u e s . 
 
 	 - - > 
 
 
 
 	 < ! - -   C l i e n t   N a m e   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " c l i e n t N a m e " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 
 
 	 	 	 	 	 < x s d : s e q u e n c e   / > 
 
 
 
 	 	 	 	 	 < ! - -   L a s t   N a m e   - - > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " l a s t "   u s e = " r e q u i r e d " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : m a x L e n g t h   v a l u e = " 0 "   / > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 	 < ! - -   F i r s t   N a m e   - - > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " f i r s t "   u s e = " r e q u i r e d " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : m a x L e n g t h   v a l u e = " 0 "   / > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   C l i e n t   A d d r e s s   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " c l i e n t A d d r e s s " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 
 
 	 	 	 	 	 < x s d : s e q u e n c e   / > 
 
 
 
 	 	 	 	 	 < ! - -   A d d r e s s   L i n e   1   - - > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " l i n e 1 "   u s e = " o p t i o n a l " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : m a x L e n g t h   v a l u e = " 0 "   / > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 	 < ! - -   A d d r e s s   L i n e   2   - - > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " l i n e 2 "   u s e = " o p t i o n a l " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : m a x L e n g t h   v a l u e = " 0 "   / > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 	 < ! - -   C i t y   - - > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " c i t y "   u s e = " o p t i o n a l " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : m a x L e n g t h   v a l u e = " 0 "   / > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 	 < ! - -   P r o v i n c e   - - > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " p r o v i n c e "   u s e = " o p t i o n a l " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : m a x L e n g t h   v a l u e = " 0 "   / > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 	 < ! - -   P o s t a l   C o d e   - - > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " p o s t a l C o d e "   u s e = " o p t i o n a l " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : m a x L e n g t h   v a l u e = " 0 "   / > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   C l i e n t   P h o n e   N u m b e r   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " c l i e n t P h o n e " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : m a x L e n g t h   v a l u e = " 0 "   / > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   C l i e n t   O H I P   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " c l i e n t O H I P " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 
 
 	 	 	 	 	 < x s d : s e q u e n c e   / > 
 
 
 
 	 	 	 	 	 < ! - -   O H I P   N u m b e r   - - > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " n u m b e r "   u s e = " o p t i o n a l " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : m a x L e n g t h   v a l u e = " 0 "   / > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 	 < ! - -   O H I P   V e r s i o n   - - > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " v e r s i o n "   u s e = " o p t i o n a l " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : m a x L e n g t h   v a l u e = " 0 "   / > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   W h a t   c u l t u r e   d o   y o u   i d e n t i f y   w i t h ?   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " c l i e n t C u l t u r e " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : m a x L e n g t h   v a l u e = " 0 "   / > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   E n d   P H I   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 
 
 	 < ! - -   R e a s o n   f o r   A s s e s s m e n t   ( s e l e c t   o n e )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " r e a s o n F o r A s s e s s m e n t " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 
 
 	 	 	 	 	 < x s d : s e q u e n c e   / > 
 
 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " v a l u e "   u s e = " r e q u i r e d " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " I A "   / > 	 	 < ! - -   I n i t i a l   A s s e s s m e n t   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " R A "   / > 	 	 < ! - -   R e a s s e s s m e n t   a t   6   m o n t h s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " D I S "   / > 	 	 < ! - -   ( P r i o r   t o )   d i s c h a r g e   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " O T H R "   / > 	 < ! - -   O t h e r   ( s i g n i f i c a n t   c h a n g e ,   c l i e n t   r e q u e s t )   - - > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " o t h e r "   u s e = " r e q u i r e d " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g "   / > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   C l i e n t   C o n t a c t   I n f o r m a t i o n   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " c l i e n t C o n t a c t " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 	 	 	 	 	 < x s d : s e q u e n c e > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " d o c t o r C o n t a c t "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " p s y c h i a t r i s t C o n t a c t "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " o t h e r P r a c t i t i o n e r C o n t a c t "   m a x O c c u r s = " u n b o u n d e d "   m i n O c c u r s = " 0 "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " o t h e r A g e n c y C o n t a c t "   / > 
 
 	 	 	 	 	 < / x s d : s e q u e n c e > 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   C l i e n t   D o c t o r   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " d o c t o r C o n t a c t " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 
 
 	 	 	 	 	 < x s d : s e q u e n c e   / > 
 
 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " d o c t o r "   u s e = " o p t i o n a l " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " T R U E "   / > 	 < ! - -   Y e s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " F A L S E "   / > 	 < ! - -   N o   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " N A "   / > 	 	 < ! - -   N o n e   A v a i l a b l e   - - > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " l a s t S e e n "   u s e = " o p t i o n a l " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " L S - 1 "   / > 	 < ! - -   W i t h i n   t h e   l a s t   m o n t h   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " L S - 6 "   / > 	 < ! - -   W i t h i n   t h e   l a s t   s i x   m o n t h s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " L S - 1 2 "   / > 	 < ! - -   W i t h i n   t h e   l a s t   y e a r   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " L S - 1 3 "   / > 	 < ! - -   M o r e   t h a n   a   y e a r   a g o   - - > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   C l i e n t   P s y c h i a t r i s t   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " p s y c h i a t r i s t C o n t a c t " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 
 
 	 	 	 	 	 < x s d : s e q u e n c e   / > 
 
 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " p s y c h i a t r i s t "   u s e = " o p t i o n a l " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " T R U E "   / > 	 < ! - -   Y e s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " F A L S E "   / > 	 < ! - -   N o   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " N A "   / > 	 	 < ! - -   N o n e   A v a i l a b l e   - - > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " l a s t S e e n "   u s e = " o p t i o n a l " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " L S - 1 "   / > 	 < ! - -   W i t h i n   t h e   l a s t   m o n t h   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " L S - 6 "   / > 	 < ! - -   W i t h i n   t h e   l a s t   s i x   m o n t h s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " L S - 1 2 "   / > 	 < ! - -   W i t h i n   t h e   l a s t   y e a r   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " L S - 1 3 "   / > 	 < ! - -   M o r e   t h a n   a   y e a r   a g o   - - > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   C l i e n t   O t h e r   P r a c t i t i o n e r   C o n t a c t s   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " o t h e r P r a c t i t i o n e r C o n t a c t " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 
 
 	 	 	 	 	 < x s d : s e q u e n c e   / > 
 
 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " p r a c t i t i o n e r T y p e "   u s e = " o p t i o n a l " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 1 1 1 "   / > 	 < ! - -   S p e c i a l i s t   P h y s i c i a n s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 1 1 2 "   / > 	 < ! - -   G e n e r a l   P r a c t i t i o n e r s   a n d   F a m i l y   P h y s i c i a n s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 1 1 3 "   / > 	 < ! - -   D e n t i s t s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 1 2 1 "   / > 	 < ! - -   O p t o m e t r i s t s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 1 2 2 "   / > 	 < ! - -   C h i r o p r a c t o r s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 1 2 3 "   / > 	 < ! - -   O t h e r   P r o f e s s i o n a l   O c c u p a t i o n s   i n   H e a l t h   D i a g n o s i n g   a n d   T r e a t i n g   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 1 3 1 "   / > 	 < ! - -   P h a r m a c i s t s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 1 3 2 "   / > 	 < ! - -   D i e t i c i a n s   a n d   N u t r i t i o n i s t s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 1 4 1 "   / > 	 < ! - -   A u d i o l o g i s t s   a n d   S p e e c h - L a n g u a g e   P a t h o l o g i s t s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 1 4 2 "   / > 	 < ! - -   P h y s i o t h e r a p i s t s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 1 4 3 "   / > 	 < ! - -   O c c u p a t i o n a l   T h e r a p i s t s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 1 4 4 "   / > 	 < ! - -   O t h e r   P r o f e s s i o n a l   O c c u p a t i o n s   i n   T h e r a p y   a n d   A s s e s s m e n t   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 1 5 2 "   / > 	 < ! - -   R e g i s t e r e d   N u r s e s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 2 1 4 "   / > 	 < ! - -   R e s p i r a t o r y   T h e r a p i s t s ,   C l i n i c a l   P e r f u s i o n i s t s   a n d   C a r d i o - P u l m o n a r y   T e c h n o l o g i s t s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 2 2 1 "   / > 	 < ! - -   D e n t u r i s t s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 2 2 2 "   / > 	 < ! - -   D e n t a l   H y g i e n i s t s   a n d   D e n t a l   T h e r a p i s t s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 2 3 1 "   / > 	 < ! - -   O p t i c i a n s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 2 3 2 "   / > 	 < ! - -   M i d w i v e s   a n d   P r a c t i t i o n e r s   o f   N a t u r a l   H e a l i n g   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 2 3 3 "   / > 	 < ! - -   L i c e n s e d   P r a c t i c a l   N u r s e s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 2 3 5 "   / > 	 < ! - -   O t h e r   T e c h n i c a l   O c c u p a t i o n s   i n   T h e r a p y   a n d   A s s e s s m e n t   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 4 1 5 1 "   / > 	 < ! - -   P s y c h o l o g i s t s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 4 1 5 2 "   / > 	 < ! - -   S o c i a l   W o r k e r s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 4 1 5 3 "   / > 	 < ! - -   F a m i l y ,   M a r r i a g e   a n d   O t h e r   R e l a t e d   C o u n s e l l o r s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 4 1 5 4 "   / > 	 < ! - -   M i n i s t e r s   o f   R e l i g i o n   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 4 1 6 7 "   / > 	 < ! - -   R e c r e a t i o n ,   S p o r t s   a n d   F i t n e s s   P r o g r a m   S u p e r v i s o r s   a n d   C o n s u l t a n t s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 4 2 1 2 "   / > 	 < ! - -   C o m m u n i t y   a n d   S o c i a l   S e r v i c e   W o r k e r s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 4 2 1 3 "   / > 	 < ! - -   E m p l o y m e n t   C o u n s e l l o r s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 4 2 1 5 "   / > 	 < ! - -   I n s t r u c t o r s   a n d   T e a c h e r s   o f   P e r s o n s   w i t h   D i s a b i l i t i e s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 4 2 1 7 "   / > 	 < ! - -   O t h e r   R e l i g i o u s   O c c u p a t i o n s   - - > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " l a s t S e e n "   u s e = " o p t i o n a l " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " L S - 1 "   / > 	 < ! - -   W i t h i n   t h e   l a s t   m o n t h   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " L S - 6 "   / > 	 < ! - -   W i t h i n   t h e   l a s t   s i x   m o n t h s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " L S - 1 2 "   / > 	 < ! - -   W i t h i n   t h e   l a s t   y e a r   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " L S - 1 3 "   / > 	 < ! - -   M o r e   t h a n   a   y e a r   a g o   - - > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   C l i e n t   O t h e r   A g e n c y   C o n t a c t   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " o t h e r A g e n c y C o n t a c t " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 	 	 	 	 	 < x s d : s e q u e n c e   / > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " l a s t S e e n "   u s e = " o p t i o n a l " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " L S - 1 "   / > 	 < ! - -   W i t h i n   t h e   l a s t   m o n t h   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " L S - 6 "   / > 	 < ! - -   W i t h i n   t h e   l a s t   s i x   m o n t h s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " L S - 1 2 "   / > 	 < ! - -   W i t h i n   t h e   l a s t   y e a r   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " L S - 1 3 "   / > 	 < ! - -   M o r e   t h a n   a   y e a r   a g o   - - > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   S e r v i c e   R e c i p i e n t   L o c a t i o n   ( c o u n t y ,   d i s t r i c t ,   m u n i c i p a l i t y )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " s e r v i c e R e c i p i e n t L o c a t i o n " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 0 1 "   / > 	 < ! - -   A l g o m a   D i s t r i c t   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 0 2 "   / > 	 < ! - -   B r a n t   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 0 3 "   / > 	 < ! - -   B r u c e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 0 4 "   / > 	 < ! - -   C o c h r a n e   D i s t r i c t   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 0 5 "   / > 	 < ! - -   D u f f e r i n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 0 6 "   / > 	 < ! - -   D u r h a m   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 0 7 "   / > 	 < ! - -   E l g i n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 0 8 "   / > 	 < ! - -   E s s e x   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 0 9 "   / > 	 < ! - -   F r o n t e n a c   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 1 0 "   / > 	 < ! - -   G r e y   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 1 1 "   / > 	 < ! - -   H a l d i m a n d - N o r f o l k   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 1 2 "   / > 	 < ! - -   H a l i b u r t o n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 1 3 "   / > 	 < ! - -   H a l t o n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 1 4 "   / > 	 < ! - -   H a m i l t o n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 1 5 "   / > 	 < ! - -   H a s t i n g s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 1 6 "   / > 	 < ! - -   H u r o n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 1 7 "   / > 	 < ! - -   K e n o r a   a n d   K e n o r a   P . P .   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 1 8 "   / > 	 < ! - -   C h a t h a m - K e n t   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 1 9 "   / > 	 < ! - -   L a m b t o n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 2 0 "   / > 	 < ! - -   L a n a r k   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 2 1 "   / > 	 < ! - -   L e e d s   a n d   G r e n v i l l e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 2 2 "   / > 	 < ! - -   L e n n o x   a n d   A d d i n g t o n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 2 3 "   / > 	 < ! - -   M a n i t o u l i n   D i s t r i c t   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 2 4 "   / > 	 < ! - -   M i d d l e s e x   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 2 5 "   / > 	 < ! - -   M u s k o k a   D i s t r i c t   M u n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 2 6 "   / > 	 < ! - -   N i a g a r a   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 2 7 "   / > 	 < ! - -   N i p i s s i n g   D i s t r i c t   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 2 8 "   / > 	 < ! - -   N o r t h u m b e r l a n d   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 2 9 "   / > 	 < ! - -   O t t a w a   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 3 0 "   / > 	 < ! - -   O u t   O f   P r o v i n c e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 3 1 "   / > 	 < ! - -   O x f o r d   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 3 2 "   / > 	 < ! - -   P a r r y   S o u n d   D i s t r i c t   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 3 3 "   / > 	 < ! - -   P e e l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 3 4 "   / > 	 < ! - -   P e r t h   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 3 5 "   / > 	 < ! - -   P e t e r b o r o u g h   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 3 6 "   / > 	 < ! - -   P r e s c o t t   a n d   R u s s e l l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 3 7 "   / > 	 < ! - -   P r i n c e   E d w a r d   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 3 8 "   / > 	 < ! - -   R a i n y   R i v e r   D i s t r i c t   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 3 9 "   / > 	 < ! - -   R e n f r e w   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 4 0 "   / > 	 < ! - -   S i m c o e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 4 1 "   / > 	 < ! - -   S t o r m o n t   D u n d a s   a n d   G l e n g a r r y   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 4 2 "   / > 	 < ! - -   S u d b u r y   D i s t r i c t   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 4 3 "   / > 	 < ! - -   S u d b u r y   R e g i o n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 4 4 "   / > 	 < ! - -   T h u n d e r   B a y   D i s t r i c t   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 4 5 "   / > 	 < ! - -   T i m i s k a m i n g   D i s t r i c t   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 4 6 "   / > 	 < ! - -   T o r o n t o   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 4 7 "   / > 	 < ! - -   U n k n o w n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 4 8 "   / > 	 < ! - -   V i c t o r i a   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 4 9 "   / > 	 < ! - -   W a t e r l o o   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 5 0 "   / > 	 < ! - -   W e l l i n g t o n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 5 1 "   / > 	 < ! - -   Y o r k   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 0 - 5 2 "   / > 	 < ! - -   O u t   o f   C o u n t r y   - - > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   S e r v i c e   R e c i p i e n t   L H I N   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " s e r v i c e R e c i p i e n t L H I N " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : i n t e g e r " > 
 
 	 	 	 	 < x s d : m i n I n c l u s i v e   v a l u e = " 1 "   / > 
 
 	 	 	 	 < x s d : m a x I n c l u s i v e   v a l u e = " 1 4 "   / > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   S e r v i c e   D e l i v e r y   L H I N   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " s e r v i c e D e l i v e r y L H I N " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : i n t e g e r " > 
 
 	 	 	 	 < x s d : m i n I n c l u s i v e   v a l u e = " 1 "   / > 
 
 	 	 	 	 < x s d : m a x I n c l u s i v e   v a l u e = " 1 4 "   / > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   C l i e n t   D a t e   o f   B i r t h :   Y Y Y Y - M M - D D Z   n o t e   t h e   e x i s t e n c e   o f   t h e   l e t t e r   Z   a t   t h e   e n d   o f   d a t e   v a l u e   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " c l i e n t D O B " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : d a t e " > 
 
 	 	 	 	 < x s d : p a t t e r n   v a l u e = " ( ( 0 0 0 [ 1 - 9 ] ) | ( 0 0 [ 1 - 9 ] [ 0 - 9 ] ) | ( 0 [ 1 - 9 ] [ 0 - 9 ] { 2 } ) | ( [ 1 - 9 ] [ 0 - 9 ] { 3 } ) ) - ( ( 0 [ 1 - 9 ] ) | ( 1 [ 0 1 2 ] ) ) - ( ( 0 [ 1 - 9 ] ) | ( [ 1 2 ] [ 0 - 9 ] ) | ( 3 [ 0 1 ] ) ) ( Z ) "   / > 
 
 	 	 	 	 < x s d : m i n I n c l u s i v e   v a l u e = " 1 9 0 0 - 0 1 - 0 1 Z "   / > 
 
 	 	 	 	 < x s d : m a x I n c l u s i v e   v a l u e = " 2 0 7 9 - 0 6 - 0 6 Z "   / > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   G e n d e r   ( s e l e c t   o n e )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " g e n d e r " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " M "   / > 	 < ! - -   M a l e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " F "   / > 	 < ! - -   F e m a l e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " O T H "   / > 	 < ! - -   O t h e r   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " U N K "   / > 	 < ! - -   U n k n o w n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C D A "   / > 	 < ! - -   C l i e n t   D e c l i n e d   t o   A n s w e r   - - > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   M a r i t a l   S t a t u s   ( s e l e c t   o n e )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " m a r i t a l S t a t u s " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 	 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 2 5 6 8 1 0 0 6 "   / > 	 < ! - -   S i n g l e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 8 7 9 1 5 0 0 2 "   / > 	 < ! - -   M a r r i e d   o r   i n   c o m m o n - l a w   r e l a t i o n s h i p   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 4 2 1 2 0 0 0 6 "   / > 	 < ! - -   P a r t n e r   o r   s i g n i f i c a n t   o t h e r   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 3 5 5 3 0 0 0 "   / > 	 < ! - -   W i d o w e d   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 3 1 8 4 0 0 1 "   / > 	 < ! - -   S e p a r a t e d   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 0 2 9 5 0 0 0 "   / > 	 < ! - -   D i v o r c e d   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 6 1 6 6 5 0 0 6 "   / > 	 < ! - -   U n k n o w n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C D A "   / > 	 	 	 < ! - -   C l i e n t   d e c l i n e d   t o   a n s w e r   - - > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   C l i e n t   C a p a c i t y   ( c h e c k   a l l   t h a t   a p p l y )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " c l i e n t C a p a c i t y " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 
 
 	 	 	 	 	 < x s d : s e q u e n c e   / > 
 
 
 
 	 	 	 	 	 < ! - -   D o e s   t h e   c l i e n t   h a v e   a   P o w e r   o f   A t t o r n e y   f o r   p r o p e r t y ?   - - > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " p r o p e r t y "   u s e = " o p t i o n a l " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " T R U E "   / > 	 < ! - -   Y e s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " F A L S E "   / > 	 < ! - -   N o   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " U N K "   / > 	 	 < ! - -   U n k n o w n   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C D A "   / > 	 	 < ! - -   C l i e n t   D e c l i n e d   t o   A n s w e r   - - > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 	 < ! - -   D o e s   t h e   c l i e n t   h a v e   a   P o w e r   o f   A t t o r n e y   o r   a   s u b s t i t u t e   d e c i s i o n   m a k e r   f o r   p e r s o n a l   c a r e ?   - - > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " p e r s o n a l C a r e "   u s e = " o p t i o n a l " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " T R U E "   / > 	 < ! - -   Y e s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " F A L S E "   / > 	 < ! - -   N o   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " U N K "   / > 	 	 < ! - -   U n k n o w n   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C D A "   / > 	 	 < ! - -   C l i e n t   D e c l i n e d   t o   A n s w e r   - - > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 	 < ! - -   D o e s   t h e   c l i e n t   h a v e   a   c o u r t   a p p o i n t e d   g u a r d i a n ?   - - > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " l e g a l G u a r d i a n "   u s e = " o p t i o n a l " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " T R U E "   / > 	 < ! - -   Y e s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " F A L S E "   / > 	 < ! - -   N o   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " U N K "   / > 	 	 < ! - -   U n k n o w n   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C D A "   / > 	 	 < ! - -   C l i e n t   D e c l i n e d   t o   A n s w e r   - - > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   W h o   r e f e r r e d   y o u   t o   t h i s   s e r v i c e ?   ( s e l e c t   o n e )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " r e f e r r a l S o u r c e " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 8 - 0 1 "   / > 	 < ! - -   G e n e r a l   H o s p i t a l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 8 - 0 2 "   / > 	 < ! - -   P s y c h i a t r i c   H o s p i t a l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 8 - 0 3 "   / > 	 < ! - -   O t h e r   i n s t i t u t i o n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 8 - 0 4 "   / > 	 < ! - -   C o m m u n i t y   M e n t a l   H e a l t h   a n d   A d d i c t i o n   O r g a n i z a t i o n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 8 - 0 5 "   / > 	 < ! - -   O t h e r   C o m m u n i t y   A g e n c i e s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 8 - 0 6 "   / > 	 < ! - -   F a m i l y   P h y s i c i a n s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 8 - 0 7 "   / > 	 < ! - -   P s y c h i a t r i s t s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 8 - 0 8 "   / > 	 < ! - -   M e n t a l   H e a l t h   W o r k e r   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 8 - 0 9 "   / > 	 < ! - -   C r i m i n a l   J u s t i c e   S y s t e m   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 8 - 1 0 "   / > 	 < ! - -   S e l f ,   F a m i l y   o r   F r i e n d   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 8 - 1 1 "   / > 	 < ! - -   O t h e r   - - > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   A b o r i g i n a l   O r i g i n   ( s e l e c t   o n e )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " a b o r i g i n a l O r i g i n " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 1 - 0 1 "   / > 	 < ! - -   A b o r i g i n a l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 1 - 0 2 "   / > 	 < ! - -   N o n - a b o r i g i n a l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 1 - 0 3 "   / > 	 < ! - -   U n k n o w n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 1 - 0 4 "   / > 	 < ! - -   C l i e n t   d e c l i n e d   t o   a n s w e r   - - > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   C i t i z e n s h i p   S t a t u s   ( s e l e c t   o n e )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " c i t i z e n s h i p S t a t u s " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C D N "   / > 	 	 < ! - -   C a n a d i a n   C i t i z e n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " P R "   / > 	 	 < ! - -   P e r m a n e n t   R e s i d e n t   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " T R "   / > 	 	 < ! - -   T e m p o r a r y   R e s i d e n t   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " R E F "   / > 	 	 < ! - -   R e f u g e e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " U N K "   / > 	 	 < ! - -   U n k n o w n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C D A "   / > 	 	 < ! - -   C l i e n t   D e c l i n e d   t o   A n s w e r   - - > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   L e n g t h   o f   t i m e   l i v e d   i n   C a n a d a   ( N u m b e r   o f   y e a r s ,   m o n t h s )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " t i m e L i v e d I n C a n a d a " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 	 	 	 	 	 < x s d : s e q u e n c e   / > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " y e a r s "   t y p e = " x s d : i n t e g e r "   u s e = " o p t i o n a l "   / > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " m o n t h s "   t y p e = " x s d : i n t e g e r "   u s e = " o p t i o n a l "   / > 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   D o   y o u   h a v e   a n y   i s s u e s   w i t h   y o u r   i m m i g r a t i o n   e x p e r i e n c e ?     ( c h e c k   a l l   t h a t   a p p l y )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " i m m i g E x p L i s t " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 
 
 	 	 	 	 	 < x s d : s e q u e n c e > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   n a m e = " v a l u e "   m i n O c c u r s = " 0 "   m a x O c c u r s = " u n b o u n d e d " > 
 
 	 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 < ! - -   N o n e   - - > 
 
 	 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 "   / > 	 < ! - -   L a c k   o f   u n d e r s t a n d i n g   o f   t h e   C a n a d i a n   s y s t e m , r e s o u r c e s   - - > 
 
 	 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 "   / > 	 < ! - -   A p p l y i n g   p r e v i o u s   w o r k   e x p e r i e n c e , p r o f e s s i o n a l   q u a l i f i c a t i o n s   - - > 
 
 	 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 "   / > 	 < ! - -   S e p a r a t i o n   f r o m   f a m i l y   m e m b e r s , s i g n i f i c a n t   o t h e r s   - - > 
 
 	 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 4 "   / > 	 < ! - -   F a m i l y   l e f t   b e h i n d   i n   r e f u g e e   c a m p   - - > 
 
 	 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 5 "   / > 	 < ! - -   E x p e r i e n c e   w i t h   w a r , i n c a r c e r a t i o n , t o r t u r e   - - > 
 
 	 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 6 "   / > 	 < ! - -   R e f u g e e   c a m p   - - > 
 
 	 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 7 "   / > 	 < ! - -   E x p e r i e n c e   w i t h   o t h e r   t r a u m a   - - > 
 
 	 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 8 "   / > 	 < ! - -   O t h e r   - - > 
 
 	 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 9 "   / > 	 < ! - -   U n k n o w n   - - > 
 
 	 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 0 "   / > 	 < ! - -   C l i e n t   d e c l i n e d   t o   a n s w e r   - - > 
 
 	 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 < / x s d : e l e m e n t > 
 
 	 	 	 	 	 < / x s d : s e q u e n c e > 
 
 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " o t h e r I m m i g E x p "   t y p e = " x s d : s t r i n g "   u s e = " o p t i o n a l "   / > 
 
 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   E x p e r i e n c e   o f   D i s c r i m i n a t i o n   ( c h e c k   a l l   t h a t   a p p l y )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " d i s c r i m E x p L i s t " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 
 
 	 	 	 	 	 < x s d : s e q u e n c e > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   n a m e = " v a l u e "   m i n O c c u r s = " 0 "   m a x O c c u r s = " u n b o u n d e d " > 
 
 	 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 	 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 1 1 3 4 0 0 2 "   / > 	 < ! - -   D i s a b i l i t y   - - > 
 
 	 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 9 7 7 3 1 0 0 0 "   / > 	 < ! - -   E t h n i c i t y   - - > 
 
 	 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 6 5 8 7 3 0 0 7 "   / > 	 < ! - -   G e n d e r   - - > 
 
 	 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " I M G R "   / > 	 	 < ! - -   I m m i g r a t i o n   - - > 
 
 	 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 7 4 7 3 2 0 0 9 "   / > 	 < ! - -   M e n t a l   I l l n e s s   - - > 
 
 	 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 4 1 5 2 2 9 0 0 0 "   / > 	 < ! - -   R a c e   - - > 
 
 	 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 6 5 5 7 7 0 0 2 "   / > 	 < ! - -   R e l i g i o n   - - > 
 
 	 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 6 5 9 5 6 0 0 9 "   / > 	 < ! - -   S e x u a l   O r i e n t a t i o n   - - > 
 
 	 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 4 1 0 5 1 5 0 0 3 "   / > 	 < ! - -   O t h e r   - - > 
 
 	 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 6 1 6 6 5 0 0 6 "   / > 	 < ! - -   U n k n o w n   - - > 
 
 	 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C D A "   / > 	 	 	 < ! - -   C l i e n t   d e c l i n e d   t o   a n s w e r   - - > 
 
 	 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 < / x s d : e l e m e n t > 
 
 	 	 	 	 	 < / x s d : s e q u e n c e > 
 
 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " o t h e r D i s c r i m E x p "   t y p e = " x s d : s t r i n g "   u s e = " o p t i o n a l "   / > 
 
 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   S e r v i c e   R e c i p i e n t   P r e f e r r e d   L a n g u a g e   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " p r e f L a n g " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " e n g "   / > 	 	 < ! - -   E n g l i s h   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " f r a "   / > 	 	 < ! - -   F r e n c h   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " a l q "   / > 	 	 < ! - -   A l g o n q u i n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " a t j "   / > 	 	 < ! - -   A t i k a m e k w   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " b l a "   / > 	 	 < ! - -   B l a c k f o o t   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " c r x "   / > 	 	 < ! - -   C a r r i e r   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " c l c "   / > 	 	 < ! - -   C h i l c o t i n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " c h p "   / > 	 	 < ! - -   C h i p e w y a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " c r e "   / > 	 	 < ! - -   C r e e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " d a k "   / > 	 	 < ! - -   S i o u a n   l a n g u a g e s   ( D a k o t a , S i o u x )   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " a t h "   / > 	 	 < ! - -   A t h a p a s k a n   l a n g u a g e s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " d g r "   / > 	 	 < ! - -   D o g r i b   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " g i t "   / > 	 	 < ! - -   G i t k s a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " i k t "   / > 	 	 < ! - -   I n u i n n a q t u n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " i k u "   / > 	 	 < ! - -   I n u k t i t u t ,   n . i . e .   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " g w i "   / > 	 	 < ! - -   K u t c h i n - G w i c h   i n   ( L o u c h e u x )   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " p q m "   / > 	 	 < ! - -   M a l e c i t e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " m i c "   / > 	 	 < ! - -   M i   k m a q   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " m o h "   / > 	 	 < ! - -   M o h a w k   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " m o e "   / > 	 	 < ! - -   M o n t a g n a i s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " n k s "   / > 	 	 < ! - -   N a s k a p i   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " n c g "   / > 	 	 < ! - -   N i s g a   a   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " s c s "   / > 	 	 < ! - -   N o r t h   S l a v e   ( H a r e )   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " o j i "   / > 	 	 < ! - -   O j i b w a y   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " a l g "   / > 	 	 < ! - -   O j i - C r e e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " s h s "   / > 	 	 < ! - -   S h u s w a p   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " x s l "   / > 	 	 < ! - -   S o u t h   S l a v e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " t l i "   / > 	 	 < ! - -   T l i n g i t   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " i t a "   / > 	 	 < ! - -   I t a l i a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " p o r "   / > 	 	 < ! - -   P o r t u g u e s e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " r o n "   / > 	 	 < ! - -   R o m a n i a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " s p a "   / > 	 	 < ! - -   S p a n i s h   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " d a n "   / > 	 	 < ! - -   D a n i s h   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " n l d "   / > 	 	 < ! - -   D u t c h   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " v l s "   / > 	 	 < ! - -   F l e m i s h   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " f r y "   / > 	 	 < ! - -   F r i s i a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " d e u "   / > 	 	 < ! - -   G e r m a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " n o r "   / > 	 	 < ! - -   N o r w e g i a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " s w e "   / > 	 	 < ! - -   S w e d i s h   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " y i d "   / > 	 	 < ! - -   Y i d d i s h   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " b o s "   / > 	 	 < ! - -   B o s n i a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " b u l "   / > 	 	 < ! - -   B u l g a r i a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " h r v "   / > 	 	 < ! - -   C r o a t i a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " c e s "   / > 	 	 < ! - -   C z e c h   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " m k d "   / > 	 	 < ! - -   M a c e d o n i a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " p o l "   / > 	 	 < ! - -   P o l i s h   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " p r g "   / > 	 	 < ! - -   R u s s i a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " s r p "   / > 	 	 < ! - -   S e r b i a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " h b s "   / > 	 	 < ! - -   S e r b o - C r o a t i a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " s l k "   / > 	 	 < ! - -   S l o v a k   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " s l v "   / > 	 	 < ! - -   S l o v e n i a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " u k r "   / > 	 	 < ! - -   U k r a i n i a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " l a v "   / > 	 	 < ! - -   L a t v i a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " l i t "   / > 	 	 < ! - -   L i t h u a n i a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " e s t "   / > 	 	 < ! - -   E s t o n i a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " f i n "   / > 	 	 < ! - -   F i n n i s h   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " h u n "   / > 	 	 < ! - -   H u n g a r i a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " e l l "   / > 	 	 < ! - -   G r e e k   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " h y e "   / > 	 	 < ! - -   A r m e n i a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " t u r "   / > 	 	 < ! - -   T u r k i s h   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " a m h "   / > 	 	 < ! - -   A m h a r i c   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " a r a "   / > 	 	 < ! - -   A r a b i c   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " h e b "   / > 	 	 < ! - -   H e b r e w   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " m l t "   / > 	 	 < ! - -   M a l t e s e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " s o m "   / > 	 	 < ! - -   S o m a l i   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " t i r "   / > 	 	 < ! - -   T i g r i n y a   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " b e n "   / > 	 	 < ! - -   B e n g a l i   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " g u j "   / > 	 	 < ! - -   G u j a r a t i   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " h i n "   / > 	 	 < ! - -   H i n d i   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " k u r "   / > 	 	 < ! - -   K u r d i s h   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " p a n "   / > 	 	 < ! - -   P a n j a b i   ( P u n j a b i )   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " p u s "   / > 	 	 < ! - -   P a s h t o   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " f a s "   / > 	 	 < ! - -   P e r s i a n   ( F a r s i )   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " s n d "   / > 	 	 < ! - -   S i n d h i   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " s i n "   / > 	 	 < ! - -   S i n h a l a   ( S i n h a l e s e )   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " u r d "   / > 	 	 < ! - -   U r d u   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " m a l "   / > 	 	 < ! - -   M a l a y a l a m   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " t a m "   / > 	 	 < ! - -   T a m i l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " t e l "   / > 	 	 < ! - -   T e l u g u   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " j p n "   / > 	 	 < ! - -   J a p a n e s e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " k o r "   / > 	 	 < ! - -   K o r e a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " y u e "   / > 	 	 < ! - -   C a n t o n e s e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " z h o "   / > 	 	 < ! - -   C h i n e s e ,   n . o . s .   ( 2 )   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " c m n "   / > 	 	 < ! - -   M a n d a r i n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " n a n "   / > 	 	 < ! - -   T a i w a n e s e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " l a o "   / > 	 	 < ! - -   L a o   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " k h m "   / > 	 	 < ! - -   K h m e r   ( C a m b o d i a n )   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " k h n "   / > 	 	 < ! - -   K h m e r   ( C a m b o d i a n )   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " v i e "   / > 	 	 < ! - -   V i e t n a m e s e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " b s b "   / > 	 	 < ! - -   B i s a y a n   l a n g u a g e s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " b s y "   / > 	 	 < ! - -   B i s a y a n   l a n g u a g e s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " i l o "   / > 	 	 < ! - -   i l o c a n o   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " z s m "   / > 	 	 < ! - -   M a l a y   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " t g l "   / > 	 	 < ! - -   T a g a l o g   ( P i l i p i n o ,   F i l i p i n o )   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " a k a "   / > 	 	 < ! - -   A k a n   ( T w i )   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " s w h "   / > 	 	 < ! - -   S w a h i l i   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " c r p "   / > 	 	 < ! - -   C r e o l e s   - - > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   L a n g u a g e   o f   S e r v i c e   P r o v i s i o n   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " s e r v i c e L a n g " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " e n g "   / > 	 	 < ! - -   E n g l i s h   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " f r a "   / > 	 	 < ! - -   F r e n c h   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " a l q "   / > 	 	 < ! - -   A l g o n q u i n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " a t j "   / > 	 	 < ! - -   A t i k a m e k w   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " b l a "   / > 	 	 < ! - -   B l a c k f o o t   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " c r x "   / > 	 	 < ! - -   C a r r i e r   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " c l c "   / > 	 	 < ! - -   C h i l c o t i n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " c h p "   / > 	 	 < ! - -   C h i p e w y a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " c r e "   / > 	 	 < ! - -   C r e e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " d a k "   / > 	 	 < ! - -   S i o u a n   l a n g u a g e s   ( D a k o t a , S i o u x )   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " a t h "   / > 	 	 < ! - -   A t h a p a s k a n   l a n g u a g e s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " d g r "   / > 	 	 < ! - -   D o g r i b   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " g i t "   / > 	 	 < ! - -   G i t k s a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " i k t "   / > 	 	 < ! - -   I n u i n n a q t u n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " i k u "   / > 	 	 < ! - -   I n u k t i t u t ,   n . i . e .   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " g w i "   / > 	 	 < ! - -   K u t c h i n - G w i c h   i n   ( L o u c h e u x )   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " p q m "   / > 	 	 < ! - -   M a l e c i t e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " m i c "   / > 	 	 < ! - -   M i   k m a q   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " m o h "   / > 	 	 < ! - -   M o h a w k   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " m o e "   / > 	 	 < ! - -   M o n t a g n a i s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " n k s "   / > 	 	 < ! - -   N a s k a p i   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " n c g "   / > 	 	 < ! - -   N i s g a   a   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " s c s "   / > 	 	 < ! - -   N o r t h   S l a v e   ( H a r e )   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " o j i "   / > 	 	 < ! - -   O j i b w a y   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " a l g "   / > 	 	 < ! - -   O j i - C r e e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " s h s "   / > 	 	 < ! - -   S h u s w a p   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " x s l "   / > 	 	 < ! - -   S o u t h   S l a v e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " t l i "   / > 	 	 < ! - -   T l i n g i t   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " i t a "   / > 	 	 < ! - -   I t a l i a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " p o r "   / > 	 	 < ! - -   P o r t u g u e s e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " r o n "   / > 	 	 < ! - -   R o m a n i a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " s p a "   / > 	 	 < ! - -   S p a n i s h   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " d a n "   / > 	 	 < ! - -   D a n i s h   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " n l d "   / > 	 	 < ! - -   D u t c h   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " v l s "   / > 	 	 < ! - -   F l e m i s h   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " f r y "   / > 	 	 < ! - -   F r i s i a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " d e u "   / > 	 	 < ! - -   G e r m a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " n o r "   / > 	 	 < ! - -   N o r w e g i a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " s w e "   / > 	 	 < ! - -   S w e d i s h   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " y i d "   / > 	 	 < ! - -   Y i d d i s h   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " b o s "   / > 	 	 < ! - -   B o s n i a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " b u l "   / > 	 	 < ! - -   B u l g a r i a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " h r v "   / > 	 	 < ! - -   C r o a t i a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " c e s "   / > 	 	 < ! - -   C z e c h   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " m k d "   / > 	 	 < ! - -   M a c e d o n i a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " p o l "   / > 	 	 < ! - -   P o l i s h   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " p r g "   / > 	 	 < ! - -   R u s s i a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " s r p "   / > 	 	 < ! - -   S e r b i a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " h b s "   / > 	 	 < ! - -   S e r b o - C r o a t i a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " s l k "   / > 	 	 < ! - -   S l o v a k   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " s l v "   / > 	 	 < ! - -   S l o v e n i a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " u k r "   / > 	 	 < ! - -   U k r a i n i a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " l a v "   / > 	 	 < ! - -   L a t v i a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " l i t "   / > 	 	 < ! - -   L i t h u a n i a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " e s t "   / > 	 	 < ! - -   E s t o n i a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " f i n "   / > 	 	 < ! - -   F i n n i s h   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " h u n "   / > 	 	 < ! - -   H u n g a r i a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " e l l "   / > 	 	 < ! - -   G r e e k   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " h y e "   / > 	 	 < ! - -   A r m e n i a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " t u r "   / > 	 	 < ! - -   T u r k i s h   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " a m h "   / > 	 	 < ! - -   A m h a r i c   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " a r a "   / > 	 	 < ! - -   A r a b i c   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " h e b "   / > 	 	 < ! - -   H e b r e w   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " m l t "   / > 	 	 < ! - -   M a l t e s e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " s o m "   / > 	 	 < ! - -   S o m a l i   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " t i r "   / > 	 	 < ! - -   T i g r i n y a   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " b e n "   / > 	 	 < ! - -   B e n g a l i   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " g u j "   / > 	 	 < ! - -   G u j a r a t i   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " h i n "   / > 	 	 < ! - -   H i n d i   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " k u r "   / > 	 	 < ! - -   K u r d i s h   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " p a n "   / > 	 	 < ! - -   P a n j a b i   ( P u n j a b i )   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " p u s "   / > 	 	 < ! - -   P a s h t o   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " f a s "   / > 	 	 < ! - -   P e r s i a n   ( F a r s i )   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " s n d "   / > 	 	 < ! - -   S i n d h i   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " s i n "   / > 	 	 < ! - -   S i n h a l a   ( S i n h a l e s e )   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " u r d "   / > 	 	 < ! - -   U r d u   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " m a l "   / > 	 	 < ! - -   M a l a y a l a m   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " t a m "   / > 	 	 < ! - -   T a m i l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " t e l "   / > 	 	 < ! - -   T e l u g u   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " j p n "   / > 	 	 < ! - -   J a p a n e s e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " k o r "   / > 	 	 < ! - -   K o r e a n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " y u e "   / > 	 	 < ! - -   C a n t o n e s e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " z h o "   / > 	 	 < ! - -   C h i n e s e ,   n . o . s .   ( 2 )   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " c m n "   / > 	 	 < ! - -   M a n d a r i n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " n a n "   / > 	 	 < ! - -   T a i w a n e s e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " l a o "   / > 	 	 < ! - -   L a o   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " k h m "   / > 	 	 < ! - -   K h m e r   ( C a m b o d i a n )   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " k h n "   / > 	 	 < ! - -   K h m e r   ( C a m b o d i a n )   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " v i e "   / > 	 	 < ! - -   V i e t n a m e s e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " b s b "   / > 	 	 < ! - -   B i s a y a n   l a n g u a g e s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " b s y "   / > 	 	 < ! - -   B i s a y a n   l a n g u a g e s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " i l o "   / > 	 	 < ! - -   i l o c a n o   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " z s m "   / > 	 	 < ! - -   M a l a y   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " t g l "   / > 	 	 < ! - -   T a g a l o g   ( P i l i p i n o ,   F i l i p i n o )   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " a k a "   / > 	 	 < ! - -   A k a n   ( T w i )   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " s w h "   / > 	 	 < ! - -   S w a h i l i   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " c r p "   / > 	 	 < ! - -   C r e o l e s   - - > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   D o   y o u   h a v e   a n y   l e g a l   i s s u e s ?   ( s e l e c t   o n e )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " l e g a l I s s u e s " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 	 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C i v i l "   / > 	 	 < ! - -   C i v i l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C r i m i n a l "   / > 	 < ! - -   C r i m i n a l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " N o n e "   / > 	 	 < ! - -   N o n e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " U N K "   / > 	 	 	 < ! - -   U n k n o w n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C D A "   / > 	 	 	 < ! - -   C l i e n t   d e c l i n e d   t o   a n s w e r   - - > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   L e g a l   S t a t u s   ( c h e c k   a l l   t h a t   a p p l y )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " l e g a l S t a t u s L i s t " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 	 	 	 	 	 < x s d : s e q u e n c e > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " l e g a l S t a t u s "   m i n O c c u r s = " 0 "   m a x O c c u r s = " u n b o u n d e d "   / > 
 
 	 	 	 	 	 < / x s d : s e q u e n c e > 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < x s d : e l e m e n t   n a m e = " l e g a l S t a t u s " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 3 - 0 1 "   / > 	 < ! - -   P r e - c h a r g e   D i v e r s i o n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 3 - 0 2 "   / > 	 < ! - -   C o u r t   D i v e r s i o n   P r o g r a m   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 3 - 0 3 "   / > 	 < ! - -   A w a i t i n g   f i t n e s s   a s s e s s m e n t   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 3 - 0 4 "   / > 	 < ! - -   A w a i t i n g   t r i a l   ( w i t h   o r   w i t h o u t   b a i l )   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 3 - 0 5 "   / > 	 < ! - -   A w a i t i n g   C r i m i n a l   R e s p o n s i b i l i t y   A s s e s s m e n t   ( N C R )   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 3 - 0 6 "   / > 	 < ! - -   I n   c o m m u n i t y   o n   o w n   r e c o g n i z a n c e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 3 - 0 7 "   / > 	 < ! - -   U n f i t   t o   s t a n d   t r i a l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 3 - 0 8 "   / > 	 < ! - -   C h a r g e s   w i t h d r a w n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 3 - 0 9 "   / > 	 < ! - -   S t a y   o f   p r o c e e d i n g s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 3 - 1 0 "   / > 	 < ! - -   A w a i t i n g   s e n t e n c e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 3 - 1 1 "   / > 	 < ! - -   N C R   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 3 - 1 2 "   / > 	 < ! - -   C o n d i t i o n a l   d i s c h a r g e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 3 - 1 3 "   / > 	 < ! - -   C o n d i t i o n a l   s e n t e n c e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 3 - 1 4 "   / > 	 < ! - -   R e s t r a i n i n g   o r d e r   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 3 - 1 5 "   / > 	 < ! - -   P e a c e   b o n d   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 3 - 1 6 "   / > 	 < ! - -   s u s p e n d e d   s e n t e n c e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 3 - 1 7 "   / > 	 < ! - -   O R B   d e t a i n e d   -   c o m m u n i t y   a c c e s s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 3 - 1 8 "   / > 	 < ! - -   O R B   c o n d i t i o n a l   d i s c h a r g e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 3 - 1 9 "   / > 	 < ! - -   O n   p a r o l e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 3 - 2 0 "   / > 	 < ! - -   O n   p r o b a t i o n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 3 - 2 1 "   / > 	 < ! - -   N o   l e g a l   p r o b l e m s   ( i n c l u d e s   a b s o l u t e   d i s c h a r g e   a n d   e n d   o f   s e n t e n c e )   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 3 - 2 4 "   / > 	 < ! - -   U n k n o w n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 3 - 2 5 "   / > 	 < ! - -   C l i e n t   D e c l i n e d   t o   A n s w e r   - - > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   E x i t   D i s p o s i t i o n ?   ( s e l e c t   o n e   i f   a p p l i c a b l e )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " e x i t D i s p o s i t i o n " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 9 - 0 1 "   / > 	 < ! - -   C o m p l e t i o n   w i t h o u t   r e f e r r a l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 9 - 0 2 "   / > 	 < ! - -   C o m p l e t i o n   w i t h   r e f e r r a l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 9 - 0 3 "   / > 	 < ! - -   S u i c i d e s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 9 - 0 4 "   / > 	 < ! - -   D e a t h   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 9 - 0 5 "   / > 	 < ! - -   R e l o c a t i o n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 9 - 0 6 "   / > 	 < ! - -   W i t h d r a w a l   - - > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < x s d : e l e m e n t   n a m e = " O C A N D o m a i n s " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 	 	 	 	 	 < x s d : s e q u e n c e > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " d o m a i n "   m a x O c c u r s = " u n b o u n d e d "   / > 
 
 	 	 	 	 	 < / x s d : s e q u e n c e > 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 	 < x s d : e l e m e n t   n a m e = " d o m a i n " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 
 
 	 	 	 	 	 < x s d : s e q u e n c e > 
 
 
 
 	 	 	 	 	 	 < ! - -   c o m m o n   f o r   a l l   d o m a i n s   - - > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " n e e d R a t i n g "   m i n O c c u r s = " 0 "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " i n f o r m a l H e l p R e c v d "   m i n O c c u r s = " 0 "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " f o r m a l H e l p R e c v d "   m i n O c c u r s = " 0 "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " f o r m a l H e l p N e e d "   m i n O c c u r s = " 0 "   / > 
 
 
 
 	 	 	 	 	 	 < ! - -   0 1 :   a c c o m m o d a t i o n   - - > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " r e s i d e n c e T y p e "   m i n O c c u r s = " 0 "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " r e s i d e n c e S u p p o r t "   m i n O c c u r s = " 0 "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " l i v i n g A r r a n g e m e n t T y p e "   m i n O c c u r s = " 0 "   / > 
 
 
 
 	 	 	 	 	 	 < ! - -   0 5 :   d a y t i m e   a c t i v i t i e s   - - > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " e m p l o y S t a t u s "   m i n O c c u r s = " 0 "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " e d u c a t i o n P r o g r a m S t a t u s "   m i n O c c u r s = " 0 "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " r i s k U n e m p l o y m e n t L i s t "   m i n O c c u r s = " 0 "   / > 
 
 
 
 	 	 	 	 	 	 < ! - -   0 6 :   p h y s i c a l   h e a l t h   - - > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " m e d i c a l C o n d i t i o n L i s t "   m i n O c c u r s = " 0 "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " p h y s i c a l H e a l t h C o n c e r n "   m i n O c c u r s = " 0 "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " c o n c e r n A r e a L i s t "   m i n O c c u r s = " 0 "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " m e d i c a t i o n L i s t "   m i n O c c u r s = " 0 "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " s i d e E f f e c t s "   m i n O c c u r s = " 0 "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " d a i l y L i v i n g A f f e c t e d "   m i n O c c u r s = " 0 "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " s i d e E f f e c t s D e t a i l L i s t "   m i n O c c u r s = " 0 "   / > 
 
 
 
 	 	 	 	 	 	 < ! - -   0 7 :   p s y c h o t i c   s y m p t o m s   - - > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " h o s p i t a l i z e d P a s t T w o Y e a r s "   m i n O c c u r s = " 0 "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " t o t a l A d m i s s i o n s "   m i n O c c u r s = " 0 "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " t o t a l H o s p i t a l D a y s "   m i n O c c u r s = " 0 "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " c o m m u n i t y T r e a t O r d e r "   m i n O c c u r s = " 0 "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " s y m p t o m L i s t "   m i n O c c u r s = " 0 "   / > 
 
 
 
 	 	 	 	 	 	 < ! - -   0 8 :   c o n d i t i o n   a n d   t r e a t m e n t   - - > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " d i a g n o s t i c L i s t "   m i n O c c u r s = " 0 "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " o t h e r I l l n e s s L i s t "   m i n O c c u r s = " 0 "   / > 
 
 
 
 	 	 	 	 	 	 < ! - -   1 0 :   s a f e t y   t o   s e l f   - - > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " s u i c i d e A t t e m p t "   m i n O c c u r s = " 0 "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " s u i c i d e T h o u g h t s "   m i n O c c u r s = " 0 "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " s a f e t y C o n c e r n S e l f "   m i n O c c u r s = " 0 "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " s a f e t y T o S e l f R i s k L i s t "   m i n O c c u r s = " 0 "   / > 
 
 
 
 	 	 	 	 	 	 < ! - -   1 2 :   a l c o h o l   - - > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " d r i n k A l c o h o l "   m i n O c c u r s = " 0 "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " s t a g e O f C h a n g e A l c o h o l "   m i n O c c u r s = " 0 "   / > 
 
 
 
 	 	 	 	 	 	 < ! - -   1 3 :   d r u g s   - - > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " d r u g U s e L i s t "   m i n O c c u r s = " 0 "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " s t a g e O f C h a n g e D r u g s "   m i n O c c u r s = " 0 "   / > 
 
 
 
 	 	 	 	 	 	 < ! - -   1 4 :   o t h e r   a d d i c t i o n s   - - > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " a d d i c t i o n T y p e L i s t "   m i n O c c u r s = " 0 "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " s t a g e O f C h a n g e A d d i c t i o n s "   m i n O c c u r s = " 0 "   / > 
 
 
 
 	 	 	 	 	 	 < ! - -   1 5 :   c o m p a n y   - - > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " c h a n g e d S o c i a l P a t t e r n s "   m i n O c c u r s = " 0 "   / > 
 
 
 
 	 	 	 	 	 	 < ! - -   2 0 :   b a s i c   e d u c a t i o n   - - > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " h i g h e s t E d u c a t i o n L e v e l "   m i n O c c u r s = " 0 "   / > 
 
 
 
 	 	 	 	 	 	 < ! - -   2 3 :   m o n e y   - - > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " s o u r c e O f I n c o m e "   m i n O c c u r s = " 0 "   / > 
 
 
 
 	 	 	 	 	 < / x s d : s e q u e n c e > 
 
 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " n a m e "   u s e = " r e q u i r e d " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 - a c c o m m o d a t i o n "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 2 - f o o d "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 3 - l o o k i n g   a f t e r   t h e   h o m e "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 4 - s e l f - c a r e "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 5 - d a y t i m e   a c t i v i t i e s "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 6 - p h y s i c a l   h e a l t h "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 7 - p s y c h o t i c   s y m p t o m s "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 8 - c o n d i t i o n   a n d   t r e a t m e n t "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 9 - p s y c h o l o g i c a l   d i s t r e s s "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 0 - s a f e t y   t o   s e l f "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 1 - s a f e t y   t o   o t h e r s "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 2 - a l c o h o l "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 3 - d r u g s "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 4 - o t h e r   a d d i c t i o n s "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 5 - c o m p a n y "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 6 - i n t i m a t e   r e l a t i o n s h i p s "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 7 - s e x u a l   e x p r e s s i o n "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 8 - c h i l d c a r e "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 9 - o t h e r   d e p e n d e n t s "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 0 - b a s i c   e d u c a t i o n "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 1 - t e l e p h o n e "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 2 - t r a n s p o r t "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 3 - m o n e y "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 4 - b e n e f i t s "   / > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   c o m m o n   f o r   a l l   d o m a i n s   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " n e e d R a t i n g " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 
 
 	 	 	 	 	 < x s d : s e q u e n c e   / > 
 
 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " s t a f f "   u s e = " r e q u i r e d " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : b y t e " > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 "   / > 	 < ! - -   N o   p r o b l e m   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 "   / > 	 < ! - -   N o   o r   m o d e r a t e   p r o b l e m   d u e   t o   h e l p   g i v e n   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 "   / > 	 < ! - -   S e r i o u s   p r o b l e m   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 9 "   / > 	 < ! - -   N o t   k n o w n   - - > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " c l i e n t "   u s e = " r e q u i r e d " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : b y t e " > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " - 1 "   / > 	 < ! - -   B l a n k   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 "   / > 	 < ! - -   N o   p r o b l e m   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 "   / > 	 < ! - -   N o   o r   m o d e r a t e   p r o b l e m   d u e   t o   h e l p   g i v e n   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 "   / > 	 < ! - -   S e r i o u s   p r o b l e m   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 9 "   / > 	 < ! - -   N o t   k n o w n   - - > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   c o m m o n   f o r   a l l   d o m a i n s   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " i n f o r m a l H e l p R e c v d " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 	 	 	 	 	 < x s d : s e q u e n c e   / > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " s t a f f "   u s e = " r e q u i r e d " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : b y t e " > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " - 1 "   / > 	 < ! - -   B l a n k   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 "   / > 	 < ! - -   N o n e   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 "   / > 	 < ! - -   L o w   H e l p   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 "   / > 	 < ! - -   M o d e r a t e   H e l p   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 "   / > 	 < ! - -   H i g h   H e l p   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 9 "   / > 	 < ! - -   N o t   k n o w n   - - > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   c o m m o n   f o r   a l l   d o m a i n s   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " f o r m a l H e l p R e c v d " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 	 	 	 	 	 < x s d : s e q u e n c e   / > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " s t a f f "   u s e = " r e q u i r e d " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : b y t e " > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " - 1 "   / > 	 < ! - -   B l a n k   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 "   / > 	 < ! - -   N o n e   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 "   / > 	 < ! - -   L o w   H e l p   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 "   / > 	 < ! - -   M o d e r a t e   H e l p   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 "   / > 	 < ! - -   H i g h   H e l p   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 9 "   / > 	 < ! - -   N o t   k n o w n   - - > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   c o m m o n   f o r   a l l   d o m a i n s   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " f o r m a l H e l p N e e d " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 	 	 	 	 	 < x s d : s e q u e n c e   / > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " s t a f f "   u s e = " r e q u i r e d " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : b y t e " > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " - 1 "   / > 	 < ! - -   B l a n k   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 "   / > 	 < ! - -   N o n e   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 "   / > 	 < ! - -   L o w   H e l p   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 "   / > 	 < ! - -   M o d e r a t e   H e l p   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 "   / > 	 < ! - -   H i g h   H e l p   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 9 "   / > 	 < ! - -   N o t   k n o w n   - - > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   0 1 :   a c c o m m o d a t i o n :   W h e r e   d o   y o u   l i v e ?   ( s e l e c t   o n e )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " r e s i d e n c e T y p e " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 2 4 - 0 1 "   / > 	 < ! - -   A p p r o v e d   H o m e s   a n d   H o m e s   f o r   S p e c i a l   C a r e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 2 4 - 0 2 "   / > 	 < ! - -   C o r r e c t i o n a l ,   P r o b a t i o n   F a c i l i t y   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 2 4 - 0 3 "   / > 	 < ! - -   D o m i c i l l a r y   H o s t e l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 2 4 - 0 4 "   / > 	 < ! - -   G e n e r a l   H o s p i t a l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 2 4 - 0 5 "   / > 	 < ! - -   P s y c h i a t r i c   H o s p i t a l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 2 4 - 0 6 "   / > 	 < ! - -   O t h e r   S p e c i a l t y   H o s p i t a l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 2 4 - 0 7 "   / > 	 < ! - -   N o   f i x e d   a d d r e s s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 2 4 - 0 8 "   / > 	 < ! - -   H o s t e l ,   S h e l t e r   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 2 4 - 0 9 "   / > 	 < ! - -   L o n g   t e r m   c a r e   f a c i l i t y ,   N u r s i n g   H o m e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 2 4 - 1 0 "   / > 	 < ! - -   M u n i c i p a l   N o n - P r o f i t   H o u s i n g   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 2 4 - 1 1 "   / > 	 < ! - -   P r i v a t e   N o n - P r o f i t   H o u s i n g   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 2 4 - 1 2 "   / > 	 < ! - -   P r i v a t e   H o u s e ,   A p t .      S R   O w n e d ,   M a r k e t   R e n t   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 2 4 - 1 3 "   / > 	 < ! - -   P r i v a t e   H o u s e ,   A p t .      O t h e r ,   S u b s i d i z e d   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 2 4 - 1 4 "   / > 	 < ! - -   R e t i r e m e n t   H o m e ,   S e n i o r ' s   R e s i d e n c e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 2 4 - 1 5 "   / > 	 < ! - -   R o o m i n g ,   B o a r d i n g   H o u s e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 2 4 - 1 6 "   / > 	 < ! - -   S u p p o r t i v e   H o u s i n g      C o n g r e g a t e   L i v i n g   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 2 4 - 1 7 "   / > 	 < ! - -   S u p p o r t i v e   H o u s i n g   -   A s s i s t e d   L i v i n g   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 2 4 - 1 8 "   / > 	 < ! - -   O t h e r   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 2 4 - 1 9 "   / > 	 < ! - -   U n k n o w n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 2 4 - 2 0 "   / > 	 < ! - -   C l i e n t   D e c l i n e d   t o   A n s w e r   - - > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   0 1 :   a c c o m m o d a t i o n :   D o   y o u   r e c e i v e   a n y   s u p p o r t ?   ( s e l e c t   o n e )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " r e s i d e n c e S u p p o r t " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 4 A - 0 1 "   / > 	 < ! - -   I n d e p e n d e n t   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 4 A - 0 2 "   / > 	 < ! - -   A s s i s t e d ,   S u p p o r t e d   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 4 A - 0 3 "   / > 	 < ! - -   S u p e r v i s e d   N o n f a c i l i t y   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 4 A - 0 4 "   / > 	 < ! - -   S u p e r v i s e d - F a c i l i t y   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 4 A - 0 5 "   / > 	 < ! - -   U n k n o w n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 4 A - 0 6 "   / > 	 < ! - -   C l i e n t   D e c l i n e d   t o   A n s w e r   - - > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   0 1 :   a c c o m m o d a t i o n :   D o   y o u   l i v e   w i t h   a n y o n e ?   ( s e l e c t   o n e )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " l i v i n g A r r a n g e m e n t T y p e " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 2 3 - 0 1 "   / > 	 < ! - -   S e l f   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 2 3 - 0 2 "   / > 	 < ! - -   S p o u s e ,   P a r t n e r   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 2 3 - 0 3 "   / > 	 < ! - -   S p o u s e ,   P a r t n e r   a n d   O t h e r s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 2 3 - 0 4 "   / > 	 < ! - -   C h i l d r e n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 2 3 - 0 5 "   / > 	 < ! - -   P a r e n t s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 2 3 - 0 6 "   / > 	 < ! - -   R e l a t i v e s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 2 3 - 0 7 "   / > 	 < ! - -   N o n - r e l a t i v e s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 2 3 - 0 8 "   / > 	 < ! - -   U n k n o w n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 2 3 - 0 9 "   / > 	 < ! - -   C l i e n t   D e c l i n e d   t o   A n s w e r   - - > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   0 5 :   d a y t i m e   a c t i v i t i e s :   W h a t   i s   y o u r   c u r r e n t   e m p l o y m e n t   s t a t u s ?   ( s e l e c t   o n e )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " e m p l o y S t a t u s " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 2 4 3 6 3 0 0 7 "   / > 	 < ! - -   I n d e p e n d e n t ,   C o m p e t i t i v e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " E S - 1 "   / > 	 	 < ! - -   A s s i s t e d ,   S u p p o r t e d   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " E S - 2 "   / > 	 	 < ! - -   A l t e r n a t i v e   b u s i n e s s e s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 2 4 3 6 6 0 0 4 "   / > 	 < ! - -   S h e l t e r e d   W o r k s h o p   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 7 6 0 6 1 0 0 3 "   / > 	 < ! - -   N o n - p a i d   w o r k   e x p e r i e n c e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " E S - 3 "   / > 	 	 < ! - -   N o   e m p l o y m e n t   -   o t h e r   a c t i v i t y   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 2 4 3 6 4 0 0 1 "   / > 	 < ! - -   C a s u a l ,   S p o r a d i c   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 7 3 4 3 8 0 0 4 "   / > 	 < ! - -   N o   e m p l o y m e n t   -   o f   a n y   k i n d   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 6 1 6 6 5 0 0 6 "   / > 	 < ! - -   U n k n o w n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C D A "   / > 	 	 	 < ! - -   C l i e n t   D e c l i n e d   t o   A n s w e r   - - > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   0 5 :   d a y t i m e   a c t i v i t i e s :   A r e   y o u   c u r r e n t l y   i n   s c h o o l ?   ( s e l e c t   o n e )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " e d u c a t i o n P r o g r a m S t a t u s " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 2 4 3 0 4 0 0 4 "   / > 	 	 < ! - -   N o t   i n   s c h o o l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 2 4 3 0 6 0 0 2 "   / > 	 	 < ! - -   E l e m e n t a r y ,   J u n i o r   H i g h   S c h o o l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 2 4 3 0 8 0 0 1 "   / > 	 	 < ! - -   S e c o n d a r y ,   H i g h   S c h o o l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 2 4 8 6 0 0 0 3 "   / > 	 	 < ! - -   T r a d e   S c h o o l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 5 4 1 0 6 0 0 8 "   / > 	 	 < ! - -   V o c a t i o n a l , T r a i n i n g   C e n t r e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 6 1 1 2 5 0 0 7 "   / > 	 	 < ! - -   A d u l t   e d u c a t i o n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 2 4 8 7 0 0 0 1 "   / > 	 	 < ! - -   C o m m u n i t y   C o l l e g e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 2 4 8 7 1 0 0 2 "   / > 	 	 < ! - -   U n i v e r s i t y   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 4 1 0 5 1 5 0 0 3 "   / > 	 	 < ! - -   O t h e r   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 6 1 6 6 5 0 0 6 "   / > 	 	 < ! - -   U n k n o w n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C D A "   / > 	 	 	 	 < ! - -   C l i e n t   D e c l i n e d   t o   A n s w e r   - - > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   0 5 :   d a y t i m e   a c t i v i t i e s :   A r e   y o u   a t   r i s k   o f   u n e m p l o y m e n t   o r   d i s r u p t e d   e d u c a t i o n ?   ( c h e c k   a l l   t h a t   a p p l y )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " r i s k U n e m p l o y m e n t L i s t " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 	 	 	 	 	 < x s d : s e q u e n c e > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " r i s k U n e m p l o y m e n t "   m a x O c c u r s = " u n b o u n d e d "   m i n O c c u r s = " 0 "   / > 
 
 	 	 	 	 	 < / x s d : s e q u e n c e > 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < x s d : e l e m e n t   n a m e = " r i s k U n e m p l o y m e n t " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 	 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " U D E R - 0 1 "   / > 	 	 < ! - -   D i f f i c u l t y   i n   g e t t i n g   t o   w o r k ,   s c h o o l   o n   t i m e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " U D E R - 0 2 "   / > 	 	 < ! - -   P r o b l e m s ,   d i f f i c u l t y   i n   w o r k ,   s c h o o l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " U D E R - 0 3 "   / > 	 	 < ! - -   L o o k i n g   t o   q u i t   w o r k ,   s c h o o l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " U D E R - 0 4 "   / > 	 	 < ! - -   F r e q u e n t   c h a n g e s   i n   w o r k ,   s c h o o l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " U D E R - 0 5 "   / > 	 	 < ! - -   N o n e   o r   n o t   a p p l i c a b l e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " U D E R - 0 6 "   / > 	 	 < ! - -   U n k n o w n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " U D E R - 0 7 "   / > 	 	 < ! - -   C l i e n t   d e c l i n e d   t o   a n s w e r   - - > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   0 6 :   p h y s i c a l   h e a l t h :   M e d i c a l   c o n d i t i o n s   ( c h e c k   a l l   t h a t   a p p l y )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " m e d i c a l C o n d i t i o n L i s t " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 	 	 	 	 	 < x s d : s e q u e n c e > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " m e d i c a l C o n d i t i o n "   m a x O c c u r s = " u n b o u n d e d "   m i n O c c u r s = " 0 "   / > 
 
 	 	 	 	 	 < / x s d : s e q u e n c e > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " a u t i s m D e t a i l "   t y p e = " x s d : s t r i n g "   u s e = " o p t i o n a l "   / > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " o t h e r D e t a i l "   t y p e = " x s d : s t r i n g "   u s e = " o p t i o n a l "   / > 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < x s d : e l e m e n t   n a m e = " m e d i c a l C o n d i t i o n " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 	 	 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 2 7 2 9 4 0 0 3 "   / > 	 	 < ! - -   A c q u i r e d   B r a i n   I n j u r y   ( A B I )   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 7 2 3 0 0 1 "   / > 	 	 	 < ! - -   A r t h r i t i s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 4 0 8 8 5 6 0 0 3 "   / > 	 	 < ! - -   A u t i s m   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 8 6 8 1 3 0 0 2 "   / > 	 	 < ! - -   B r e a t h i n g   p r o b l e m s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 6 3 3 4 6 0 0 0 "   / > 	 	 < ! - -   C a n c e r   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 9 9 4 3 0 0 7 "   / > 	 	 < ! - -   C i r r h o s i s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 9 1 4 1 5 0 0 2 "   / > 	 	 < ! - -   C o m m u n i c a b l e   h e a l t h   d i s e a s e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 4 6 6 3 5 0 0 9 "   / > 	 	 < ! - -   D i a b e t e s   T y p e   1   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 4 4 0 5 4 0 0 6 "   / > 	 	 < ! - -   D i a b e t e s   T y p e   2   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 5 9 9 3 9 0 0 9 "   / > 	 	 < ! - -   D i a b e t e s   T y p e   3   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 7 3 2 1 1 0 0 9 "   / > 	 	 < ! - -   D i a b e t e s   O t h e r   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 7 2 3 6 6 0 0 4 "   / > 	 	 < ! - -   E a t i n g   d i s o r d e r   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 8 4 7 5 7 0 0 9 "   / > 	 	 < ! - -   E p i l e p s y   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 5 1 8 8 0 0 1 "   / > 	 	 < ! - -   H e a r i n g   i m p a i r m e n t   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 0 1 0 9 5 0 0 5 "   / > 	 	 < ! - -   H e a r t   c o n d i t i o n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 4 0 4 6 8 0 0 3 "   / > 	 	 < ! - -   H e p a t i t i s   A   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 6 6 0 7 1 0 0 2 "   / > 	 	 < ! - -   H e p a t i t i s   B   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 5 0 7 1 1 0 0 7 "   / > 	 	 < ! - -   H e p a t i t i s   C   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 8 6 4 0 6 0 0 8 "   / > 	 	 < ! - -   H I V   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 8 3 4 1 0 0 3 "   / > 	 	 < ! - -   H i g h   b l o o d   p r e s s u r e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 3 6 4 4 0 0 9 "   / > 	 	 < ! - -   H i g h   c h o l e s t e r o l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 2 8 1 5 6 0 0 7 "   / > 	 	 < ! - -   I n t e l l e c t u a l   d i s a b i l i t y   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 4 5 0 0 7 0 0 3 "   / > 	 	 < ! - -   L o w   b l o o d   p r e s s u r e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 8 2 0 2 8 0 0 1 "   / > 	 	 < ! - -   M R S A ,   C   D i f f i c i l e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 4 1 4 9 1 6 0 0 1 "   / > 	 	 < ! - -   O b e s i t y   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 6 4 8 5 9 0 0 6 "   / > 	 	 < ! - -   O s t e o p o r o s i s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 1 8 1 8 5 0 0 1 "   / > 	 	 < ! - -   P r e g n a n c y   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 2 8 6 1 3 0 0 2 "   / > 	 	 < ! - -   S e i z u r e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 8 0 9 8 0 0 9 "   / > 	 	 	 < ! - -   S e x u a l l y   T r a n s m i t t e d   D i s e a s e   ( S T D )   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 9 5 3 2 0 0 0 5 "   / > 	 	 < ! - -   S k i n   c o n d i t i o n s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 3 0 6 9 0 0 0 7 "   / > 	 	 < ! - -   S t r o k e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 4 3 0 4 0 0 0 "   / > 	 	 < ! - -   T h y r o i d   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 9 7 5 4 0 0 0 3 "   / > 	 	 < ! - -   V i s i o n   i m p a i r m e n t   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 4 1 0 5 1 5 0 0 3 "   / > 	 	 < ! - -   O t h e r   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 6 1 6 6 5 0 0 6 "   / > 	 	 < ! - -   U n k n o w n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C D A "   / > 	 	 	 	 < ! - -   C l i e n t   d e c l i n e d   t o   a n s w e r   - - > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   0 6 :   p h y s i c a l   h e a l t h :   D o   y o u   h a v e   a n y   c o n c e r n s   a b o u t   y o u r   p h y s i c a l   h e a l t h ?   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " p h y s i c a l H e a l t h C o n c e r n " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " T R U E "   / > 	 < ! - -   Y e s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " F A L S E "   / > 	 < ! - -   N o   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " U N K "   / > 	 	 < ! - -   U n k n o w n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C D A "   / > 	 	 < ! - -   C l i e n t   d e c l i n e d   t o   a n s w e r   - - > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   0 6 :   p h y s i c a l   h e a l t h :   I f   Y e s ,   p l e a s e   i n d i c a t e   t h e   a r e a s   w h e r e   y o u   h a v e   c o n c e r n s   ( c h e c k   a l l   t h a t   a p p l y )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " c o n c e r n A r e a L i s t " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 	 	 	 	 	 < x s d : s e q u e n c e > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " c o n c e r n A r e a "   m a x O c c u r s = " u n b o u n d e d "   m i n O c c u r s = " 0 "   / > 
 
 	 	 	 	 	 < / x s d : s e q u e n c e > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " o t h e r C o n c e r n A r e a "   t y p e = " x s d : s t r i n g "   u s e = " o p t i o n a l "   / > 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < x s d : e l e m e n t   n a m e = " c o n c e r n A r e a " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 	 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 1 8 2 5 4 0 0 2 "   / > 	 < ! - -   H e a d   a n d   n e c k   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 7 9 0 8 4 0 0 9 "   / > 	 < ! - -   C h e s t   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 1 9 4 1 5 0 0 7 "   / > 	 < ! - -   A b d o m e n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 0 2 2 9 3 0 0 8 "   / > 	 < ! - -   E x t r e m i t i e s   ( a r m s ,   l e g s ,   h a n d s ,   f e e t )   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 0 0 4 7 9 0 0 8 "   / > 	 < ! - -   G e n i t a l ,   u r i n a r y   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 0 6 0 7 6 0 0 1 "   / > 	 < ! - -   S k i n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 1 8 9 5 2 0 0 5 "   / > 	 < ! - -   J o i n t s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 6 5 0 9 2 0 0 5 "   / > 	 < ! - -   M o b i l i t y   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 0 2 9 5 7 0 0 3 "   / > 	 < ! - -   N e u r o l o g i c a l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 1 8 2 3 0 0 0 7 "   / > 	 < ! - -   H e a r i n g   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 1 8 2 3 5 0 0 2 "   / > 	 < ! - -   V i s i o n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 4 1 0 5 1 5 0 0 3 "   / > 	 < ! - -   O t h e r   - - > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   0 6 :   p h y s i c a l   h e a l t h :   L i s t   o f   a l l   c u r r e n t   m e d i c a t i o n s   ( i n c l u d i n g   p r e s c r i b e d   a n d   a l t e r n a t i v e / o v e r   t h e   c o u n t e r   m e d i c a t i o n )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " m e d i c a t i o n L i s t " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 	 	 	 	 	 < x s d : s e q u e n c e > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " m e d i c a t i o n D e t a i l "   m a x O c c u r s = " u n b o u n d e d "   m i n O c c u r s = " 0 "   / > 
 
 	 	 	 	 	 < / x s d : s e q u e n c e > 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < x s d : e l e m e n t   n a m e = " m e d i c a t i o n D e t a i l " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 
 
 	 	 	 	 	 < x s d : s e q u e n c e   / > 
 
 
 
 	 	 	 	 	 < ! - -   T a k e n   a s   p r e s c r i b e d ?   - - > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " t a k e n A s P r e s c r i b e d "   u s e = " o p t i o n a l " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " T R U E "   / > 	 < ! - -   Y e s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " F A L S E "   / > 	 < ! - -   N o   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " U N K "   / > 	 	 < ! - -   U n k n o w n   - - > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 	 < ! - -   H e l p   i s   p r o v i d e d ?   - - > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " i s H e l p P r o v i d e d "   u s e = " o p t i o n a l " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " T R U E "   / > 	 < ! - -   Y e s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " F A L S E "   / > 	 < ! - -   N o   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " U N K "   / > 	 	 < ! - -   U n k n o w n   - - > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 	 < ! - -   H e l p   i s   n e e d e d ?   - - > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " i s H e l p N e e d e d "   u s e = " o p t i o n a l " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " T R U E "   / > 	 < ! - -   Y e s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " F A L S E "   / > 	 < ! - -   N o   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " U N K "   / > 	 	 < ! - -   U n k n o w n   - - > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   0 6 :   p h y s i c a l   h e a l t h :   R e p o r t s   s i d e   e f f e c t s ?   ( s e l e c t   o n e )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " s i d e E f f e c t s " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " T R U E "   / > 	 < ! - -   Y e s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " F A L S E "   / > 	 < ! - -   N o   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " U N K "   / > 	 	 < ! - -   U n k n o w n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C D A "   / > 	 	 < ! - -   C l i e n t   d e c l i n e d   t o   a n s w e r   - - > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   0 6 :   p h y s i c a l   h e a l t h :   D o   t h e s e   s i d e   e f f e c t s   a f f e c t   y o u r   d a i l y   l i v i n g ?   ( s e l e c t   o n e )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " d a i l y L i v i n g A f f e c t e d " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " T R U E "   / > 	 < ! - -   Y e s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " F A L S E "   / > 	 < ! - -   N o   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " U N K "   / > 	 	 < ! - -   U n k n o w n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C D A "   / > 	 	 < ! - -   C l i e n t   d e c l i n e d   t o   a n s w e r   - - > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   0 6 :   p h y s i c a l   h e a l t h :   D e s c r i p t i o n   o f   s i d e   e f f e c t s   ( c h e c k   a l l   t h a t   a p p l y )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " s i d e E f f e c t s D e t a i l L i s t " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 	 	 	 	 	 < x s d : s e q u e n c e > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " s i d e E f f e c t s D e t a i l "   m a x O c c u r s = " u n b o u n d e d "   m i n O c c u r s = " 0 "   / > 
 
 	 	 	 	 	 < / x s d : s e q u e n c e > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " o t h e r S i d e E f f e c t s D e t a i l "   t y p e = " x s d : s t r i n g "   u s e = " o p t i o n a l "   / > 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < x s d : e l e m e n t   n a m e = " s i d e E f f e c t s D e t a i l " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 	 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 4 1 0 5 1 6 0 0 2 "   / > 	 < ! - -   N o n e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 4 6 6 3 6 0 0 8 "   / > 	 < ! - -   B l u r r e d ,   d i m m e d   v i s i o n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 4 9 4 7 3 0 0 4 "   / > 	 < ! - -   C h a n g e s   i n   a p p e t i t e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 4 0 4 6 4 0 0 0 3 "   / > 	 < ! - -   D i z z i n e s s ,   s p i n n i n g   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 7 1 7 8 2 0 0 1 "   / > 	 < ! - -   D r o w s i n e s s ,   s e d a t i o n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 8 7 7 1 5 0 0 8 "   / > 	 < ! - -   D r y   m o u t h   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 8 4 2 2 9 0 0 1 "   / > 	 < ! - -   F a t i g u e ,   w e a k n e s s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 6 2 8 5 0 0 3 "   / > 	 	 < ! - -   F a s t   h e a r t   b e a t   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 5 3 6 1 9 0 0 0 "   / > 	 < ! - -   G a s t r o i n t e s t i n a l   d i s t r e s s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 5 0 6 4 0 0 2 "   / > 	 < ! - -   H e a d a c h e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 9 3 4 6 2 0 0 1 "   / > 	 < ! - -   I n s o m n i a   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 0 6 0 0 2 0 0 0 "   / > 	 < ! - -   M e n s t r u a l   c h a n g e s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 7 1 9 4 1 0 0 7 "   / > 	 < ! - -   M i l k y   d i s c h a r g e   f r o m   b r e a s t s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 0 6 0 3 0 0 0 0 "   / > 	 < ! - -   M u s c l e   s p a s m s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 4 4 0 7 7 0 0 6 "   / > 	 < ! - -   N u m b n e s s ,   t i n g l i n g   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 4 1 9 9 0 0 5 "   / > 	 < ! - -   R e s t l e s s n e s s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 0 6 1 4 2 0 0 7 "   / > 	 < ! - -   S e x u a l   d i s t u r b a n c e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 6 0 7 9 0 0 4 "   / > 	 < ! - -   T r e m o r s ,   r i g i d i t y ,   b a l a n c e   p r o b l e m s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 8 9 4 3 0 0 2 "   / > 	 	 < ! - -   W e i g h t   g a i n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 4 1 0 5 1 5 0 0 3 "   / > 	 < ! - -   O t h e r   - - > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   0 7 :   p s y c h o t i c   s y m p t o m s :   H a v e   y o u   b e e n   h o s p i t a l i z e d   d u e   t o   y o u r   m e n t a l   h e a l t h   d u r i n g   t h e   p a s t   t w o   y e a r s ?   ( S e l e c t   o n e )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " h o s p i t a l i z e d P a s t T w o Y e a r s " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " T R U E "   / > 	 < ! - -   Y e s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " F A L S E "   / > 	 < ! - -   N o   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " U N K "   / > 	 	 < ! - -   U n k n o w n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C D A "   / > 	 	 < ! - -   C l i e n t   d e c l i n e d   t o   a n s w e r   - - > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   0 7 :   p s y c h o t i c   s y m p t o m s :   T o t a l   N u m b e r   o f   A d m i s s i o n s   ( l a s t   t w o   y e a r s )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " t o t a l A d m i s s i o n s "   t y p e = " x s d : i n t e g e r "   / > 
 
 
 
 	 < ! - -   0 7 :   p s y c h o t i c   s y m p t o m s :   T o t a l   N u m b e r   o f   H o s p i t a l i z a t i o n   D a y s   ( l a s t   t w o   y e a r s )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " t o t a l H o s p i t a l D a y s "   t y p e = " x s d : i n t e g e r "   / > 
 
 
 
 	 < ! - -   0 7 :   p s y c h o t i c   s y m p t o m s :   C o m m u n i t y   T r e a t m e n t   O r d e r   ( C D S )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " c o m m u n i t y T r e a t O r d e r " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 5 - 0 1 "   / > 	 < ! - -   I s s u e d   C T O   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 5 - 0 2 "   / > 	 < ! - -   N o   C T O   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 5 - 0 3 "   / > 	 < ! - -   U n k n o w n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 5 - 0 4 "   / > 	 < ! - -   C l i e n t   D e c l i n e d   t o   A n s w e r   - - > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   0 7 :   p s y c h o t i c   s y m p t o m s :   S y m p t o m   c h e c k l i s t   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " s y m p t o m L i s t " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 	 	 	 	 	 < x s d : s e q u e n c e > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " s y m p t o m "   m a x O c c u r s = " u n b o u n d e d "   m i n O c c u r s = " 0 "   / > 
 
 	 	 	 	 	 < / x s d : s e q u e n c e > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " o t h e r S y m p t o m "   t y p e = " x s d : s t r i n g "   u s e = " o p t i o n a l "   / > 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < x s d : e l e m e n t   n a m e = " s y m p t o m " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 	 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 4 1 0 5 1 6 0 0 2 "   / > 	 < ! - -   N o n e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 4 1 6 3 8 3 0 0 8 "   / > 	 < ! - -   A b n o r m a l   a f f e c t   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 7 8 6 3 3 0 0 1 "   / > 	 < ! - -   A b n o r m a l   t h o u g h t   p r o c e s s , f o r m   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 7 5 4 0 8 0 0 8 "   / > 	 < ! - -   A n g e r   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 4 8 6 9 4 0 0 2 "   / > 	 < ! - -   A n x i e t y   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 7 8 5 9 5 0 0 2 "   / > 	 < ! - -   C o m m a n d   h a l l u c i n a t i o n s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 2 4 7 9 0 0 6 "   / > 	 < ! - -   C o m p u l s i v e   b e h a v i o u r   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 4 8 2 7 4 0 0 2 "   / > 	 < ! - -   D e c r e a s e d   e n e r g y   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 0 7 3 0 0 0 "   / > 	 	 < ! - -   D e l u s i o n s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 2 5 6 2 4 0 0 0 "   / > 	 < ! - -   E p i s o d e s   o f   p a n i c   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 4 0 2 0 0 1 "   / > 	 	 < ! - -   F e a r s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 7 5 7 1 0 0 3 "   / > 	 	 < ! - -   G u i l t ,   s h a m e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 7 0 1 1 0 0 1 "   / > 	 	 < ! - -   H a l l u c i n a t i o n s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 0 7 0 7 7 0 0 3 "   / > 	 < ! - -   H o p e l e s s n e s s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 4 1 0 4 2 8 0 0 8 "   / > 	 < ! - -   H y g i e n e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 8 6 6 9 0 0 7 "   / > 	 < ! - -   I n a b i l i t y   t o   e x p e r i e n c e   j o y   o r   p l e a s u r e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 4 7 7 8 3 0 0 9 "   / > 	 < ! - -   I n f l a t e d   s e l f - w o r t h   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 2 5 4 4 5 0 0 3 "   / > 	 < ! - -   I n t r u s i v e   t h o u g h t s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 5 5 9 2 9 0 0 7 "   / > 	 < ! - -   I r r i t a b i l i t y   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 6 7 6 9 8 0 0 9 "   / > 	 < ! - -   O b s e s s i v e   t h o u g h t s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 8 6 8 0 8 0 0 1 "   / > 	 < ! - -   P h o b i a s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 5 3 8 9 0 0 0 3 "   / > 	 < ! - -   P r e s s u r e d   s p e e c h   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 8 5 3 0 3 0 0 6 "   / > 	 < ! - -   R a c i n g   t h o u g h t s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 8 9 6 3 0 0 9 "   / > 	 < ! - -   R a p i d   m o o d   c h a n g e s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 8 5 2 1 6 0 0 9 "   / > 	 < ! - -   R e l i v i n g   t r a u m a t i c   m e m o r i e s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 4 7 8 9 2 0 0 1 "   / > 	 < ! - -   S e l f - d e p r e c a t i o n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 0 1 3 4 5 0 0 2 "   / > 	 < ! - -   S l e e p   p r o b l e m s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 7 1 9 5 1 0 0 8 "   / > 	 < ! - -   T e a r f u l n e s s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 2 5 6 0 6 0 0 2 "   / > 	 < ! - -   U n u s u a l   o r   a b n o r m a l   p h y s i c a l   m o v e m e n t s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 6 1 6 6 5 0 0 6 "   / > 	 < ! - -   U n k n o w n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 4 1 0 5 1 5 0 0 3 "   / > 	 < ! - -   O t h e r   - - > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   0 8 :   c o n d i t i o n   a n d   t r e a t m e n t :   D i a g n o s t i c   c a t e g o r i e s   ( c h e c k   a l l   t h a t   a p p l y )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " d i a g n o s t i c L i s t " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 	 	 	 	 	 < x s d : s e q u e n c e > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " d i a g n o s t i c "   m a x O c c u r s = " u n b o u n d e d "   m i n O c c u r s = " 0 "   / > 
 
 	 	 	 	 	 < / x s d : s e q u e n c e > 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < x s d : e l e m e n t   n a m e = " d i a g n o s t i c " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 	 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 7 2 2 6 0 0 7 "   / > 	 < ! - -   A d j u s t m e n t   D i s o r d e r s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 9 7 4 8 0 0 0 6 "   / > 	 < ! - -   A n x i e t y   D i s o r d e r   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 7 7 6 0 0 0 "   / > 	 	 < ! - -   D e l i r i u m ,   D e m e n t i a ,   a n d   A m n e s t i c   a n d   C o g n i t i v e   D i s o r d e r s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " D C A "   / > 	 	 	 < ! - -   D i s o r d e r   o f   C h i l d h o o d ,   A d o l e s c e n c e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 4 4 3 7 6 0 0 7 "   / > 	 < ! - -   D i s s o c i a t i v e   D i s o r d e r s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 7 2 3 6 6 0 0 4 "   / > 	 < ! - -   E a t i n g   D i s o r d e r s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 5 0 7 0 5 0 0 9 "   / > 	 < ! - -   F a c t i t i o u s   D i s o r d e r s     - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 6 6 3 4 7 0 0 0 "   / > 	 < ! - -   I m p u l s e   C o n t r o l   D i s o r d e r s   n o t   e l s e w h e r e   c l a s s i f i e d   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " M D G M C "   / > 	 	 < ! - -   M e n t a l   D i s o r d e r s   d u e   t o   G e n e r a l   M e d i c a l   C o n d i t i o n s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 4 6 2 0 6 0 0 5 "   / > 	 < ! - -   M o o d   D i s o r d e r   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 3 4 4 9 0 0 4 "   / > 	 < ! - -   P e r s o n a l i t y   D i s o r d e r s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 5 8 2 1 4 0 0 4 "   / > 	 < ! - -   S c h i z o p h r e n i a   o t h e r   P s y c h o t i c   D i s o r d e r s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 9 8 9 8 0 0 5 "   / > 	 < ! - -   S e x u a l   a n d   G e n d e r   I d e n t i t y   D i s o r d e r s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 1 2 9 7 0 0 8 "   / > 	 < ! - -   S l e e p   D i s o r d e r s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 8 7 8 5 8 0 0 2 "   / > 	 < ! - -   S o m a t o f o r m   D i s o r d e r s     - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " S R D D H "   / > 	 	 < ! - -   S u b s t a n c e   R e l a t e d   D i s o r d e r s ,   D e v e l o p m e n t a l   H a n d i c a p   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " D H "   / > 	 	 	 < ! - -   D e v e l o p m e n t a l   H a n d i c a p   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 6 1 6 6 5 0 0 6 "   / > 	 < ! - -   U n k n o w n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C D A "   / > 	 	 	 < ! - -   C l i e n t   d e c l i n e d   t o   a n s w e r   - - > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   0 8 :   c o n d i t i o n   a n d   t r e a t m e n t :   O t h e r   I l l n e s s   I n f o r m a t i o n   ( c h e c k   a l l   t h a t   a p p l y )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " o t h e r I l l n e s s L i s t " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 	 	 	 	 	 < x s d : s e q u e n c e > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " o t h e r I l l n e s s "   m a x O c c u r s = " u n b o u n d e d "   m i n O c c u r s = " 0 "   / > 
 
 	 	 	 	 	 < / x s d : s e q u e n c e > 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < x s d : e l e m e n t   n a m e = " o t h e r I l l n e s s " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 6 A - 0 1 "   / > 	 < ! - -   C o n c u r r e n t   D i s o r d e r   ( S u b s t a n c e   A b u s e )   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 6 A - 0 2 "   / > 	 < ! - -   D u a l   D i a g n o s i s   ( D e v e l o p m e n t   D i s a b i l i t y )   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 6 A - 0 3 "   / > 	 < ! - -   O t h e r   C h r o n i c   I l l n e s s   a n d , o r   p h y s i c a l   d i s a b i l i t i e s   - - > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   1 0 :   s a f e t y   t o   s e l f :   H a v e   y o u   a t t e m p t e d   s u i c i d e   i n   t h e   p a s t ?   ( s e l e c t   o n e )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " s u i c i d e A t t e m p t " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " T R U E "   / > 	 < ! - -   Y e s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " F A L S E "   / > 	 < ! - -   N o   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " U N K "   / > 	 	 < ! - -   U n k n o w n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C D A "   / > 	 	 < ! - -   C l i e n t   d e c l i n e d   t o   a n s w e r   - - > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   1 0 :   s a f e t y   t o   s e l f :   D o   y o u   c u r r e n t l y   h a v e   s u i c i d a l   t h o u g h t s ?   ( s e l e c t   o n e )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " s u i c i d e T h o u g h t s " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " T R U E "   / > 	 < ! - -   Y e s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " F A L S E "   / > 	 < ! - -   N o   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " U N K "   / > 	 	 < ! - -   U n k n o w n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C D A "   / > 	 	 < ! - -   C l i e n t   d e c l i n e d   t o   a n s w e r   - - > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   1 0 :   s a f e t y   t o   s e l f :   D o   y o u   h a v e   a n y   c o n c e r n s   f o r   y o u r   o w n   s a f e t y ?   ( s e l e c t   o n e )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " s a f e t y C o n c e r n S e l f " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " T R U E "   / > 	 < ! - -   Y e s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " F A L S E "   / > 	 < ! - -   N o   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " U N K "   / > 	 	 < ! - -   U n k n o w n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C D A "   / > 	 	 < ! - -   C l i e n t   d e c l i n e d   t o   a n s w e r   - - > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   1 0 :   s a f e t y   t o   s e l f :   R i s k s   ( s e l e c t   a l l   t h a t   a p p l y )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " s a f e t y T o S e l f R i s k L i s t " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 	 	 	 	 	 < x s d : s e q u e n c e > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " s a f e t y T o S e l f R i s k "   m a x O c c u r s = " u n b o u n d e d "   m i n O c c u r s = " 0 "   / > 
 
 	 	 	 	 	 < / x s d : s e q u e n c e > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " o t h e r S a f e t y T o S e l f R i s k "   t y p e = " x s d : s t r i n g "   u s e = " o p t i o n a l "   / > 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < x s d : e l e m e n t   n a m e = " s a f e t y T o S e l f R i s k " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 	 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 2 5 9 1 5 0 0 6 "   / > 	 < ! - -   A b u s e   ,   N e g l e c t   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 4 2 0 5 6 0 0 5 "   / > 	 < ! - -   A c c i d e n t a l   s e l f - h a r m   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 4 0 1 2 0 6 0 0 8 "   / > 	 < ! - -   D e l i b e r a t e   s e l f - h a r m   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 4 1 7 4 3 0 0 0 8 "   / > 	 < ! - -   E x p l o i t a t i o n   r i s k   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 4 1 0 5 1 5 0 0 3 "   / > 	 < ! - -   O t h e r   - - > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   1 2 :   a l c o h o l :   H o w   o f t e n   d o   y o u   d r i n k   a l c o h o l   ( i . e .   n u m b e r   o f   d r i n k s ) ?   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " d r i n k A l c o h o l " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 
 
 	 	 	 	 	 < x s d : s e q u e n c e   / > 
 
 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " q u a n t i t y "   u s e = " o p t i o n a l " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : i n t e g e r " > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " f r e q u e n c y "   u s e = " o p t i o n a l " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 "   / > 	 < ! - -   d a i l y   o r   a l m o s t   e v e r y   d a y .   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 "   / > 	 < ! - -   t w o   o r   t h r e e   t i m e s   a   w e e k   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 "   / > 	 < ! - -   a t   l e a s t   o n c e   a   w e e k   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 4 "   / > 	 < ! - -   a t   l e a s t   o n c e   a   m o n t h   - - > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   1 2 :   a l c o h o l :   I n d i c a t e   t h e   s t a g e   o f   c h a n g e   c l i e n t   i s   a t      O p t i o n a l   ( s e l e c t   o n e )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " s t a g e O f C h a n g e A l c o h o l " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 "   / > 	 < ! - -   P r e - c o n t e m p l a t i o n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 "   / > 	 < ! - -   C o n t e m p l a t i o n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 "   / > 	 < ! - -   A c t i o n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 4 "   / > 	 < ! - -   M a i n t e n a n c e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 5 "   / > 	 < ! - -   R e l a p s e   P r e v e n t i o n   - - > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   1 3 :   d r u g s :   W h i c h   o f   t h e   f o l l o w i n g   d r u g s   h a v e   y o u   u s e d ?   ( c h e c k   a l l   t h a t   a p p l y )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " d r u g U s e L i s t " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 
 
 	 	 	 	 	 < x s d : s e q u e n c e > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " d r u g U s e "   m a x O c c u r s = " u n b o u n d e d "   m i n O c c u r s = " 0 "   / > 
 
 	 	 	 	 	 < / x s d : s e q u e n c e > 
 
 
 
 	 	 	 	 	 < ! - -   H a s   t h e   s u b s t a n c e   b e e n   i n j e c t e d ?   - - > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " i n j e c t e d "   u s e = " o p t i o n a l " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 5 "   / > 	 < ! - -   P a s t   6   m o n t h s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 6 "   / > 	 < ! - -   E v e r   - - > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < x s d : e l e m e n t   n a m e = " d r u g U s e " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 
 
 	 	 	 	 	 < x s d : s e q u e n c e   / > 
 
 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " n a m e "   u s e = " o p t i o n a l " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 	 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 9 8 7 0 5 0 0 4 "   / > 	 < ! - -   M a r i j u a n a   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 8 8 4 5 3 0 0 2 "   / > 	 < ! - -   C o c a i n e   ( c r a c k )   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 2 9 0 0 5 0 0 6 "   / > 	 < ! - -   H a l l u c i n o g e n s   ( e . g .   L S D ,   P C P )   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 2 6 0 5 9 0 0 5 "   / > 	 < ! - -   S t i m u l a n t s   ( e . g .   a m p h e t a m i n e s )   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 2 6 0 4 4 0 0 4 "   / > 	 < ! - -   O p i a t e s   ( e . g .   h e r o i n )   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 7 2 6 1 4 0 0 0 "   / > 	 < ! - -   S e d a t i v e s   ( n o t   p r e s c r i b e d   o r   n o t   t a k e n   a s   p r e s c r i b e d   e . g .   V a l i u m )   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 8 0 2 8 8 0 0 2 "   / > 	 < ! - -   O v e r - t h e - c o u n t e r   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 6 1 0 1 0 0 0 5 "   / > 	 < ! - -   S o l v e n t s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 4 1 0 5 1 5 0 0 3 "   / > 	 < ! - -   O t h e r   - - > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " f r e q u e n c y "   u s e = " o p t i o n a l " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 5 "   / > 	 < ! - -   P a s t   6   m o n t h s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 6 "   / > 	 < ! - -   E v e r   - - > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   1 3 :   d r u g s :   I n d i c a t e   t h e   s t a g e   o f   c h a n g e   c l i e n t   i s   a t      O p t i o n a l   ( s e l e c t   o n e )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " s t a g e O f C h a n g e D r u g s " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 "   / > 	 < ! - -   P r e - c o n t e m p l a t i o n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 "   / > 	 < ! - -   C o n t e m p l a t i o n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 "   / > 	 < ! - -   A c t i o n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 4 "   / > 	 < ! - -   M a i n t e n a n c e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 5 "   / > 	 < ! - -   R e l a p s e   P r e v e n t i o n   - - > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   1 4 :   o t h e r   a d d i c t i o n s :   T y p e   o f   a d d i c t i o n   ( c h e c k   a l l   t h a t   a p p l y )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " a d d i c t i o n T y p e L i s t " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 	 	 	 	 	 < x s d : s e q u e n c e > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " a d d i c t i o n T y p e "   m a x O c c u r s = " u n b o u n d e d "   m i n O c c u r s = " 0 "   / > 
 
 	 	 	 	 	 < / x s d : s e q u e n c e > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " o t h e r A d d i c t i o n T y p e "   t y p e = " x s d : s t r i n g "   u s e = " o p t i o n a l "   / > 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < x s d : e l e m e n t   n a m e = " a d d i c t i o n T y p e " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 	 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 0 5 5 2 3 0 0 9 "   / > 	 < ! - -   G a m b l i n g   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 5 6 2 9 4 0 0 8 "   / > 	 < ! - -   N i c o t i n e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 4 1 0 5 1 5 0 0 3 "   / > 	 < ! - -   O t h e r   - - > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   1 4 :   o t h e r   a d d i c t i o n s :   I n d i c a t e   t h e   s t a g e   o f   c h a n g e   c l i e n t   i s   a t      O p t i o n a l   ( s e l e c t   o n e )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " s t a g e O f C h a n g e A d d i c t i o n s " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 "   / > 	 < ! - -   P r e - c o n t e m p l a t i o n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 "   / > 	 < ! - -   C o n t e m p l a t i o n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 3 "   / > 	 < ! - -   A c t i o n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 4 "   / > 	 < ! - -   M a i n t e n a n c e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 5 "   / > 	 < ! - -   R e l a p s e   P r e v e n t i o n   - - > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   1 5 :   c o m p a n y :   H a v e   t h e r e   b e e n   a n y   c h a n g e s   t o   y o u r   s o c i a l   p a t t e r n s   r e c e n t l y ?   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " c h a n g e d S o c i a l P a t t e r n s " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " T R U E "   / > 	 < ! - -   Y e s   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " F A L S E "   / > 	 < ! - -   N o   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " U N K "   / > 	 	 < ! - -   U n k n o w n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C D A "   / > 	 	 < ! - -   C l i e n t   d e c l i n e d   t o   a n s w e r   - - > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   2 0 :   b a s i c   e d u c a t i o n :   W h a t   i s   y o u r   h i g h e s t   l e v e l   o f   e d u c a t i o n ?   ( s e l e c t   o n e )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " h i g h e s t E d u c a t i o n L e v e l " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 2 4 3 0 4 0 0 4 "   / > 	 < ! - -   N o   F o r m a l   S c h o o l i n g   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " H L E S - 2 "   / > 	 	 < ! - -   S o m e   E l e m e n t a r y ,   J u n i o r   H i g h   S c h o o l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 2 4 3 0 6 0 0 2 "   / > 	 < ! - -   E l e m e n t a r y ,   J u n i o r   H i g h   S c h o o l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " H L E S - 4 "   / > 	 	 < ! - -   S o m e   S e c o n d a r y ,   H i g h   S c h o o l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 2 4 3 0 8 0 0 1 "   / > 	 < ! - -   S e c o n d a r y ,   H i g h   S c h o o l   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " H L E S - 6 "   / > 	 	 < ! - -   S o m e   C o l l e g e ,   U n i v e r s i t y   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 2 4 8 7 1 0 0 2 "   / > 	 < ! - -   C o l l e g e ,   U n i v e r s i t y   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 6 1 6 6 5 0 0 6 "   / > 	 < ! - -   U n k n o w n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C D A "   / > 	 	 	 < ! - -   C l i e n t   d e c l i n e d   t o   a n s w e r   - - > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   2 3 :   m o n e y :   W h a t   i s   y o u r   p r i m a r y   s o u r c e   o f   i n c o m e ?   ( s e l e c t   o n e )   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " s o u r c e O f I n c o m e " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 3 1 - 0 1 "   / > 	 < ! - -   E m p l o y m e n t   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 3 1 - 0 2 "   / > 	 < ! - -   E m p l o y m e n t   I n s u r a n c e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 3 1 - 0 3 "   / > 	 < ! - -   P e n s i o n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 3 1 - 0 4 "   / > 	 < ! - -   O D S P   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 3 1 - 0 5 "   / > 	 < ! - -   S o c i a l   A s s i s t a n c e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 3 1 - 0 6 "   / > 	 < ! - -   D i s a b i l i t y   A s s i s t a n c e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 3 1 - 0 7 "   / > 	 < ! - -   F a m i l y   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 3 1 - 0 8 "   / > 	 < ! - -   N o   s o u r c e   o f   i n c o m e   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 3 1 - 0 9 "   / > 	 < ! - -   O t h e r   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 3 1 - 1 0 "   / > 	 < ! - -   U n k n o w n   - - > 
 
 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 3 1 - 1 1 "   / > 	 < ! - -   C l i e n t   D e c l i n e d   t o   A n s w e r   - - > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < x s d : e l e m e n t   n a m e = " a d d i t i o n a l E l e m e n t s " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 
 
 	 	 	 	 	 < x s d : s e q u e n c e > 
 
 
 
 	 	 	 	 	 	 < ! - -   B e g i n   F r e e   T e x t   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " c l i e n t H o p e s F o r F u t u r e "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " c l i e n t N e e d T o G e t T h e r e "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " c l i e n t V i e w M e n t a l H e a l t h "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " c l i e n t S p i r i t u a l i t y I m p o r t a n c e "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " c l i e n t C u l t u r e H e r i t a g e I m p o r t a n c e "   / > 
 
 	 	 	 	 	 	 < ! - -   E n d   F r e e   T e x t   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " p r e s e n t i n g I s s u e L i s t "   m i n O c c u r s = " 1 "   m a x O c c u r s = " 1 "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " a c t i o n L i s t "   m i n O c c u r s = " 0 "   m a x O c c u r s = " 1 "   / > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " r e f e r r a l L i s t "   m i n O c c u r s = " 0 "   m a x O c c u r s = " 1 "   / > 
 
 
 
 	 	 	 	 	 < / x s d : s e q u e n c e > 
 
 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   B e g i n   F r e e   T e x t   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 < ! - -   
 
 	 	   N o t e :   
 
 	 	 	 A l l   F r e e   T e x t   e l e m e n t s   a r e   n o t   s u p p o s e d   t o   b e   c o l l e c t e d   d u r i n g   t h i s   p h a s e . 
 
 	 	 	 T h e r e   i s   a   p l a n   t o   c o l l e c t   t h e m   i n   t h e   f u t u r e ,   s o ,   t h e   p l a c e h o l d e r   
 
 	 	 	 i . e .   t h e   T A G s   w i l l   b e   t h e r e   b u t   t h e y   a r e   r e s t r i c t e d   w i t h   m a x L e n g t h = 0 
 
 	 	 	 i n   o r d e r   t o   e n s u r e   n o   F r e e   T e x t   d a t a   i s   s e n t   a t   t h i s   t i m e . 
 
 	 	 	 F r e e   T e x t   T A G s   w i l l   b e   i n c l u d e d   i n   t h e   d a t a   s u b m i s s i o n   f i l e   b u t   w i t h   e m p t y   v a l u e s . 
 
 	 - - > 
 
 
 
 	 < ! - -   W h a t   a r e   y o u r   h o p e s   f o r   t h e   f u t u r e ?   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " c l i e n t H o p e s F o r F u t u r e " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : m a x L e n g t h   v a l u e = " 0 "   / > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   W h a t   d o   y o u   t h i n k   y o u   n e e d   i n   o r d e r   t o   g e t   t h e r e ?   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " c l i e n t N e e d T o G e t T h e r e " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : m a x L e n g t h   v a l u e = " 0 "   / > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   H o w   d o   y o u   v i e w   y o u r   m e n t a l   h e a l t h ?   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " c l i e n t V i e w M e n t a l H e a l t h " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : m a x L e n g t h   v a l u e = " 0 "   / > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   I s   s p i r i t u a l i t y   a n   i m p o r t a n t   p a r t   o f   y o u r   l i f e ?   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " c l i e n t S p i r i t u a l i t y I m p o r t a n c e " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : m a x L e n g t h   v a l u e = " 0 "   / > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   I s   c u l t u r e   ( h e r i t a g e )   a n   i m p o r t a n t   p a r t   o f   y o u r   l i f e ?   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " c l i e n t C u l t u r e H e r i t a g e I m p o r t a n c e " > 
 
 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 < x s d : m a x L e n g t h   v a l u e = " 0 "   / > 
 
 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 < / x s d : s i m p l e T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   E n d   F r e e   T e x t   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 
 
 	 < ! - -   P r e s e n t i n g   I s s u e s :   t h i s   i s   m a n d a t o r y ,   a t   l e a s t   o n e   i s s u e   s h o u l d   b e   l i s t e d   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " p r e s e n t i n g I s s u e L i s t " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 	 	 	 	 	 < x s d : s e q u e n c e > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " p r e s e n t i n g I s s u e "   m i n O c c u r s = " 1 "   m a x O c c u r s = " u n b o u n d e d "   / > 
 
 	 	 	 	 	 < / x s d : s e q u e n c e > 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < x s d : e l e m e n t   n a m e = " p r e s e n t i n g I s s u e " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 	 	 	 	 	 < x s d : s e q u e n c e   / > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " t y p e "   u s e = " r e q u i r e d " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 7 - 0 1 "   / > 	 < ! - -   T h r e a t   t o   o t h e r ,   a t t e m p t e d   s u i c i d e   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 7 - 0 2 "   / > 	 < ! - -   s p e c i f i c   s y m p t o m   o f   s e r i o u s   m e n t a l   i l l n e s s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 7 - 0 3 "   / > 	 < ! - -   p h y s i c a l / s e x u a l   a b u s e   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 7 - 0 4 "   / > 	 < ! - -   E d u c a t i o n a l   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 7 - 0 5 "   / > 	 < ! - -   O c c u p a t i o n a l ,   E m p l o y m e n t ,   V o c a t i o n a l   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 7 - 0 6 "   / > 	 < ! - -   H o u s i n g   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 7 - 0 7 "   / > 	 < ! - -   F i n a n c i a l   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 7 - 0 8 "   / > 	 < ! - -   L e g a l   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 7 - 0 9 "   / > 	 < ! - -   P r o b l e m s   w i t h   r e l a t i o n s h i p s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 7 - 1 0 "   / > 	 < ! - -   P r o b l e m s   w i t h   s u b s t a n c e   a b u s e ,   a d d i c t i o n s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 7 - 1 1 "   / > 	 < ! - -   A c t i v i t i e s   o f   d a i l y   l i v i n g   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 7 - 1 2 "   / > 	 < ! - -   O t h e r   - - > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   S u m m a r y   o f   a c t i o n s   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " a c t i o n L i s t " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 	 	 	 	 	 < x s d : s e q u e n c e > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " a c t i o n "   m a x O c c u r s = " u n b o u n d e d "   m i n O c c u r s = " 0 "   / > 
 
 	 	 	 	 	 < / x s d : s e q u e n c e > 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < x s d : e l e m e n t   n a m e = " a c t i o n " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 
 
 	 	 	 	 	 < x s d : s e q u e n c e   / > 
 
 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " p r i o r i t y "   u s e = " o p t i o n a l " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " d o m a i n "   u s e = " o p t i o n a l " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 1 - a c c o m m o d a t i o n "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 2 - f o o d "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 3 - l o o k i n g   a f t e r   t h e   h o m e "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 4 - s e l f - c a r e "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 5 - d a y t i m e   a c t i v i t i e s "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 6 - p h y s i c a l   h e a l t h "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 7 - p s y c h o t i c   s y m p t o m s "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 8 - c o n d i t i o n   a n d   t r e a t m e n t "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 9 - p s y c h o l o g i c a l   d i s t r e s s "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 0 - s a f e t y   t o   s e l f "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 1 - s a f e t y   t o   o t h e r s "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 2 - a l c o h o l "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 3 - d r u g s "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 4 - o t h e r   a d d i c t i o n s "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 5 - c o m p a n y "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 6 - i n t i m a t e   r e l a t i o n s h i p s "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 7 - s e x u a l   e x p r e s s i o n "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 8 - c h i l d c a r e "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 1 9 - o t h e r   d e p e n d e n t s "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 0 - b a s i c   e d u c a t i o n "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 1 - t e l e p h o n e "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 2 - t r a n s p o r t "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 3 - m o n e y "   / > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 2 4 - b e n e f i t s "   / > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < ! - -   S u m m a r y   o f   r e f e r r a l s   - - > 
 
 	 < x s d : e l e m e n t   n a m e = " r e f e r r a l L i s t " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 	 	 	 	 	 < x s d : s e q u e n c e > 
 
 	 	 	 	 	 	 < x s d : e l e m e n t   r e f = " r e f e r r a l "   m a x O c c u r s = " u n b o u n d e d "   m i n O c c u r s = " 0 "   / > 
 
 	 	 	 	 	 < / x s d : s e q u e n c e > 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 
 
 	 < x s d : e l e m e n t   n a m e = " r e f e r r a l " > 
 
 	 	 < x s d : c o m p l e x T y p e > 
 
 	 	 	 < x s d : c o m p l e x C o n t e n t > 
 
 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : a n y T y p e " > 
 
 
 
 	 	 	 	 	 < x s d : s e q u e n c e   / > 
 
 
 
 	 	 	 	 	 < ! - -   O p t i m a l   R e f e r r a l   - - > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " o p t i m a l "   u s e = " o p t i o n a l " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 A S "   / > 	 	 < ! - -   A b u s e   S e r v i c e s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " A D D "   / > 	 	 < ! - -   A d d i c t i o n s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 A B "   / > 	 	 < ! - -   A l t e r n a t i v e   B u s i n e s s e s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " A C T "   / > 	 	 < ! - -   A s s e r t i v e   C o m m u n i t y   T r e a t m e n t   T e a m s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 C M "   / > 	 	 < ! - -   M e n t a l   H e a l t h   C a s e   M a n a g e m e n t   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C H I "   / > 	 	 < ! - -   C h i l d ,   A d o l e s c e n t   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C L U "   / > 	 	 < ! - -   C l u b h o u s e s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 C D "   / > 	 	 < ! - -   C o m m u n i t y   D e v e l o p m e n t   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C M H "   / > 	 	 < ! - -   C o m m u n i t y   M e n t a l   H e a l t h   C l i n i c   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 I R "   / > 	 	 < ! - -   C o m m u n i t y   S e r v i c e   I n f o r m a t i o n   a n d   R e f e r r a l   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C O N "   / > 	 	 < ! - -   C o n c u r r e n t   D i s o r d e r s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C & a m p ; T "   / > 	 < ! - -   C o u n s e l i n g   a n d   T r e a t m e n t   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " D D x "   / > 	 	 < ! - -   D u a l   D i a g n o s i s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " E A R "   / > 	 	 < ! - -   E a r l y   I n t e r v e n t i o n   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " E A T "   / > 	 	 < ! - -   E a t i n g   D i s o r d e r   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 F I "   / > 	 	 < ! - -   F a m i l y   I n i t i a t i v e s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " F O R "   / > 	 	 < ! - -   F o r e n s i c   D C S   D i v e r s i o n   a n d   C o u r t   S u p p o r t   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " H P A "   / > 	 	 < ! - -   H e a l t h   P r o m o t i o n ,   E d u c a t i o n      A w a r e n e s s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " H P W "   / > 	 	 < ! - -   H e a l t h   P r o m o t i o n ,   E d u c a t i o n      W o m e n s   M e n t a l   H e a l t h   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " H S C "   / > 	 	 < ! - -   H o m e s   f o r   S p e c i a l   C a r e   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C R I "   / > 	 	 < ! - -   M e n t a l   H e a l t h   C r i s i s   I n t e r v e n t i o n   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " P S H "   / > 	 	 < ! - -   P e e r ,   S e l f - h e l p   I n i t i a t i v e s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 D N "   / > 	 	 < ! - -   P r i m a r y   D a y ,   N i g h t   C a r e   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " G E R "   / > 	 	 < ! - -   P s y c h o - g e r i a t r i c   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C S B "   / > 	 	 < ! - -   S h o r t   t e r m   R e s .   C r i s i s   S u p p o r t   B e d s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 S R "   / > 	 	 < ! - -   S o c i a l   R e h a b i l i t a t i o n ,   R e c r e a t i o n   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 S H "   / > 	 	 < ! - -   S u p p o r t   w i t h i n   H o u s i n g   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " E M P "   / > 	 	 < ! - -   V o c a t i o n a l ,   E m p l o y m e n t   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C A C "   / > 	 	 < ! - -   C C A C   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C M S "   / > 	 	 < ! - -   C h u r c h ,   m o s q u e ,   s y n a g o g u e ,   e t c .   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " D N T "   / > 	 	 < ! - -   D e n t i s t   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " F H G "   / > 	 	 < ! - -   F a m i l y   h e l p   g r o u p s   ( O t h e r   t h a n   M H )   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " A G S "   / > 	 	 < ! - -   O l d e r   a d u l t   a n d   g e r i a t r i c   s e r v i c e s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " O P T "   / > 	 	 < ! - -   O p t o m e t r i s t s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " P O L "   / > 	 	 < ! - -   P o l i c e   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " P R I "   / > 	 	 < ! - -   P r i m a r y   c a r e      C H C ,   F H T ,   F H G ,   F H N ,   G P   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " S C H "   / > 	 	 < ! - -   S c h o o l   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " S H G "   / > 	 	 < ! - -   S e l f   h e l p   g r o u p s   ( O t h e r   t h a n   M H )   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 S C "   / > 	 	 < ! - -   S e r v i c e   c l u b s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " S S s "   / > 	 	 < ! - -   S o c i a l   S e r v i c e s   -   O D S P ,   C P P ,   E I ,   e t c .   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " Y S s "   / > 	 	 < ! - -   Y o u t h   s e r v i c e s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 C P "   / > 	 	 < ! - -   C o m m u n i t y   p s y c h i a t r y   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 P P "   / > 	 	 < ! - -   P r i v a t e   p r a c t i t i o n e r s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " F B S "   / > 	 	 < ! - -   F o o d   b a n k ,   s o u p   k i t c h e n s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 L C "   / > 	 	 < ! - -   L e g a l   c o u n s e l   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 I S "   / > 	 	 < ! - -   I m m i g r a t i o n   s e r v i c e s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " F C P "   / > 	 	 < ! - -   F i n a n c i a l - c r e d i t   c o u n s e l i n g ,   f i n a n c i a l   p l a n n i n g   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " F I T "   / > 	 	 < ! - -   F i t n e s s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " A H O "   / > 	 	 < ! - -   A l t e r n a t i v e   h e a l i n g   o p t i o n s :   c h i r o p r a c t i c ,   a c u p u n c t u r e ,   m e d i t a t i o n ,   h e r b a l i s t ,   e t c .   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " P S C "   / > 	 	 < ! - -   P a r e n t i n g   s u p p o r t s :   c h i l d   c a r e   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C A S "   / > 	 	 < ! - -   C A S ,   C C A S ,   J C F S   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " H O S "   / > 	 	 < ! - -   H o s p i t a l   - - > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 	 < ! - -   S p e c i f y   m o r e   d e t a i l s   a b o u t   O p t i m a l   R e f e r r a l   - - > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " s p e c i f y O p t i m a l "   u s e = " o p t i o n a l " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 	 < ! - -   A c t u a l   R e f e r r a l   - - > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " a c t u a l "   u s e = " o p t i o n a l " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 A S "   / > 	 	 < ! - -   A b u s e   S e r v i c e s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " A D D "   / > 	 	 < ! - -   A d d i c t i o n s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 A B "   / > 	 	 < ! - -   A l t e r n a t i v e   B u s i n e s s e s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " A C T "   / > 	 	 < ! - -   A s s e r t i v e   C o m m u n i t y   T r e a t m e n t   T e a m s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 C M "   / > 	 	 < ! - -   M e n t a l   H e a l t h   C a s e   M a n a g e m e n t   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C H I "   / > 	 	 < ! - -   C h i l d ,   A d o l e s c e n t   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C L U "   / > 	 	 < ! - -   C l u b h o u s e s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 C D "   / > 	 	 < ! - -   C o m m u n i t y   D e v e l o p m e n t   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C M H "   / > 	 	 < ! - -   C o m m u n i t y   M e n t a l   H e a l t h   C l i n i c   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 I R "   / > 	 	 < ! - -   C o m m u n i t y   S e r v i c e   I n f o r m a t i o n   a n d   R e f e r r a l   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C O N "   / > 	 	 < ! - -   C o n c u r r e n t   D i s o r d e r s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C & a m p ; T "   / > 	 < ! - -   C o u n s e l i n g   a n d   T r e a t m e n t   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " D D x "   / > 	 	 < ! - -   D u a l   D i a g n o s i s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " E A R "   / > 	 	 < ! - -   E a r l y   I n t e r v e n t i o n   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " E A T "   / > 	 	 < ! - -   E a t i n g   D i s o r d e r   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 F I "   / > 	 	 < ! - -   F a m i l y   I n i t i a t i v e s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " F O R "   / > 	 	 < ! - -   F o r e n s i c   D C S   D i v e r s i o n   a n d   C o u r t   S u p p o r t   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " H P A "   / > 	 	 < ! - -   H e a l t h   P r o m o t i o n ,   E d u c a t i o n      A w a r e n e s s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " H P W "   / > 	 	 < ! - -   H e a l t h   P r o m o t i o n ,   E d u c a t i o n      W o m e n s   M e n t a l   H e a l t h   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " H S C "   / > 	 	 < ! - -   H o m e s   f o r   S p e c i a l   C a r e   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C R I "   / > 	 	 < ! - -   M e n t a l   H e a l t h   C r i s i s   I n t e r v e n t i o n   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " P S H "   / > 	 	 < ! - -   P e e r ,   S e l f - h e l p   I n i t i a t i v e s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 D N "   / > 	 	 < ! - -   P r i m a r y   D a y ,   N i g h t   C a r e   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " G E R "   / > 	 	 < ! - -   P s y c h o - g e r i a t r i c   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C S B "   / > 	 	 < ! - -   S h o r t   t e r m   R e s .   C r i s i s   S u p p o r t   B e d s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 S R "   / > 	 	 < ! - -   S o c i a l   R e h a b i l i t a t i o n ,   R e c r e a t i o n   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 S H "   / > 	 	 < ! - -   S u p p o r t   w i t h i n   H o u s i n g   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " E M P "   / > 	 	 < ! - -   V o c a t i o n a l ,   E m p l o y m e n t   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C A C "   / > 	 	 < ! - -   C C A C   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C M S "   / > 	 	 < ! - -   C h u r c h ,   m o s q u e ,   s y n a g o g u e ,   e t c .   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " D N T "   / > 	 	 < ! - -   D e n t i s t   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " F H G "   / > 	 	 < ! - -   F a m i l y   h e l p   g r o u p s   ( O t h e r   t h a n   M H )   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " A G S "   / > 	 	 < ! - -   O l d e r   a d u l t   a n d   g e r i a t r i c   s e r v i c e s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " O P T "   / > 	 	 < ! - -   O p t o m e t r i s t s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " P O L "   / > 	 	 < ! - -   P o l i c e   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " P R I "   / > 	 	 < ! - -   P r i m a r y   c a r e      C H C ,   F H T ,   F H G ,   F H N ,   G P   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " S C H "   / > 	 	 < ! - -   S c h o o l   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " S H G "   / > 	 	 < ! - -   S e l f   h e l p   g r o u p s   ( O t h e r   t h a n   M H )   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 S C "   / > 	 	 < ! - -   S e r v i c e   c l u b s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " S S s "   / > 	 	 < ! - -   S o c i a l   S e r v i c e s   -   O D S P ,   C P P ,   E I ,   e t c .   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " Y S s "   / > 	 	 < ! - -   Y o u t h   s e r v i c e s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 C P "   / > 	 	 < ! - -   C o m m u n i t y   p s y c h i a t r y   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 P P "   / > 	 	 < ! - -   P r i v a t e   p r a c t i t i o n e r s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " F B S "   / > 	 	 < ! - -   F o o d   b a n k ,   s o u p   k i t c h e n s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 L C "   / > 	 	 < ! - -   L e g a l   c o u n s e l   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " 0 I S "   / > 	 	 < ! - -   I m m i g r a t i o n   s e r v i c e s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " F C P "   / > 	 	 < ! - -   F i n a n c i a l - c r e d i t   c o u n s e l i n g ,   f i n a n c i a l   p l a n n i n g   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " F I T "   / > 	 	 < ! - -   F i t n e s s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " A H O "   / > 	 	 < ! - -   A l t e r n a t i v e   h e a l i n g   o p t i o n s :   c h i r o p r a c t i c ,   a c u p u n c t u r e ,   m e d i t a t i o n ,   h e r b a l i s t ,   e t c .   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " P S C "   / > 	 	 < ! - -   P a r e n t i n g   s u p p o r t s :   c h i l d   c a r e   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " C A S "   / > 	 	 < ! - -   C A S ,   C C A S ,   J C F S   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " H O S "   / > 	 	 < ! - -   H o s p i t a l   - - > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 	 < ! - -   S p e c i f y   m o r e   d e t a i l s   a b o u t   A c t u a l   R e f e r r a l   - - > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " s p e c i f y A c t u a l "   u s e = " o p t i o n a l " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 	 < ! - -   R e a s o n   f o r   D i f f e r e n c e   - - > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " d i f f e r e n c e R e a s o n "   u s e = " o p t i o n a l " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " R D - 1 "   / > 	 < ! - -   S e r v i c e   d o e s   n o t   e x i s t   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " R D - 2 "   / > 	 < ! - -   S e r v i c e   n o t   a v a i l a b l e   l o c a l l y   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " R D - 3 "   / > 	 < ! - -   S e r v i c e   n o t   a v a i l a b l e   d u e   t o   l a n g u a g e   i s s u e s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " R D - 4 "   / > 	 < ! - -   S e r v i c e   n o t   a v a i l a b l e   d u e   t o   f i n a n c i a l   i s s u e s   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " R D - 5 "   / > 	 < ! - -   S e r v i c e   n o t   a v a i l a b l e   d u e   t o   p h y s i c a l   b a r r i e r s      p h y s i c a l ,   v i s i o n ,   h e a r i n g   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " R D - 6 "   / > 	 < ! - -   E x c l u s i o n a r y   c r i t e r i a   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " R D - 7 "   / > 	 < ! - -   E x c e s s i v e   w a i t   t i m e s   f o r   s e r v i c e   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " R D - 8 "   / > 	 < ! - -   W a i t   l i s t   c l o s e d   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " R D - 9 "   / > 	 < ! - -   N e e d   e x i s t s   b u t   s e r v i c e   n o t   i d e n t i f i e d   b y   c l i e n t / s t a f f   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " R D - 1 0 "   / > 	 < ! - -   S e r v i c e   a v a i l a b l e   b u t   o n l y   p a r t i a l l y   m e e t s   c l i e n t  s   n e e d   - - > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 	 < ! - -   R e f e r r a l   S t a t u s   - - > 
 
 	 	 	 	 	 < x s d : a t t r i b u t e   n a m e = " s t a t u s "   u s e = " o p t i o n a l " > 
 
 	 	 	 	 	 	 < x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 	 	 < x s d : r e s t r i c t i o n   b a s e = " x s d : s t r i n g " > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " "   / > 	 	 < ! - -   N u l l   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " R S - 1 "   / > 	 < ! - -   R e c e i v e d   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " R S - 2 "   / > 	 < ! - -   A c c e p t e d   o n t o   w a i t l i s t   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " R S - 3 "   / > 	 < ! - -   A c c e p t e d   i n t o   s e r v i c e   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " R S - 4 "   / > 	 < ! - -   R e j e c t e d   - - > 
 
 	 	 	 	 	 	 	 	 < x s d : e n u m e r a t i o n   v a l u e = " R S - 5 "   / > 	 < ! - -   W i t h d r a w n   ( b y   t h e   c l i e n t )   - - > 
 
 	 	 	 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 	 	 	 < / x s d : s i m p l e T y p e > 
 
 	 	 	 	 	 < / x s d : a t t r i b u t e > 
 
 
 
 	 	 	 	 < / x s d : r e s t r i c t i o n > 
 
 	 	 	 < / x s d : c o m p l e x C o n t e n t > 
 
 	 	 < / x s d : c o m p l e x T y p e > 
 
 	 < / x s d : e l e m e n t > 
 
 < / x s d : s c h e m a > 
 
 