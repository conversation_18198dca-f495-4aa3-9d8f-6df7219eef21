< ? x m l   v e r s i o n = " 1 . 0 "   e n c o d i n g = " U T F - 1 6 "   s t a n d a l o n e = " y e s " ? > 
 
 < O C A N S u b m i s s i o n F i l e   v e r s i o n = " 1 . 0 . 0 "   I D = " F i l e - 0 1 "   t i m e s t a m p = " 2 0 0 9 - 0 3 - 1 6 T 0 0 : 0 0 : 0 0 Z " > 
 
 
 
 	 < ! - -   a s s e s s m e n t   r e c o r d #   1 :   t h i s   a s s e s s m e n t   s h o w s   a l l   T A G s   h a v e   t h e i r   v a l u e s   p o p u l a t e d   - - > 
 
 	 < O C A N S u b m i s s i o n R e c o r d   a s s e s s m e n t I D = " 1 0 0 1 "   s t a r t D a t e = " 2 0 0 9 - 0 1 - 1 6 Z "   c o m p l e t i o n D a t e = " 2 0 0 9 - 0 1 - 3 1 Z "   a s s e s s m e n t S t a t u s = " C o m p l e t e " > 
 
 	 	 < o r g a n i z a t i o n R e c o r d > 
 
 	 	 	 < s e r v i c e O r g   n a m e = " N E   L H I N   T e s t   O r g "   n u m b e r = " 1 2 3 " / > 
 
 	 	 	 < p r o g r a m   n a m e = " I n t e n s i v e   C a s e   M a n a g e m e n t "   n u m b e r = " 3 0 2 5 " / > 
 
 	 	 	 < M I S F u n c t i o n   v a l u e = " 7 2 5   1 0   7 6   1 2 " / > 
 
 	 	 < / o r g a n i z a t i o n R e c o r d > 
 
 	 	 < c l i e n t R e c o r d > 
 
 	 	 	 < c l i e n t I D   o r g C l i e n t I D = " 1 2 3 0 0 0 " / > 
 
 	 	 	 < ! - -   B e g i n   P H I   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < c l i e n t N a m e   l a s t = " "   f i r s t = " " / > 
 
 	 	 	 < c l i e n t A d d r e s s   l i n e 1 = " "   l i n e 2 = " "   c i t y = " "   p r o v i n c e = " "   p o s t a l C o d e = " " / > 
 
 	 	 	 < c l i e n t P h o n e / > 
 
 	 	 	 < c l i e n t O H I P   n u m b e r = " "   v e r s i o n = " " / > 
 
 	 	 	 < c l i e n t C u l t u r e / > 
 
 	 	 	 < ! - -   E n d   P H I   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < r e a s o n F o r A s s e s s m e n t   v a l u e = " O T H R "   o t h e r = " d e t a i l   t e x t   f o r   o t h e r   r e a s o n   o f   a s s e s s m e n t " / > 
 
 	 	 	 < c l i e n t C o n t a c t > 
 
 	 	 	 	 < d o c t o r C o n t a c t   d o c t o r = " T R U E "   l a s t S e e n = " L S - 6 " / > 
 
 	 	 	 	 < p s y c h i a t r i s t C o n t a c t   p s y c h i a t r i s t = " T R U E "   l a s t S e e n = " L S - 1 2 " / > 
 
 	 	 	 	 < o t h e r P r a c t i t i o n e r C o n t a c t   p r a c t i t i o n e r T y p e = " 3 1 1 1 "   l a s t S e e n = " L S - 1 " / > 
 
 	 	 	 	 < o t h e r P r a c t i t i o n e r C o n t a c t   p r a c t i t i o n e r T y p e = " 3 1 1 2 "   l a s t S e e n = " L S - 1 3 " / > 
 
 	 	 	 	 < o t h e r P r a c t i t i o n e r C o n t a c t   p r a c t i t i o n e r T y p e = " 4 2 1 7 "   l a s t S e e n = " L S - 6 " / > 
 
 	 	 	 	 < o t h e r A g e n c y C o n t a c t   l a s t S e e n = " L S - 1 2 " / > 
 
 	 	 	 < / c l i e n t C o n t a c t > 
 
 	 	 	 < s e r v i c e R e c i p i e n t L o c a t i o n > 0 1 0 - 5 2 < / s e r v i c e R e c i p i e n t L o c a t i o n > 
 
 	 	 	 < s e r v i c e R e c i p i e n t L H I N > 1 2 < / s e r v i c e R e c i p i e n t L H I N > 
 
 	 	 	 < s e r v i c e D e l i v e r y L H I N > 5 < / s e r v i c e D e l i v e r y L H I N > 
 
 	 	 	 < c l i e n t D O B > 2 0 0 1 - 0 3 - 1 5 Z < / c l i e n t D O B > 
 
 	 	 	 < g e n d e r > F < / g e n d e r > 
 
 	 	 	 < m a r i t a l S t a t u s > 1 2 5 6 8 1 0 0 6 < / m a r i t a l S t a t u s > 
 
 	 	 	 < c l i e n t C a p a c i t y   p r o p e r t y = " T R U E "   p e r s o n a l C a r e = " F A L S E "   l e g a l G u a r d i a n = " C D A " / > 
 
 	 	 	 < r e f e r r a l S o u r c e > 0 1 8 - 0 5 < / r e f e r r a l S o u r c e > 
 
 	 	 	 < a b o r i g i n a l O r i g i n > 0 1 1 - 0 2 < / a b o r i g i n a l O r i g i n > 
 
 	 	 	 < c i t i z e n s h i p S t a t u s > C D N < / c i t i z e n s h i p S t a t u s > 
 
 	 	 	 < t i m e L i v e d I n C a n a d a   y e a r s = " 3 "   m o n t h s = " 8 " / > 
 
 	 	 	 < i m m i g E x p L i s t   o t h e r I m m i g E x p = " o t h e r   i m m i g r a t i o n   e x p e r i e n c e   t e x t " > 
 
 	 	 	 	 < v a l u e > 2 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 3 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 7 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 8 < / v a l u e > 
 
 	 	 	 < / i m m i g E x p L i s t > 
 
 	 	 	 < d i s c r i m E x p L i s t   o t h e r D i s c r i m E x p = " o t h e r   d i s c r i m i n a t i o n   e x p e r i e n c e   t e x t " > 
 
 	 	 	 	 < v a l u e > 2 1 1 3 4 0 0 2 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 3 6 5 8 7 3 0 0 7 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 4 1 0 5 1 5 0 0 3 < / v a l u e > 
 
 	 	 	 < / d i s c r i m E x p L i s t > 
 
 	 	 	 < p r e f L a n g > f r a < / p r e f L a n g > 
 
 	 	 	 < s e r v i c e L a n g > e n g < / s e r v i c e L a n g > 
 
 	 	 	 < l e g a l I s s u e s > C i v i l < / l e g a l I s s u e s > 
 
 	 	 	 < l e g a l S t a t u s L i s t > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 1 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 2 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 3 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 8 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 9 < / l e g a l S t a t u s > 
 
 	 	 	 < / l e g a l S t a t u s L i s t > 
 
 	 	 	 < e x i t D i s p o s i t i o n > 0 1 9 - 0 6 < / e x i t D i s p o s i t i o n > 
 
 	 	 < / c l i e n t R e c o r d > 
 
 	 	 < O C A N D o m a i n s > 
 
 	 	 	 < d o m a i n   n a m e = " 0 1 - a c c o m m o d a t i o n " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " 2 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 	 < r e s i d e n c e T y p e > 0 2 4 - 1 9 < / r e s i d e n c e T y p e > 
 
 	 	 	 	 < r e s i d e n c e S u p p o r t > 2 4 A - 0 5 < / r e s i d e n c e S u p p o r t > 
 
 	 	 	 	 < l i v i n g A r r a n g e m e n t T y p e > 0 2 3 - 0 8 < / l i v i n g A r r a n g e m e n t T y p e > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 2 - f o o d " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " 9 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 3 - l o o k i n g   a f t e r   t h e   h o m e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " 2 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 4 - s e l f - c a r e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " 0 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 5 - d a y t i m e   a c t i v i t i e s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 3 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 	 < e m p l o y S t a t u s > 2 2 4 3 6 6 0 0 4 < / e m p l o y S t a t u s > 
 
 	 	 	 	 < e d u c a t i o n P r o g r a m S t a t u s > 2 2 4 8 7 0 0 0 1 < / e d u c a t i o n P r o g r a m S t a t u s > 
 
 	 	 	 	 < r i s k U n e m p l o y m e n t L i s t > 
 
 	 	 	 	 	 < r i s k U n e m p l o y m e n t > U D E R - 0 2 < / r i s k U n e m p l o y m e n t > 
 
 	 	 	 	 	 < r i s k U n e m p l o y m e n t > U D E R - 0 3 < / r i s k U n e m p l o y m e n t > 
 
 	 	 	 	 	 < r i s k U n e m p l o y m e n t > U D E R - 0 6 < / r i s k U n e m p l o y m e n t > 
 
 	 	 	 	 < / r i s k U n e m p l o y m e n t L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 6 - p h y s i c a l   h e a l t h " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < m e d i c a l C o n d i t i o n L i s t   a u t i s m D e t a i l = " a u t i s m   s p e c i f i c   t e x t "   o t h e r D e t a i l = " o t h e r   m e d i c a l   t e x t " > 
 
 	 	 	 	 	 < m e d i c a l C o n d i t i o n > 4 0 8 8 5 6 0 0 3 < / m e d i c a l C o n d i t i o n > 
 
 	 	 	 	 	 < m e d i c a l C o n d i t i o n > 4 1 0 5 1 5 0 0 3 < / m e d i c a l C o n d i t i o n > 
 
 	 	 	 	 	 < m e d i c a l C o n d i t i o n > 4 1 4 9 1 6 0 0 1 < / m e d i c a l C o n d i t i o n > 
 
 	 	 	 	 < / m e d i c a l C o n d i t i o n L i s t > 
 
 	 	 	 	 < p h y s i c a l H e a l t h C o n c e r n > T R U E < / p h y s i c a l H e a l t h C o n c e r n > 
 
 	 	 	 	 < c o n c e r n A r e a L i s t   o t h e r C o n c e r n A r e a = " o t h e r   c o n c e r n   t e x t " > 
 
 	 	 	 	 	 < c o n c e r n A r e a > 1 1 9 4 1 5 0 0 7 < / c o n c e r n A r e a > 
 
 	 	 	 	 	 < c o n c e r n A r e a > 1 1 8 9 5 2 0 0 5 < / c o n c e r n A r e a > 
 
 	 	 	 	 	 < c o n c e r n A r e a > 4 1 0 5 1 5 0 0 3 < / c o n c e r n A r e a > 
 
 	 	 	 	 < / c o n c e r n A r e a L i s t > 
 
 	 	 	 	 < m e d i c a t i o n L i s t > 
 
 	 	 	 	 	 < m e d i c a t i o n D e t a i l   t a k e n A s P r e s c r i b e d = " T R U E "   i s H e l p P r o v i d e d = " F A L S E "   i s H e l p N e e d e d = " T R U E " / > 
 
 	 	 	 	 	 < m e d i c a t i o n D e t a i l   t a k e n A s P r e s c r i b e d = " F A L S E "   i s H e l p P r o v i d e d = " U N K "   i s H e l p N e e d e d = " T R U E " / > 
 
 	 	 	 	 	 < m e d i c a t i o n D e t a i l   t a k e n A s P r e s c r i b e d = " U N K "   i s H e l p P r o v i d e d = " T R U E "   i s H e l p N e e d e d = " F A L S E " / > 
 
 	 	 	 	 < / m e d i c a t i o n L i s t > 
 
 	 	 	 	 < s i d e E f f e c t s > T R U E < / s i d e E f f e c t s > 
 
 	 	 	 	 < d a i l y L i v i n g A f f e c t e d > T R U E < / d a i l y L i v i n g A f f e c t e d > 
 
 	 	 	 	 < s i d e E f f e c t s D e t a i l L i s t   o t h e r S i d e E f f e c t s D e t a i l = " o t h e r   s i d e   e f f e c t   t e x t " > 
 
 	 	 	 	 	 < s i d e E f f e c t s D e t a i l > 2 4 6 6 3 6 0 0 8 < / s i d e E f f e c t s D e t a i l > 
 
 	 	 	 	 	 < s i d e E f f e c t s D e t a i l > 5 3 6 1 9 0 0 0 < / s i d e E f f e c t s D e t a i l > 
 
 	 	 	 	 	 < s i d e E f f e c t s D e t a i l > 4 1 0 5 1 5 0 0 3 < / s i d e E f f e c t s D e t a i l > 
 
 	 	 	 	 < / s i d e E f f e c t s D e t a i l L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 7 - p s y c h o t i c   s y m p t o m s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " 0 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 	 < h o s p i t a l i z e d P a s t T w o Y e a r s > T R U E < / h o s p i t a l i z e d P a s t T w o Y e a r s > 
 
 	 	 	 	 < t o t a l A d m i s s i o n s > 5 < / t o t a l A d m i s s i o n s > 
 
 	 	 	 	 < t o t a l H o s p i t a l D a y s > 7 8 < / t o t a l H o s p i t a l D a y s > 
 
 	 	 	 	 < c o m m u n i t y T r e a t O r d e r > 0 1 5 - 0 3 < / c o m m u n i t y T r e a t O r d e r > 
 
 	 	 	 	 < s y m p t o m L i s t   o t h e r S y m p t o m = " o t h e r   s y m p t o m   t e x t " > 
 
 	 	 	 	 	 < s y m p t o m > 4 1 0 5 1 6 0 0 2 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 7 5 4 0 8 0 0 8 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 2 0 7 3 0 0 0 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 1 4 0 2 0 0 1 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 5 5 9 2 9 0 0 7 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 4 1 0 5 1 5 0 0 3 < / s y m p t o m > 
 
 	 	 	 	 < / s y m p t o m L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 8 - c o n d i t i o n   a n d   t r e a t m e n t " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 	 < d i a g n o s t i c L i s t > 
 
 	 	 	 	 	 < d i a g n o s t i c > 2 7 7 6 0 0 0 < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > M D G M C < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > 8 7 8 5 8 0 0 2 < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > S R D D H < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > D H < / d i a g n o s t i c > 
 
 	 	 	 	 < / d i a g n o s t i c L i s t > 
 
 	 	 	 	 < o t h e r I l l n e s s L i s t > 
 
 	 	 	 	 	 < o t h e r I l l n e s s > 0 1 6 A - 0 1 < / o t h e r I l l n e s s > 
 
 	 	 	 	 	 < o t h e r I l l n e s s > 0 1 6 A - 0 3 < / o t h e r I l l n e s s > 
 
 	 	 	 	 < / o t h e r I l l n e s s L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 9 - p s y c h o l o g i c a l   d i s t r e s s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 0 - s a f e t y   t o   s e l f " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < s u i c i d e A t t e m p t > T R U E < / s u i c i d e A t t e m p t > 
 
 	 	 	 	 < s u i c i d e T h o u g h t s > T R U E < / s u i c i d e T h o u g h t s > 
 
 	 	 	 	 < s a f e t y C o n c e r n S e l f > T R U E < / s a f e t y C o n c e r n S e l f > 
 
 	 	 	 	 < s a f e t y T o S e l f R i s k L i s t   o t h e r S a f e t y T o S e l f R i s k = " o t h e r   r i s k   t o   s e l f   t e x t " > 
 
 	 	 	 	 	 < s a f e t y T o S e l f R i s k > 2 2 5 9 1 5 0 0 6 < / s a f e t y T o S e l f R i s k > 
 
 	 	 	 	 	 < s a f e t y T o S e l f R i s k > 4 0 1 2 0 6 0 0 8 < / s a f e t y T o S e l f R i s k > 
 
 	 	 	 	 	 < s a f e t y T o S e l f R i s k > 4 1 0 5 1 5 0 0 3 < / s a f e t y T o S e l f R i s k > 
 
 	 	 	 	 < / s a f e t y T o S e l f R i s k L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 1 - s a f e t y   t o   o t h e r s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " 9 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 2 - a l c o h o l " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " 0 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 	 < d r i n k A l c o h o l   q u a n t i t y = " 6 "   f r e q u e n c y = " 1 " / > 
 
 	 	 	 	 < s t a g e O f C h a n g e A l c o h o l > 5 < / s t a g e O f C h a n g e A l c o h o l > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 3 - d r u g s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 2 "   c l i e n t = " 2 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 	 < d r u g U s e L i s t   i n j e c t e d = " 6 " > 
 
 	 	 	 	 	 < d r u g U s e   n a m e = " 3 9 8 7 0 5 0 0 4 "   f r e q u e n c y = " 5 " / > 
 
 	 	 	 	 	 < d r u g U s e   n a m e = " 2 2 6 0 4 4 0 0 4 "   f r e q u e n c y = " 6 " / > 
 
 	 	 	 	 	 < d r u g U s e   n a m e = " 4 1 0 5 1 5 0 0 3 "   f r e q u e n c y = " 5 " / > 
 
 	 	 	 	 < / d r u g U s e L i s t > 
 
 	 	 	 	 < s t a g e O f C h a n g e D r u g s > 2 < / s t a g e O f C h a n g e D r u g s > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 4 - o t h e r   a d d i c t i o n s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " 9 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 	 < a d d i c t i o n T y p e L i s t   o t h e r A d d i c t i o n T y p e = " o t h e r   a d d i c t i o n   t e x t " > 
 
 	 	 	 	 	 < a d d i c t i o n T y p e > 1 0 5 5 2 3 0 0 9 < / a d d i c t i o n T y p e > 
 
 	 	 	 	 	 < a d d i c t i o n T y p e > 5 6 2 9 4 0 0 8 < / a d d i c t i o n T y p e > 
 
 	 	 	 	 	 < a d d i c t i o n T y p e > 4 1 0 5 1 5 0 0 3 < / a d d i c t i o n T y p e > 
 
 	 	 	 	 < / a d d i c t i o n T y p e L i s t > 
 
 	 	 	 	 < s t a g e O f C h a n g e A d d i c t i o n s > 3 < / s t a g e O f C h a n g e A d d i c t i o n s > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 5 - c o m p a n y " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 2 " / > 
 
 	 	 	 	 < c h a n g e d S o c i a l P a t t e r n s > C D A < / c h a n g e d S o c i a l P a t t e r n s > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 6 - i n t i m a t e   r e l a t i o n s h i p s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 7 - s e x u a l   e x p r e s s i o n " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " 0 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 8 - c h i l d c a r e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 9 - o t h e r   d e p e n d e n t s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 0 - b a s i c   e d u c a t i o n " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < h i g h e s t E d u c a t i o n L e v e l > H L E S - 6 < / h i g h e s t E d u c a t i o n L e v e l > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 1 - t e l e p h o n e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " 9 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 2 - t r a n s p o r t " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " 9 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 3 - m o n e y " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " 9 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 	 < s o u r c e O f I n c o m e > 0 3 1 - 1 0 < / s o u r c e O f I n c o m e > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 4 - b e n e f i t s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " 9 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 < / O C A N D o m a i n s > 
 
 	 	 < a d d i t i o n a l E l e m e n t s > 
 
 	 	 	 < ! - -   B e g i n   F r e e   T e x t   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < c l i e n t H o p e s F o r F u t u r e / > 
 
 	 	 	 < c l i e n t N e e d T o G e t T h e r e / > 
 
 	 	 	 < c l i e n t V i e w M e n t a l H e a l t h / > 
 
 	 	 	 < c l i e n t S p i r i t u a l i t y I m p o r t a n c e / > 
 
 	 	 	 < c l i e n t C u l t u r e H e r i t a g e I m p o r t a n c e / > 
 
 	 	 	 < ! - -   E n d   F r e e   T e x t   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < p r e s e n t i n g I s s u e L i s t > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 5 " / > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 9 " / > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 1 1 " / > 
 
 	 	 	 < / p r e s e n t i n g I s s u e L i s t > 
 
 	 	 	 < a c t i o n L i s t > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 1 "   d o m a i n = " 0 1 - a c c o m m o d a t i o n " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 2 "   d o m a i n = " 0 2 - f o o d " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 3 "   d o m a i n = " 2 1 - t e l e p h o n e " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 4 "   d o m a i n = " 2 2 - t r a n s p o r t " / > 
 
 	 	 	 < / a c t i o n L i s t > 
 
 	 	 	 < r e f e r r a l L i s t > 
 
 	 	 	 	 < r e f e r r a l   o p t i m a l = " A D D "   s p e c i f y O p t i m a l = " s p e c i f y   t e x t   f o r   o p t i m a l   r e f e r r a l "   a c t u a l = " C H I "   s p e c i f y A c t u a l = " s p e c i f y   t e x t   f o r   a c t u a l   r e f e r r a l "   d i f f e r e n c e R e a s o n = " R D - 1 "   s t a t u s = " R S - 2 " / > 
 
 	 	 	 	 < r e f e r r a l   o p t i m a l = " C H I "   s p e c i f y O p t i m a l = " s p e c i f y   t e x t   f o r   o p t i m a l   r e f e r r a l "   a c t u a l = " A D D "   s p e c i f y A c t u a l = " s p e c i f y   t e x t   f o r   a c t u a l   r e f e r r a l "   d i f f e r e n c e R e a s o n = " R D - 3 "   s t a t u s = " R S - 1 " / > 
 
 	 	 	 < / r e f e r r a l L i s t > 
 
 	 	 < / a d d i t i o n a l E l e m e n t s > 
 
 	 < / O C A N S u b m i s s i o n R e c o r d > 
 
 
 
 	 < ! - -   a s s e s s m e n t   r e c o r d #   2 :   t h i s   a s s e s s m e n t   s h o w s   s o m e   o p t i o n a l   T A G s   a l l o w   f o r   N u l l   v a l u e s   - - > 
 
 	 < O C A N S u b m i s s i o n R e c o r d   a s s e s s m e n t I D = " 1 0 0 2 "   s t a r t D a t e = " 2 0 0 9 - 0 1 - 0 5 Z "   c o m p l e t i o n D a t e = " 2 0 0 9 - 0 1 - 2 7 Z "   a s s e s s m e n t S t a t u s = " C o m p l e t e " > 
 
 	 	 < o r g a n i z a t i o n R e c o r d > 
 
 	 	 	 < s e r v i c e O r g   n a m e = " N E   L H I N   T e s t   O r g "   n u m b e r = " 1 2 3 " / > 
 
 	 	 	 < p r o g r a m   n a m e = " I n t e n s i v e   C a s e   M a n a g e m e n t "   n u m b e r = " 3 0 2 5 " / > 
 
 	 	 	 < M I S F u n c t i o n   v a l u e = " 7 2 5   1 0   7 6   9 6 " / > 
 
 	 	 < / o r g a n i z a t i o n R e c o r d > 
 
 	 	 < c l i e n t R e c o r d > 
 
 	 	 	 < c l i e n t I D   o r g C l i e n t I D = " 1 2 3 1 0 0 " / > 
 
 	 	 	 < ! - -   B e g i n   P H I   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < c l i e n t N a m e   l a s t = " "   f i r s t = " " / > 
 
 	 	 	 < c l i e n t A d d r e s s   l i n e 1 = " "   l i n e 2 = " "   c i t y = " "   p r o v i n c e = " "   p o s t a l C o d e = " " / > 
 
 	 	 	 < c l i e n t P h o n e / > 
 
 	 	 	 < c l i e n t O H I P   n u m b e r = " "   v e r s i o n = " " / > 
 
 	 	 	 < c l i e n t C u l t u r e / > 
 
 	 	 	 < ! - -   E n d   P H I   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < r e a s o n F o r A s s e s s m e n t   v a l u e = " I A "   o t h e r = " " / > 
 
 	 	 	 < c l i e n t C o n t a c t > 
 
 	 	 	 	 < d o c t o r C o n t a c t   d o c t o r = " F A L S E "     l a s t S e e n = " " / > 
 
 	 	 	 	 < p s y c h i a t r i s t C o n t a c t   p s y c h i a t r i s t = " N A "   l a s t S e e n = " " / > 
 
 	 	 	 	 < o t h e r P r a c t i t i o n e r C o n t a c t / > 
 
 	 	 	 	 < o t h e r A g e n c y C o n t a c t / > 
 
 	 	 	 < / c l i e n t C o n t a c t > 
 
 	 	 	 < s e r v i c e R e c i p i e n t L o c a t i o n > 0 1 0 - 1 5 < / s e r v i c e R e c i p i e n t L o c a t i o n > 
 
 	 	 	 < s e r v i c e R e c i p i e n t L H I N > 1 2 < / s e r v i c e R e c i p i e n t L H I N > 
 
 	 	 	 < s e r v i c e D e l i v e r y L H I N > 5 < / s e r v i c e D e l i v e r y L H I N > 
 
 	 	 	 < c l i e n t D O B > 1 9 8 9 - 0 2 - 1 1 Z < / c l i e n t D O B > 
 
 	 	 	 < g e n d e r > F < / g e n d e r > 
 
 	 	 	 < m a r i t a l S t a t u s > 2 0 2 9 5 0 0 0 < / m a r i t a l S t a t u s > 
 
 	 	 	 < c l i e n t C a p a c i t y   p r o p e r t y = " F A L S E "   p e r s o n a l C a r e = " F A L S E "   l e g a l G u a r d i a n = " T R U E " / > 
 
 	 	 	 < r e f e r r a l S o u r c e > 0 1 8 - 0 2 < / r e f e r r a l S o u r c e > 
 
 	 	 	 < a b o r i g i n a l O r i g i n > 0 1 1 - 0 1 < / a b o r i g i n a l O r i g i n > 
 
 	 	 	 < c i t i z e n s h i p S t a t u s > R E F < / c i t i z e n s h i p S t a t u s > 
 
 	 	 	 < t i m e L i v e d I n C a n a d a   y e a r s = " 8 "   m o n t h s = " 1 " / > 
 
 	 	 	 < i m m i g E x p L i s t   o t h e r I m m i g E x p = " " > 
 
 	 	 	 	 < v a l u e > 3 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 7 < / v a l u e > 
 
 	 	 	 < / i m m i g E x p L i s t > 
 
 	 	 	 < d i s c r i m E x p L i s t   o t h e r D i s c r i m E x p = " " > 
 
 	 	 	 	 < v a l u e > 2 1 1 3 4 0 0 2 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 3 6 5 8 7 3 0 0 7 < / v a l u e > 
 
 	 	 	 < / d i s c r i m E x p L i s t > 
 
 	 	 	 < p r e f L a n g > f r a < / p r e f L a n g > 
 
 	 	 	 < s e r v i c e L a n g > e n g < / s e r v i c e L a n g > 
 
 	 	 	 < l e g a l I s s u e s > C D A < / l e g a l I s s u e s > 
 
 	 	 	 < l e g a l S t a t u s L i s t > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 4 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 5 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 6 < / l e g a l S t a t u s > 
 
 	 	 	 < / l e g a l S t a t u s L i s t > 
 
 	 	 	 < e x i t D i s p o s i t i o n > 0 1 9 - 0 1 < / e x i t D i s p o s i t i o n > 
 
 	 	 < / c l i e n t R e c o r d > 
 
 	 	 < O C A N D o m a i n s > 
 
 	 	 	 < d o m a i n   n a m e = " 0 1 - a c c o m m o d a t i o n " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " 2 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 	 < r e s i d e n c e T y p e > 0 2 4 - 1 9 < / r e s i d e n c e T y p e > 
 
 	 	 	 	 < r e s i d e n c e S u p p o r t > 2 4 A - 0 5 < / r e s i d e n c e S u p p o r t > 
 
 	 	 	 	 < l i v i n g A r r a n g e m e n t T y p e > 0 2 3 - 0 8 < / l i v i n g A r r a n g e m e n t T y p e > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 2 - f o o d " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " 9 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 3 - l o o k i n g   a f t e r   t h e   h o m e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " 2 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 4 - s e l f - c a r e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " 0 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 5 - d a y t i m e   a c t i v i t i e s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 3 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 	 < e m p l o y S t a t u s > 2 2 4 3 6 6 0 0 4 < / e m p l o y S t a t u s > 
 
 	 	 	 	 < e d u c a t i o n P r o g r a m S t a t u s > 2 2 4 8 7 0 0 0 1 < / e d u c a t i o n P r o g r a m S t a t u s > 
 
 	 	 	 	 < r i s k U n e m p l o y m e n t L i s t > 
 
 	 	 	 	 	 < r i s k U n e m p l o y m e n t > U D E R - 0 3 < / r i s k U n e m p l o y m e n t > 
 
 	 	 	 	 < / r i s k U n e m p l o y m e n t L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 6 - p h y s i c a l   h e a l t h " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < m e d i c a l C o n d i t i o n L i s t   a u t i s m D e t a i l = " "   o t h e r D e t a i l = " " > 
 
 	 	 	 	 	 < m e d i c a l C o n d i t i o n > 6 4 8 5 9 0 0 6 < / m e d i c a l C o n d i t i o n > 
 
 	 	 	 	 < / m e d i c a l C o n d i t i o n L i s t > 
 
 	 	 	 	 < p h y s i c a l H e a l t h C o n c e r n > F A L S E < / p h y s i c a l H e a l t h C o n c e r n > 
 
 	 	 	 	 < c o n c e r n A r e a L i s t > 
 
 	 	 	 	 < / c o n c e r n A r e a L i s t > 
 
 	 	 	 	 < m e d i c a t i o n L i s t > 
 
 	 	 	 	 < / m e d i c a t i o n L i s t > 
 
 	 	 	 	 < s i d e E f f e c t s > C D A < / s i d e E f f e c t s > 
 
 	 	 	 	 < d a i l y L i v i n g A f f e c t e d > T R U E < / d a i l y L i v i n g A f f e c t e d > 
 
 	 	 	 	 < s i d e E f f e c t s D e t a i l L i s t > 
 
 	 	 	 	 < / s i d e E f f e c t s D e t a i l L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 7 - p s y c h o t i c   s y m p t o m s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " 0 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 	 < h o s p i t a l i z e d P a s t T w o Y e a r s > T R U E < / h o s p i t a l i z e d P a s t T w o Y e a r s > 
 
 	 	 	 	 < t o t a l A d m i s s i o n s > 5 < / t o t a l A d m i s s i o n s > 
 
 	 	 	 	 < t o t a l H o s p i t a l D a y s > 7 8 < / t o t a l H o s p i t a l D a y s > 
 
 	 	 	 	 < c o m m u n i t y T r e a t O r d e r > 0 1 5 - 0 3 < / c o m m u n i t y T r e a t O r d e r > 
 
 	 	 	 	 < s y m p t o m L i s t   o t h e r S y m p t o m = " " > 
 
 	 	 	 	 	 < s y m p t o m > 4 1 0 5 1 6 0 0 2 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 7 5 4 0 8 0 0 8 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 2 0 7 3 0 0 0 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 1 4 0 2 0 0 1 < / s y m p t o m > 
 
 	 	 	 	 < / s y m p t o m L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 8 - c o n d i t i o n   a n d   t r e a t m e n t " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 	 < d i a g n o s t i c L i s t > 
 
 	 	 	 	 	 < d i a g n o s t i c > 2 7 7 6 0 0 0 < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > M D G M C < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > 8 7 8 5 8 0 0 2 < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > S R D D H < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > D H < / d i a g n o s t i c > 
 
 	 	 	 	 < / d i a g n o s t i c L i s t > 
 
 	 	 	 	 < o t h e r I l l n e s s L i s t > 
 
 	 	 	 	 	 < o t h e r I l l n e s s > 0 1 6 A - 0 1 < / o t h e r I l l n e s s > 
 
 	 	 	 	 	 < o t h e r I l l n e s s > 0 1 6 A - 0 2 < / o t h e r I l l n e s s > 
 
 	 	 	 	 < / o t h e r I l l n e s s L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 9 - p s y c h o l o g i c a l   d i s t r e s s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 0 - s a f e t y   t o   s e l f " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < s u i c i d e A t t e m p t > T R U E < / s u i c i d e A t t e m p t > 
 
 	 	 	 	 < s u i c i d e T h o u g h t s > T R U E < / s u i c i d e T h o u g h t s > 
 
 	 	 	 	 < s a f e t y C o n c e r n S e l f > T R U E < / s a f e t y C o n c e r n S e l f > 
 
 	 	 	 	 < s a f e t y T o S e l f R i s k L i s t   o t h e r S a f e t y T o S e l f R i s k = " " > 
 
 	 	 	 	 	 < s a f e t y T o S e l f R i s k > 2 2 5 9 1 5 0 0 6 < / s a f e t y T o S e l f R i s k > 
 
 	 	 	 	 	 < s a f e t y T o S e l f R i s k > 4 0 1 2 0 6 0 0 8 < / s a f e t y T o S e l f R i s k > 
 
 	 	 	 	 < / s a f e t y T o S e l f R i s k L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 1 - s a f e t y   t o   o t h e r s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " 9 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 2 - a l c o h o l " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " 0 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 	 < d r i n k A l c o h o l   q u a n t i t y = " 6 "   f r e q u e n c y = " 1 " / > 
 
 	 	 	 	 < s t a g e O f C h a n g e A l c o h o l > 5 < / s t a g e O f C h a n g e A l c o h o l > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 3 - d r u g s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 2 "   c l i e n t = " 2 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 	 < d r u g U s e L i s t   i n j e c t e d = " 6 " > 
 
 	 	 	 	 	 < d r u g U s e   n a m e = " 3 9 8 7 0 5 0 0 4 "   f r e q u e n c y = " 5 " / > 
 
 	 	 	 	 	 < d r u g U s e   n a m e = " 2 2 6 0 4 4 0 0 4 "   f r e q u e n c y = " 6 " / > 
 
 	 	 	 	 	 < d r u g U s e   n a m e = " 4 1 0 5 1 5 0 0 3 "   f r e q u e n c y = " 5 " / > 
 
 	 	 	 	 < / d r u g U s e L i s t > 
 
 	 	 	 	 < s t a g e O f C h a n g e D r u g s > 2 < / s t a g e O f C h a n g e D r u g s > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 4 - o t h e r   a d d i c t i o n s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " 9 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 	 < a d d i c t i o n T y p e L i s t   o t h e r A d d i c t i o n T y p e = " " > 
 
 	 	 	 	 	 < a d d i c t i o n T y p e > 1 0 5 5 2 3 0 0 9 < / a d d i c t i o n T y p e > 
 
 	 	 	 	 	 < a d d i c t i o n T y p e > 5 6 2 9 4 0 0 8 < / a d d i c t i o n T y p e > 
 
 	 	 	 	 < / a d d i c t i o n T y p e L i s t > 
 
 	 	 	 	 < s t a g e O f C h a n g e A d d i c t i o n s > 3 < / s t a g e O f C h a n g e A d d i c t i o n s > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 5 - c o m p a n y " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 2 " / > 
 
 	 	 	 	 < c h a n g e d S o c i a l P a t t e r n s > C D A < / c h a n g e d S o c i a l P a t t e r n s > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 6 - i n t i m a t e   r e l a t i o n s h i p s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 7 - s e x u a l   e x p r e s s i o n " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " 0 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 8 - c h i l d c a r e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 9 - o t h e r   d e p e n d e n t s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 0 - b a s i c   e d u c a t i o n " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < h i g h e s t E d u c a t i o n L e v e l > H L E S - 6 < / h i g h e s t E d u c a t i o n L e v e l > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 1 - t e l e p h o n e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " 9 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 2 - t r a n s p o r t " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " 9 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 3 - m o n e y " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " 9 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 	 < s o u r c e O f I n c o m e > 0 3 1 - 1 0 < / s o u r c e O f I n c o m e > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 4 - b e n e f i t s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " 9 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 < / O C A N D o m a i n s > 
 
 	 	 < a d d i t i o n a l E l e m e n t s > 
 
 	 	 	 < ! - -   B e g i n   F r e e   T e x t   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < c l i e n t H o p e s F o r F u t u r e / > 
 
 	 	 	 < c l i e n t N e e d T o G e t T h e r e / > 
 
 	 	 	 < c l i e n t V i e w M e n t a l H e a l t h / > 
 
 	 	 	 < c l i e n t S p i r i t u a l i t y I m p o r t a n c e / > 
 
 	 	 	 < c l i e n t C u l t u r e H e r i t a g e I m p o r t a n c e / > 
 
 	 	 	 < ! - -   E n d   F r e e   T e x t   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < p r e s e n t i n g I s s u e L i s t > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 5 " / > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 9 " / > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 1 1 " / > 
 
 	 	 	 < / p r e s e n t i n g I s s u e L i s t > 
 
 	 	 	 < a c t i o n L i s t > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 1 "   d o m a i n = " 2 1 - t e l e p h o n e " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 2 "   d o m a i n = " 2 2 - t r a n s p o r t " / > 
 
 	 	 	 < / a c t i o n L i s t > 
 
 	 	 	 < r e f e r r a l L i s t > 
 
 	 	 	 	 < r e f e r r a l   o p t i m a l = " A D D "   s p e c i f y O p t i m a l = " s p e c i f y   t e x t   f o r   o p t i m a l   r e f e r r a l "   a c t u a l = " C H I "   s p e c i f y A c t u a l = " s p e c i f y   t e x t   f o r   a c t u a l   r e f e r r a l "   d i f f e r e n c e R e a s o n = " R D - 1 "   s t a t u s = " R S - 2 " / > 
 
 	 	 	 < / r e f e r r a l L i s t > 
 
 	 	 < / a d d i t i o n a l E l e m e n t s > 
 
 	 < / O C A N S u b m i s s i o n R e c o r d > 
 
 
 
 	 < ! - -   a s s e s s m e n t   r e c o r d #   3 :   t h i s   a s s e s s m e n t   s h o w s   c l i e n t   d i d   n o t   a n s w e r   c l i e n t   v e r s i o n   o f   t h e   a s s e s s m e n t   t o o l   - - > 
 
 	 < O C A N S u b m i s s i o n R e c o r d   a s s e s s m e n t I D = " 1 0 0 3 "   s t a r t D a t e = " 2 0 0 9 - 0 1 - 0 2 Z "   c o m p l e t i o n D a t e = " 2 0 0 9 - 0 1 - 1 7 Z "   a s s e s s m e n t S t a t u s = " C o m p l e t e " > 
 
 	 	 < o r g a n i z a t i o n R e c o r d > 
 
 	 	 	 < s e r v i c e O r g   n a m e = " N E   L H I N   T e s t   O r g "   n u m b e r = " 1 2 3 " / > 
 
 	 	 	 < p r o g r a m   n a m e = " I n t e n s i v e   C a s e   M a n a g e m e n t "   n u m b e r = " 3 0 2 5 " / > 
 
 	 	 	 < M I S F u n c t i o n   v a l u e = " 7 2 5   5 1   7 6   2 0 " / > 
 
 	 	 < / o r g a n i z a t i o n R e c o r d > 
 
 	 	 < c l i e n t R e c o r d > 
 
 	 	 	 < c l i e n t I D   o r g C l i e n t I D = " 1 2 3 3 0 0 " / > 
 
 	 	 	 < ! - -   B e g i n   P H I   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < c l i e n t N a m e   l a s t = " "   f i r s t = " " / > 
 
 	 	 	 < c l i e n t A d d r e s s   l i n e 1 = " "   l i n e 2 = " "   c i t y = " "   p r o v i n c e = " "   p o s t a l C o d e = " " / > 
 
 	 	 	 < c l i e n t P h o n e / > 
 
 	 	 	 < c l i e n t O H I P   n u m b e r = " "   v e r s i o n = " " / > 
 
 	 	 	 < c l i e n t C u l t u r e / > 
 
 	 	 	 < ! - -   E n d   P H I   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < r e a s o n F o r A s s e s s m e n t   v a l u e = " O T H R "   o t h e r = " d e t a i l   t e x t   f o r   o t h e r   r e a s o n   o f   a s s e s s m e n t " / > 
 
 	 	 	 < c l i e n t C o n t a c t > 
 
 	 	 	 	 < d o c t o r C o n t a c t   d o c t o r = " T R U E "   l a s t S e e n = " L S - 6 " / > 
 
 	 	 	 	 < p s y c h i a t r i s t C o n t a c t   p s y c h i a t r i s t = " T R U E "   l a s t S e e n = " L S - 1 2 " / > 
 
 	 	 	 	 < o t h e r P r a c t i t i o n e r C o n t a c t   p r a c t i t i o n e r T y p e = " 3 1 1 1 "   l a s t S e e n = " L S - 1 " / > 
 
 	 	 	 	 < o t h e r P r a c t i t i o n e r C o n t a c t   p r a c t i t i o n e r T y p e = " 3 1 1 2 "   l a s t S e e n = " L S - 1 3 " / > 
 
 	 	 	 	 < o t h e r P r a c t i t i o n e r C o n t a c t   p r a c t i t i o n e r T y p e = " 4 2 1 7 "   l a s t S e e n = " L S - 6 " / > 
 
 	 	 	 	 < o t h e r A g e n c y C o n t a c t   l a s t S e e n = " L S - 1 2 " / > 
 
 	 	 	 < / c l i e n t C o n t a c t > 
 
 	 	 	 < s e r v i c e R e c i p i e n t L o c a t i o n > 0 1 0 - 5 2 < / s e r v i c e R e c i p i e n t L o c a t i o n > 
 
 	 	 	 < s e r v i c e R e c i p i e n t L H I N > 1 2 < / s e r v i c e R e c i p i e n t L H I N > 
 
 	 	 	 < s e r v i c e D e l i v e r y L H I N > 5 < / s e r v i c e D e l i v e r y L H I N > 
 
 	 	 	 < c l i e n t D O B > 2 0 0 1 - 0 8 - 2 2 Z < / c l i e n t D O B > 
 
 	 	 	 < g e n d e r > M < / g e n d e r > 
 
 	 	 	 < m a r i t a l S t a t u s > 1 2 5 6 8 1 0 0 6 < / m a r i t a l S t a t u s > 
 
 	 	 	 < c l i e n t C a p a c i t y   p r o p e r t y = " T R U E "   p e r s o n a l C a r e = " F A L S E "   l e g a l G u a r d i a n = " C D A " / > 
 
 	 	 	 < r e f e r r a l S o u r c e > 0 1 8 - 0 5 < / r e f e r r a l S o u r c e > 
 
 	 	 	 < a b o r i g i n a l O r i g i n > 0 1 1 - 0 2 < / a b o r i g i n a l O r i g i n > 
 
 	 	 	 < c i t i z e n s h i p S t a t u s > T R < / c i t i z e n s h i p S t a t u s > 
 
 	 	 	 < t i m e L i v e d I n C a n a d a   y e a r s = " 3 "   m o n t h s = " 8 " / > 
 
 	 	 	 < i m m i g E x p L i s t   o t h e r I m m i g E x p = " o t h e r   i m m i g r a t i o n   e x p e r i e n c e   t e x t " > 
 
 	 	 	 	 < v a l u e > 2 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 3 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 7 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 8 < / v a l u e > 
 
 	 	 	 < / i m m i g E x p L i s t > 
 
 	 	 	 < d i s c r i m E x p L i s t   o t h e r D i s c r i m E x p = " o t h e r   d i s c r i m i n a t i o n   e x p e r i e n c e   t e x t " > 
 
 	 	 	 	 < v a l u e > 2 1 1 3 4 0 0 2 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 3 6 5 8 7 3 0 0 7 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 4 1 0 5 1 5 0 0 3 < / v a l u e > 
 
 	 	 	 < / d i s c r i m E x p L i s t > 
 
 	 	 	 < p r e f L a n g > f r a < / p r e f L a n g > 
 
 	 	 	 < s e r v i c e L a n g > e n g < / s e r v i c e L a n g > 
 
 	 	 	 < l e g a l I s s u e s > C i v i l < / l e g a l I s s u e s > 
 
 	 	 	 < l e g a l S t a t u s L i s t > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 1 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 2 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 3 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 8 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 9 < / l e g a l S t a t u s > 
 
 	 	 	 < / l e g a l S t a t u s L i s t > 
 
 	 	 	 < e x i t D i s p o s i t i o n > 0 1 9 - 0 6 < / e x i t D i s p o s i t i o n > 
 
 	 	 < / c l i e n t R e c o r d > 
 
 	 	 < O C A N D o m a i n s > 
 
 	 	 	 < d o m a i n   n a m e = " 0 1 - a c c o m m o d a t i o n " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 	 < r e s i d e n c e T y p e > 0 2 4 - 1 9 < / r e s i d e n c e T y p e > 
 
 	 	 	 	 < r e s i d e n c e S u p p o r t > 2 4 A - 0 5 < / r e s i d e n c e S u p p o r t > 
 
 	 	 	 	 < l i v i n g A r r a n g e m e n t T y p e > 0 2 3 - 0 8 < / l i v i n g A r r a n g e m e n t T y p e > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 2 - f o o d " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 3 - l o o k i n g   a f t e r   t h e   h o m e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 4 - s e l f - c a r e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 5 - d a y t i m e   a c t i v i t i e s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 3 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 	 < e m p l o y S t a t u s > 2 2 4 3 6 6 0 0 4 < / e m p l o y S t a t u s > 
 
 	 	 	 	 < e d u c a t i o n P r o g r a m S t a t u s > 2 2 4 8 7 0 0 0 1 < / e d u c a t i o n P r o g r a m S t a t u s > 
 
 	 	 	 	 < r i s k U n e m p l o y m e n t L i s t > 
 
 	 	 	 	 	 < r i s k U n e m p l o y m e n t > U D E R - 0 2 < / r i s k U n e m p l o y m e n t > 
 
 	 	 	 	 	 < r i s k U n e m p l o y m e n t > U D E R - 0 3 < / r i s k U n e m p l o y m e n t > 
 
 	 	 	 	 	 < r i s k U n e m p l o y m e n t > U D E R - 0 6 < / r i s k U n e m p l o y m e n t > 
 
 	 	 	 	 < / r i s k U n e m p l o y m e n t L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 6 - p h y s i c a l   h e a l t h " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < m e d i c a l C o n d i t i o n L i s t   a u t i s m D e t a i l = " a u t i s m   s p e c i f i c   t e x t "   o t h e r D e t a i l = " o t h e r   m e d i c a l   t e x t " > 
 
 	 	 	 	 	 < m e d i c a l C o n d i t i o n > 4 0 8 8 5 6 0 0 3 < / m e d i c a l C o n d i t i o n > 
 
 	 	 	 	 	 < m e d i c a l C o n d i t i o n > 4 1 0 5 1 5 0 0 3 < / m e d i c a l C o n d i t i o n > 
 
 	 	 	 	 	 < m e d i c a l C o n d i t i o n > 4 1 4 9 1 6 0 0 1 < / m e d i c a l C o n d i t i o n > 
 
 	 	 	 	 < / m e d i c a l C o n d i t i o n L i s t > 
 
 	 	 	 	 < p h y s i c a l H e a l t h C o n c e r n > T R U E < / p h y s i c a l H e a l t h C o n c e r n > 
 
 	 	 	 	 < c o n c e r n A r e a L i s t   o t h e r C o n c e r n A r e a = " o t h e r   c o n c e r n   t e x t " > 
 
 	 	 	 	 	 < c o n c e r n A r e a > 1 1 9 4 1 5 0 0 7 < / c o n c e r n A r e a > 
 
 	 	 	 	 	 < c o n c e r n A r e a > 1 1 8 9 5 2 0 0 5 < / c o n c e r n A r e a > 
 
 	 	 	 	 	 < c o n c e r n A r e a > 4 1 0 5 1 5 0 0 3 < / c o n c e r n A r e a > 
 
 	 	 	 	 < / c o n c e r n A r e a L i s t > 
 
 	 	 	 	 < m e d i c a t i o n L i s t > 
 
 	 	 	 	 	 < m e d i c a t i o n D e t a i l   t a k e n A s P r e s c r i b e d = " T R U E "   i s H e l p P r o v i d e d = " F A L S E "   i s H e l p N e e d e d = " T R U E " / > 
 
 	 	 	 	 	 < m e d i c a t i o n D e t a i l   t a k e n A s P r e s c r i b e d = " F A L S E "   i s H e l p P r o v i d e d = " U N K "   i s H e l p N e e d e d = " T R U E " / > 
 
 	 	 	 	 	 < m e d i c a t i o n D e t a i l   t a k e n A s P r e s c r i b e d = " U N K "   i s H e l p P r o v i d e d = " T R U E "   i s H e l p N e e d e d = " F A L S E " / > 
 
 	 	 	 	 < / m e d i c a t i o n L i s t > 
 
 	 	 	 	 < s i d e E f f e c t s > T R U E < / s i d e E f f e c t s > 
 
 	 	 	 	 < d a i l y L i v i n g A f f e c t e d > T R U E < / d a i l y L i v i n g A f f e c t e d > 
 
 	 	 	 	 < s i d e E f f e c t s D e t a i l L i s t   o t h e r S i d e E f f e c t s D e t a i l = " o t h e r   s i d e   e f f e c t   t e x t " > 
 
 	 	 	 	 	 < s i d e E f f e c t s D e t a i l > 2 4 6 6 3 6 0 0 8 < / s i d e E f f e c t s D e t a i l > 
 
 	 	 	 	 	 < s i d e E f f e c t s D e t a i l > 5 3 6 1 9 0 0 0 < / s i d e E f f e c t s D e t a i l > 
 
 	 	 	 	 	 < s i d e E f f e c t s D e t a i l > 4 1 0 5 1 5 0 0 3 < / s i d e E f f e c t s D e t a i l > 
 
 	 	 	 	 < / s i d e E f f e c t s D e t a i l L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 7 - p s y c h o t i c   s y m p t o m s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 	 < h o s p i t a l i z e d P a s t T w o Y e a r s > T R U E < / h o s p i t a l i z e d P a s t T w o Y e a r s > 
 
 	 	 	 	 < t o t a l A d m i s s i o n s > 5 < / t o t a l A d m i s s i o n s > 
 
 	 	 	 	 < t o t a l H o s p i t a l D a y s > 7 8 < / t o t a l H o s p i t a l D a y s > 
 
 	 	 	 	 < c o m m u n i t y T r e a t O r d e r > 0 1 5 - 0 3 < / c o m m u n i t y T r e a t O r d e r > 
 
 	 	 	 	 < s y m p t o m L i s t   o t h e r S y m p t o m = " o t h e r   s y m p t o m   t e x t " > 
 
 	 	 	 	 	 < s y m p t o m > 4 1 0 5 1 6 0 0 2 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 7 5 4 0 8 0 0 8 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 2 0 7 3 0 0 0 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 1 4 0 2 0 0 1 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 5 5 9 2 9 0 0 7 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 4 1 0 5 1 5 0 0 3 < / s y m p t o m > 
 
 	 	 	 	 < / s y m p t o m L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 8 - c o n d i t i o n   a n d   t r e a t m e n t " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 	 < d i a g n o s t i c L i s t > 
 
 	 	 	 	 	 < d i a g n o s t i c > 2 7 7 6 0 0 0 < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > M D G M C < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > 8 7 8 5 8 0 0 2 < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > S R D D H < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > D H < / d i a g n o s t i c > 
 
 	 	 	 	 < / d i a g n o s t i c L i s t > 
 
 	 	 	 	 < o t h e r I l l n e s s L i s t > 
 
 	 	 	 	 	 < o t h e r I l l n e s s > 0 1 6 A - 0 1 < / o t h e r I l l n e s s > 
 
 	 	 	 	 	 < o t h e r I l l n e s s > 0 1 6 A - 0 3 < / o t h e r I l l n e s s > 
 
 	 	 	 	 < / o t h e r I l l n e s s L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 9 - p s y c h o l o g i c a l   d i s t r e s s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 0 - s a f e t y   t o   s e l f " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < s u i c i d e A t t e m p t > T R U E < / s u i c i d e A t t e m p t > 
 
 	 	 	 	 < s u i c i d e T h o u g h t s > T R U E < / s u i c i d e T h o u g h t s > 
 
 	 	 	 	 < s a f e t y C o n c e r n S e l f > T R U E < / s a f e t y C o n c e r n S e l f > 
 
 	 	 	 	 < s a f e t y T o S e l f R i s k L i s t   o t h e r S a f e t y T o S e l f R i s k = " o t h e r   r i s k   t o   s e l f   t e x t " > 
 
 	 	 	 	 	 < s a f e t y T o S e l f R i s k > 2 2 5 9 1 5 0 0 6 < / s a f e t y T o S e l f R i s k > 
 
 	 	 	 	 	 < s a f e t y T o S e l f R i s k > 4 0 1 2 0 6 0 0 8 < / s a f e t y T o S e l f R i s k > 
 
 	 	 	 	 	 < s a f e t y T o S e l f R i s k > 4 1 0 5 1 5 0 0 3 < / s a f e t y T o S e l f R i s k > 
 
 	 	 	 	 < / s a f e t y T o S e l f R i s k L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 1 - s a f e t y   t o   o t h e r s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 2 - a l c o h o l " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 	 < d r i n k A l c o h o l   q u a n t i t y = " 6 "   f r e q u e n c y = " 1 " / > 
 
 	 	 	 	 < s t a g e O f C h a n g e A l c o h o l > 5 < / s t a g e O f C h a n g e A l c o h o l > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 3 - d r u g s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 2 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 	 < d r u g U s e L i s t   i n j e c t e d = " 6 " > 
 
 	 	 	 	 	 < d r u g U s e   n a m e = " 3 9 8 7 0 5 0 0 4 "   f r e q u e n c y = " 5 " / > 
 
 	 	 	 	 	 < d r u g U s e   n a m e = " 2 2 6 0 4 4 0 0 4 "   f r e q u e n c y = " 6 " / > 
 
 	 	 	 	 	 < d r u g U s e   n a m e = " 4 1 0 5 1 5 0 0 3 "   f r e q u e n c y = " 5 " / > 
 
 	 	 	 	 < / d r u g U s e L i s t > 
 
 	 	 	 	 < s t a g e O f C h a n g e D r u g s > 2 < / s t a g e O f C h a n g e D r u g s > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 4 - o t h e r   a d d i c t i o n s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 	 < a d d i c t i o n T y p e L i s t   o t h e r A d d i c t i o n T y p e = " o t h e r   a d d i c t i o n   t e x t " > 
 
 	 	 	 	 	 < a d d i c t i o n T y p e > 1 0 5 5 2 3 0 0 9 < / a d d i c t i o n T y p e > 
 
 	 	 	 	 	 < a d d i c t i o n T y p e > 5 6 2 9 4 0 0 8 < / a d d i c t i o n T y p e > 
 
 	 	 	 	 	 < a d d i c t i o n T y p e > 4 1 0 5 1 5 0 0 3 < / a d d i c t i o n T y p e > 
 
 	 	 	 	 < / a d d i c t i o n T y p e L i s t > 
 
 	 	 	 	 < s t a g e O f C h a n g e A d d i c t i o n s > 3 < / s t a g e O f C h a n g e A d d i c t i o n s > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 5 - c o m p a n y " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 2 " / > 
 
 	 	 	 	 < c h a n g e d S o c i a l P a t t e r n s > C D A < / c h a n g e d S o c i a l P a t t e r n s > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 6 - i n t i m a t e   r e l a t i o n s h i p s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 7 - s e x u a l   e x p r e s s i o n " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 8 - c h i l d c a r e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 9 - o t h e r   d e p e n d e n t s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 0 - b a s i c   e d u c a t i o n " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < h i g h e s t E d u c a t i o n L e v e l > H L E S - 6 < / h i g h e s t E d u c a t i o n L e v e l > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 1 - t e l e p h o n e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 2 - t r a n s p o r t " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 3 - m o n e y " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 	 < s o u r c e O f I n c o m e > 0 3 1 - 1 0 < / s o u r c e O f I n c o m e > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 4 - b e n e f i t s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 < / O C A N D o m a i n s > 
 
 	 	 < a d d i t i o n a l E l e m e n t s > 
 
 	 	 	 < ! - -   B e g i n   F r e e   T e x t   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < c l i e n t H o p e s F o r F u t u r e / > 
 
 	 	 	 < c l i e n t N e e d T o G e t T h e r e / > 
 
 	 	 	 < c l i e n t V i e w M e n t a l H e a l t h / > 
 
 	 	 	 < c l i e n t S p i r i t u a l i t y I m p o r t a n c e / > 
 
 	 	 	 < c l i e n t C u l t u r e H e r i t a g e I m p o r t a n c e / > 
 
 	 	 	 < ! - -   E n d   F r e e   T e x t   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < p r e s e n t i n g I s s u e L i s t > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 5 " / > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 9 " / > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 1 1 " / > 
 
 	 	 	 < / p r e s e n t i n g I s s u e L i s t > 
 
 	 	 	 < a c t i o n L i s t > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 1 "   d o m a i n = " 0 1 - a c c o m m o d a t i o n " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 2 "   d o m a i n = " 0 2 - f o o d " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 3 "   d o m a i n = " 2 1 - t e l e p h o n e " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 4 "   d o m a i n = " 2 2 - t r a n s p o r t " / > 
 
 	 	 	 < / a c t i o n L i s t > 
 
 	 	 	 < r e f e r r a l L i s t > 
 
 	 	 	 	 < r e f e r r a l   o p t i m a l = " A D D "   s p e c i f y O p t i m a l = " s p e c i f y   t e x t   f o r   o p t i m a l   r e f e r r a l "   a c t u a l = " C H I "   s p e c i f y A c t u a l = " s p e c i f y   t e x t   f o r   a c t u a l   r e f e r r a l "   d i f f e r e n c e R e a s o n = " R D - 1 "   s t a t u s = " R S - 2 " / > 
 
 	 	 	 	 < r e f e r r a l   o p t i m a l = " C H I "   s p e c i f y O p t i m a l = " s p e c i f y   t e x t   f o r   o p t i m a l   r e f e r r a l "   a c t u a l = " A D D "   s p e c i f y A c t u a l = " s p e c i f y   t e x t   f o r   a c t u a l   r e f e r r a l "   d i f f e r e n c e R e a s o n = " R D - 3 "   s t a t u s = " R S - 1 " / > 
 
 	 	 	 < / r e f e r r a l L i s t > 
 
 	 	 < / a d d i t i o n a l E l e m e n t s > 
 
 	 < / O C A N S u b m i s s i o n R e c o r d > 
 
 
 
 	 < ! - -   a s s e s s m e n t   r e c o r d #   4 :   t h i s   a s s e s s m e n t   s h o w s   c l i e n t   a n s w e r e d   s o m e   o f   t h e   c l i e n t   v e r s i o n   o f   t h e   a s s e s s m e n t   t o o l   - - > 
 
 	 < O C A N S u b m i s s i o n R e c o r d   a s s e s s m e n t I D = " 1 0 0 4 "   s t a r t D a t e = " 2 0 0 9 - 0 1 - 0 7 Z "   c o m p l e t i o n D a t e = " 2 0 0 9 - 0 1 - 1 9 Z "   a s s e s s m e n t S t a t u s = " C o m p l e t e " > 
 
 	 	 < o r g a n i z a t i o n R e c o r d > 
 
 	 	 	 < s e r v i c e O r g   n a m e = " N E   L H I N   T e s t   O r g "   n u m b e r = " 1 2 3 " / > 
 
 	 	 	 < p r o g r a m   n a m e = " I n t e n s i v e   C a s e   M a n a g e m e n t "   n u m b e r = " 3 0 2 5 " / > 
 
 	 	 	 < M I S F u n c t i o n   v a l u e = " 7 2 5   5 1   7 6   2 0 " / > 
 
 	 	 < / o r g a n i z a t i o n R e c o r d > 
 
 	 	 < c l i e n t R e c o r d > 
 
 	 	 	 < c l i e n t I D   o r g C l i e n t I D = " 1 2 3 4 0 0 " / > 
 
 	 	 	 < ! - -   B e g i n   P H I   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < c l i e n t N a m e   l a s t = " "   f i r s t = " " / > 
 
 	 	 	 < c l i e n t A d d r e s s   l i n e 1 = " "   l i n e 2 = " "   c i t y = " "   p r o v i n c e = " "   p o s t a l C o d e = " " / > 
 
 	 	 	 < c l i e n t P h o n e / > 
 
 	 	 	 < c l i e n t O H I P   n u m b e r = " "   v e r s i o n = " " / > 
 
 	 	 	 < c l i e n t C u l t u r e / > 
 
 	 	 	 < ! - -   E n d   P H I   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < r e a s o n F o r A s s e s s m e n t   v a l u e = " O T H R "   o t h e r = " d e t a i l   t e x t   f o r   o t h e r   r e a s o n   o f   a s s e s s m e n t " / > 
 
 	 	 	 < c l i e n t C o n t a c t > 
 
 	 	 	 	 < d o c t o r C o n t a c t   d o c t o r = " T R U E "   l a s t S e e n = " L S - 6 " / > 
 
 	 	 	 	 < p s y c h i a t r i s t C o n t a c t   p s y c h i a t r i s t = " T R U E "   l a s t S e e n = " L S - 1 2 " / > 
 
 	 	 	 	 < o t h e r P r a c t i t i o n e r C o n t a c t   p r a c t i t i o n e r T y p e = " 3 1 1 1 "   l a s t S e e n = " L S - 1 " / > 
 
 	 	 	 	 < o t h e r P r a c t i t i o n e r C o n t a c t   p r a c t i t i o n e r T y p e = " 3 1 1 2 "   l a s t S e e n = " L S - 1 3 " / > 
 
 	 	 	 	 < o t h e r P r a c t i t i o n e r C o n t a c t   p r a c t i t i o n e r T y p e = " 4 2 1 7 "   l a s t S e e n = " L S - 6 " / > 
 
 	 	 	 	 < o t h e r A g e n c y C o n t a c t   l a s t S e e n = " L S - 1 2 " / > 
 
 	 	 	 < / c l i e n t C o n t a c t > 
 
 	 	 	 < s e r v i c e R e c i p i e n t L o c a t i o n > 0 1 0 - 5 2 < / s e r v i c e R e c i p i e n t L o c a t i o n > 
 
 	 	 	 < s e r v i c e R e c i p i e n t L H I N > 1 2 < / s e r v i c e R e c i p i e n t L H I N > 
 
 	 	 	 < s e r v i c e D e l i v e r y L H I N > 5 < / s e r v i c e D e l i v e r y L H I N > 
 
 	 	 	 < c l i e n t D O B > 1 9 6 6 - 1 1 - 1 5 Z < / c l i e n t D O B > 
 
 	 	 	 < g e n d e r > M < / g e n d e r > 
 
 	 	 	 < m a r i t a l S t a t u s > 1 2 5 6 8 1 0 0 6 < / m a r i t a l S t a t u s > 
 
 	 	 	 < c l i e n t C a p a c i t y   p r o p e r t y = " T R U E "   p e r s o n a l C a r e = " F A L S E "   l e g a l G u a r d i a n = " C D A " / > 
 
 	 	 	 < r e f e r r a l S o u r c e > 0 1 8 - 0 5 < / r e f e r r a l S o u r c e > 
 
 	 	 	 < a b o r i g i n a l O r i g i n > 0 1 1 - 0 2 < / a b o r i g i n a l O r i g i n > 
 
 	 	 	 < c i t i z e n s h i p S t a t u s > P R < / c i t i z e n s h i p S t a t u s > 
 
 	 	 	 < t i m e L i v e d I n C a n a d a   y e a r s = " 3 "   m o n t h s = " 8 " / > 
 
 	 	 	 < i m m i g E x p L i s t   o t h e r I m m i g E x p = " o t h e r   i m m i g r a t i o n   e x p e r i e n c e   t e x t " > 
 
 	 	 	 	 < v a l u e > 2 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 3 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 7 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 8 < / v a l u e > 
 
 	 	 	 < / i m m i g E x p L i s t > 
 
 	 	 	 < d i s c r i m E x p L i s t   o t h e r D i s c r i m E x p = " o t h e r   d i s c r i m i n a t i o n   e x p e r i e n c e   t e x t " > 
 
 	 	 	 	 < v a l u e > 2 1 1 3 4 0 0 2 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 3 6 5 8 7 3 0 0 7 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 4 1 0 5 1 5 0 0 3 < / v a l u e > 
 
 	 	 	 < / d i s c r i m E x p L i s t > 
 
 	 	 	 < p r e f L a n g > f r a < / p r e f L a n g > 
 
 	 	 	 < s e r v i c e L a n g > e n g < / s e r v i c e L a n g > 
 
 	 	 	 < l e g a l I s s u e s > C i v i l < / l e g a l I s s u e s > 
 
 	 	 	 < l e g a l S t a t u s L i s t > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 1 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 2 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 3 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 8 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 9 < / l e g a l S t a t u s > 
 
 	 	 	 < / l e g a l S t a t u s L i s t > 
 
 	 	 	 < e x i t D i s p o s i t i o n > 0 1 9 - 0 6 < / e x i t D i s p o s i t i o n > 
 
 	 	 < / c l i e n t R e c o r d > 
 
 	 	 < O C A N D o m a i n s > 
 
 	 	 	 < d o m a i n   n a m e = " 0 1 - a c c o m m o d a t i o n " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 	 < r e s i d e n c e T y p e > 0 2 4 - 1 9 < / r e s i d e n c e T y p e > 
 
 	 	 	 	 < r e s i d e n c e S u p p o r t > 2 4 A - 0 5 < / r e s i d e n c e S u p p o r t > 
 
 	 	 	 	 < l i v i n g A r r a n g e m e n t T y p e > 0 2 3 - 0 8 < / l i v i n g A r r a n g e m e n t T y p e > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 2 - f o o d " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 3 - l o o k i n g   a f t e r   t h e   h o m e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 4 - s e l f - c a r e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 5 - d a y t i m e   a c t i v i t i e s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 3 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 	 < e m p l o y S t a t u s > 2 2 4 3 6 6 0 0 4 < / e m p l o y S t a t u s > 
 
 	 	 	 	 < e d u c a t i o n P r o g r a m S t a t u s > 2 2 4 8 7 0 0 0 1 < / e d u c a t i o n P r o g r a m S t a t u s > 
 
 	 	 	 	 < r i s k U n e m p l o y m e n t L i s t > 
 
 	 	 	 	 	 < r i s k U n e m p l o y m e n t > U D E R - 0 2 < / r i s k U n e m p l o y m e n t > 
 
 	 	 	 	 	 < r i s k U n e m p l o y m e n t > U D E R - 0 3 < / r i s k U n e m p l o y m e n t > 
 
 	 	 	 	 	 < r i s k U n e m p l o y m e n t > U D E R - 0 6 < / r i s k U n e m p l o y m e n t > 
 
 	 	 	 	 < / r i s k U n e m p l o y m e n t L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 6 - p h y s i c a l   h e a l t h " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < m e d i c a l C o n d i t i o n L i s t   a u t i s m D e t a i l = " a u t i s m   s p e c i f i c   t e x t "   o t h e r D e t a i l = " o t h e r   m e d i c a l   t e x t " > 
 
 	 	 	 	 	 < m e d i c a l C o n d i t i o n > 4 0 8 8 5 6 0 0 3 < / m e d i c a l C o n d i t i o n > 
 
 	 	 	 	 	 < m e d i c a l C o n d i t i o n > 4 1 0 5 1 5 0 0 3 < / m e d i c a l C o n d i t i o n > 
 
 	 	 	 	 	 < m e d i c a l C o n d i t i o n > 4 1 4 9 1 6 0 0 1 < / m e d i c a l C o n d i t i o n > 
 
 	 	 	 	 < / m e d i c a l C o n d i t i o n L i s t > 
 
 	 	 	 	 < p h y s i c a l H e a l t h C o n c e r n > T R U E < / p h y s i c a l H e a l t h C o n c e r n > 
 
 	 	 	 	 < c o n c e r n A r e a L i s t   o t h e r C o n c e r n A r e a = " o t h e r   c o n c e r n   t e x t " > 
 
 	 	 	 	 	 < c o n c e r n A r e a > 1 1 9 4 1 5 0 0 7 < / c o n c e r n A r e a > 
 
 	 	 	 	 	 < c o n c e r n A r e a > 1 1 8 9 5 2 0 0 5 < / c o n c e r n A r e a > 
 
 	 	 	 	 	 < c o n c e r n A r e a > 4 1 0 5 1 5 0 0 3 < / c o n c e r n A r e a > 
 
 	 	 	 	 < / c o n c e r n A r e a L i s t > 
 
 	 	 	 	 < m e d i c a t i o n L i s t > 
 
 	 	 	 	 	 < m e d i c a t i o n D e t a i l   t a k e n A s P r e s c r i b e d = " T R U E "   i s H e l p P r o v i d e d = " F A L S E "   i s H e l p N e e d e d = " T R U E " / > 
 
 	 	 	 	 	 < m e d i c a t i o n D e t a i l   t a k e n A s P r e s c r i b e d = " F A L S E "   i s H e l p P r o v i d e d = " U N K "   i s H e l p N e e d e d = " T R U E " / > 
 
 	 	 	 	 	 < m e d i c a t i o n D e t a i l   t a k e n A s P r e s c r i b e d = " U N K "   i s H e l p P r o v i d e d = " T R U E "   i s H e l p N e e d e d = " F A L S E " / > 
 
 	 	 	 	 < / m e d i c a t i o n L i s t > 
 
 	 	 	 	 < s i d e E f f e c t s > T R U E < / s i d e E f f e c t s > 
 
 	 	 	 	 < d a i l y L i v i n g A f f e c t e d > T R U E < / d a i l y L i v i n g A f f e c t e d > 
 
 	 	 	 	 < s i d e E f f e c t s D e t a i l L i s t   o t h e r S i d e E f f e c t s D e t a i l = " o t h e r   s i d e   e f f e c t   t e x t " > 
 
 	 	 	 	 	 < s i d e E f f e c t s D e t a i l > 2 4 6 6 3 6 0 0 8 < / s i d e E f f e c t s D e t a i l > 
 
 	 	 	 	 	 < s i d e E f f e c t s D e t a i l > 5 3 6 1 9 0 0 0 < / s i d e E f f e c t s D e t a i l > 
 
 	 	 	 	 	 < s i d e E f f e c t s D e t a i l > 4 1 0 5 1 5 0 0 3 < / s i d e E f f e c t s D e t a i l > 
 
 	 	 	 	 < / s i d e E f f e c t s D e t a i l L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 7 - p s y c h o t i c   s y m p t o m s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 	 < h o s p i t a l i z e d P a s t T w o Y e a r s > T R U E < / h o s p i t a l i z e d P a s t T w o Y e a r s > 
 
 	 	 	 	 < t o t a l A d m i s s i o n s > 5 < / t o t a l A d m i s s i o n s > 
 
 	 	 	 	 < t o t a l H o s p i t a l D a y s > 7 8 < / t o t a l H o s p i t a l D a y s > 
 
 	 	 	 	 < c o m m u n i t y T r e a t O r d e r > 0 1 5 - 0 3 < / c o m m u n i t y T r e a t O r d e r > 
 
 	 	 	 	 < s y m p t o m L i s t   o t h e r S y m p t o m = " o t h e r   s y m p t o m   t e x t " > 
 
 	 	 	 	 	 < s y m p t o m > 4 1 0 5 1 6 0 0 2 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 7 5 4 0 8 0 0 8 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 2 0 7 3 0 0 0 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 1 4 0 2 0 0 1 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 5 5 9 2 9 0 0 7 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 4 1 0 5 1 5 0 0 3 < / s y m p t o m > 
 
 	 	 	 	 < / s y m p t o m L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 8 - c o n d i t i o n   a n d   t r e a t m e n t " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 	 < d i a g n o s t i c L i s t > 
 
 	 	 	 	 	 < d i a g n o s t i c > 2 7 7 6 0 0 0 < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > M D G M C < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > 8 7 8 5 8 0 0 2 < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > S R D D H < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > D H < / d i a g n o s t i c > 
 
 	 	 	 	 < / d i a g n o s t i c L i s t > 
 
 	 	 	 	 < o t h e r I l l n e s s L i s t > 
 
 	 	 	 	 	 < o t h e r I l l n e s s > 0 1 6 A - 0 1 < / o t h e r I l l n e s s > 
 
 	 	 	 	 	 < o t h e r I l l n e s s > 0 1 6 A - 0 3 < / o t h e r I l l n e s s > 
 
 	 	 	 	 < / o t h e r I l l n e s s L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 9 - p s y c h o l o g i c a l   d i s t r e s s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 0 - s a f e t y   t o   s e l f " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < s u i c i d e A t t e m p t > T R U E < / s u i c i d e A t t e m p t > 
 
 	 	 	 	 < s u i c i d e T h o u g h t s > T R U E < / s u i c i d e T h o u g h t s > 
 
 	 	 	 	 < s a f e t y C o n c e r n S e l f > T R U E < / s a f e t y C o n c e r n S e l f > 
 
 	 	 	 	 < s a f e t y T o S e l f R i s k L i s t   o t h e r S a f e t y T o S e l f R i s k = " o t h e r   r i s k   t o   s e l f   t e x t " > 
 
 	 	 	 	 	 < s a f e t y T o S e l f R i s k > 2 2 5 9 1 5 0 0 6 < / s a f e t y T o S e l f R i s k > 
 
 	 	 	 	 	 < s a f e t y T o S e l f R i s k > 4 0 1 2 0 6 0 0 8 < / s a f e t y T o S e l f R i s k > 
 
 	 	 	 	 	 < s a f e t y T o S e l f R i s k > 4 1 0 5 1 5 0 0 3 < / s a f e t y T o S e l f R i s k > 
 
 	 	 	 	 < / s a f e t y T o S e l f R i s k L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 1 - s a f e t y   t o   o t h e r s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 2 - a l c o h o l " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 	 < d r i n k A l c o h o l   q u a n t i t y = " 6 "   f r e q u e n c y = " 1 " / > 
 
 	 	 	 	 < s t a g e O f C h a n g e A l c o h o l > 5 < / s t a g e O f C h a n g e A l c o h o l > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 3 - d r u g s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 2 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 	 < d r u g U s e L i s t   i n j e c t e d = " 6 " > 
 
 	 	 	 	 	 < d r u g U s e   n a m e = " 3 9 8 7 0 5 0 0 4 "   f r e q u e n c y = " 5 " / > 
 
 	 	 	 	 	 < d r u g U s e   n a m e = " 2 2 6 0 4 4 0 0 4 "   f r e q u e n c y = " 6 " / > 
 
 	 	 	 	 	 < d r u g U s e   n a m e = " 4 1 0 5 1 5 0 0 3 "   f r e q u e n c y = " 5 " / > 
 
 	 	 	 	 < / d r u g U s e L i s t > 
 
 	 	 	 	 < s t a g e O f C h a n g e D r u g s > 2 < / s t a g e O f C h a n g e D r u g s > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 4 - o t h e r   a d d i c t i o n s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 	 < a d d i c t i o n T y p e L i s t   o t h e r A d d i c t i o n T y p e = " o t h e r   a d d i c t i o n   t e x t " > 
 
 	 	 	 	 	 < a d d i c t i o n T y p e > 1 0 5 5 2 3 0 0 9 < / a d d i c t i o n T y p e > 
 
 	 	 	 	 	 < a d d i c t i o n T y p e > 5 6 2 9 4 0 0 8 < / a d d i c t i o n T y p e > 
 
 	 	 	 	 	 < a d d i c t i o n T y p e > 4 1 0 5 1 5 0 0 3 < / a d d i c t i o n T y p e > 
 
 	 	 	 	 < / a d d i c t i o n T y p e L i s t > 
 
 	 	 	 	 < s t a g e O f C h a n g e A d d i c t i o n s > 3 < / s t a g e O f C h a n g e A d d i c t i o n s > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 5 - c o m p a n y " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 2 " / > 
 
 	 	 	 	 < c h a n g e d S o c i a l P a t t e r n s > C D A < / c h a n g e d S o c i a l P a t t e r n s > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 6 - i n t i m a t e   r e l a t i o n s h i p s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 7 - s e x u a l   e x p r e s s i o n " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 8 - c h i l d c a r e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 9 - o t h e r   d e p e n d e n t s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 0 - b a s i c   e d u c a t i o n " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < h i g h e s t E d u c a t i o n L e v e l > H L E S - 6 < / h i g h e s t E d u c a t i o n L e v e l > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 1 - t e l e p h o n e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 2 - t r a n s p o r t " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 3 - m o n e y " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 	 < s o u r c e O f I n c o m e > 0 3 1 - 1 0 < / s o u r c e O f I n c o m e > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 4 - b e n e f i t s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 < / O C A N D o m a i n s > 
 
 	 	 < a d d i t i o n a l E l e m e n t s > 
 
 	 	 	 < ! - -   B e g i n   F r e e   T e x t   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < c l i e n t H o p e s F o r F u t u r e / > 
 
 	 	 	 < c l i e n t N e e d T o G e t T h e r e / > 
 
 	 	 	 < c l i e n t V i e w M e n t a l H e a l t h / > 
 
 	 	 	 < c l i e n t S p i r i t u a l i t y I m p o r t a n c e / > 
 
 	 	 	 < c l i e n t C u l t u r e H e r i t a g e I m p o r t a n c e / > 
 
 	 	 	 < ! - -   E n d   F r e e   T e x t   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < p r e s e n t i n g I s s u e L i s t > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 5 " / > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 9 " / > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 1 1 " / > 
 
 	 	 	 < / p r e s e n t i n g I s s u e L i s t > 
 
 	 	 	 < a c t i o n L i s t > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 1 "   d o m a i n = " 0 1 - a c c o m m o d a t i o n " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 2 "   d o m a i n = " 0 2 - f o o d " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 3 "   d o m a i n = " 2 1 - t e l e p h o n e " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 4 "   d o m a i n = " 2 2 - t r a n s p o r t " / > 
 
 	 	 	 < / a c t i o n L i s t > 
 
 	 	 	 < r e f e r r a l L i s t > 
 
 	 	 	 	 < r e f e r r a l   o p t i m a l = " A D D "   s p e c i f y O p t i m a l = " s p e c i f y   t e x t   f o r   o p t i m a l   r e f e r r a l "   a c t u a l = " C H I "   s p e c i f y A c t u a l = " s p e c i f y   t e x t   f o r   a c t u a l   r e f e r r a l "   d i f f e r e n c e R e a s o n = " R D - 1 "   s t a t u s = " R S - 2 " / > 
 
 	 	 	 	 < r e f e r r a l   o p t i m a l = " C H I "   s p e c i f y O p t i m a l = " s p e c i f y   t e x t   f o r   o p t i m a l   r e f e r r a l "   a c t u a l = " A D D "   s p e c i f y A c t u a l = " s p e c i f y   t e x t   f o r   a c t u a l   r e f e r r a l "   d i f f e r e n c e R e a s o n = " R D - 3 "   s t a t u s = " R S - 1 " / > 
 
 	 	 	 < / r e f e r r a l L i s t > 
 
 	 	 < / a d d i t i o n a l E l e m e n t s > 
 
 	 < / O C A N S u b m i s s i o n R e c o r d > 
 
 
 
 	 < ! - -   a s s e s s m e n t   r e c o r d #   5 :   t h i s   a s s e s s m e n t   s h o w s   a   r e a s s e s s m e n t   - - > 
 
 	 < O C A N S u b m i s s i o n R e c o r d   a s s e s s m e n t I D = " 1 0 0 5 "   s t a r t D a t e = " 2 0 0 9 - 0 1 - 1 1 Z "   c o m p l e t i o n D a t e = " 2 0 0 9 - 0 1 - 1 6 Z "   a s s e s s m e n t S t a t u s = " C o m p l e t e " > 
 
 	 	 < o r g a n i z a t i o n R e c o r d > 
 
 	 	 	 < s e r v i c e O r g   n a m e = " N E   L H I N   T e s t   O r g "   n u m b e r = " 1 2 3 " / > 
 
 	 	 	 < p r o g r a m   n a m e = " I n t e n s i v e   C a s e   M a n a g e m e n t "   n u m b e r = " 3 0 2 5 " / > 
 
 	 	 	 < M I S F u n c t i o n   v a l u e = " 7 2 5   5 1   7 6   2 0 " / > 
 
 	 	 < / o r g a n i z a t i o n R e c o r d > 
 
 	 	 < c l i e n t R e c o r d > 
 
 	 	 	 < c l i e n t I D   o r g C l i e n t I D = " 1 2 3 5 0 0 " / > 
 
 	 	 	 < ! - -   B e g i n   P H I   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < c l i e n t N a m e   l a s t = " "   f i r s t = " " / > 
 
 	 	 	 < c l i e n t A d d r e s s   l i n e 1 = " "   l i n e 2 = " "   c i t y = " "   p r o v i n c e = " "   p o s t a l C o d e = " " / > 
 
 	 	 	 < c l i e n t P h o n e / > 
 
 	 	 	 < c l i e n t O H I P   n u m b e r = " "   v e r s i o n = " " / > 
 
 	 	 	 < c l i e n t C u l t u r e / > 
 
 	 	 	 < ! - -   E n d   P H I   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < r e a s o n F o r A s s e s s m e n t   v a l u e = " R A "   o t h e r = " " / > 
 
 	 	 	 < c l i e n t C o n t a c t > 
 
 	 	 	 	 < d o c t o r C o n t a c t   d o c t o r = " T R U E "   l a s t S e e n = " L S - 6 " / > 
 
 	 	 	 	 < p s y c h i a t r i s t C o n t a c t   p s y c h i a t r i s t = " T R U E "   l a s t S e e n = " L S - 1 2 " / > 
 
 	 	 	 	 < o t h e r P r a c t i t i o n e r C o n t a c t   p r a c t i t i o n e r T y p e = " 3 1 1 1 "   l a s t S e e n = " L S - 1 " / > 
 
 	 	 	 	 < o t h e r P r a c t i t i o n e r C o n t a c t   p r a c t i t i o n e r T y p e = " 3 1 1 2 "   l a s t S e e n = " L S - 1 3 " / > 
 
 	 	 	 	 < o t h e r P r a c t i t i o n e r C o n t a c t   p r a c t i t i o n e r T y p e = " 4 2 1 7 "   l a s t S e e n = " L S - 6 " / > 
 
 	 	 	 	 < o t h e r A g e n c y C o n t a c t   l a s t S e e n = " L S - 1 2 " / > 
 
 	 	 	 < / c l i e n t C o n t a c t > 
 
 	 	 	 < s e r v i c e R e c i p i e n t L o c a t i o n > 0 1 0 - 5 2 < / s e r v i c e R e c i p i e n t L o c a t i o n > 
 
 	 	 	 < s e r v i c e R e c i p i e n t L H I N > 1 2 < / s e r v i c e R e c i p i e n t L H I N > 
 
 	 	 	 < s e r v i c e D e l i v e r y L H I N > 5 < / s e r v i c e D e l i v e r y L H I N > 
 
 	 	 	 < c l i e n t D O B > 2 0 0 1 - 0 3 - 1 5 Z < / c l i e n t D O B > 
 
 	 	 	 < g e n d e r > M < / g e n d e r > 
 
 	 	 	 < m a r i t a l S t a t u s > 1 2 5 6 8 1 0 0 6 < / m a r i t a l S t a t u s > 
 
 	 	 	 < c l i e n t C a p a c i t y   p r o p e r t y = " T R U E "   p e r s o n a l C a r e = " F A L S E "   l e g a l G u a r d i a n = " C D A " / > 
 
 	 	 	 < r e f e r r a l S o u r c e > 0 1 8 - 0 5 < / r e f e r r a l S o u r c e > 
 
 	 	 	 < a b o r i g i n a l O r i g i n > 0 1 1 - 0 2 < / a b o r i g i n a l O r i g i n > 
 
 	 	 	 < c i t i z e n s h i p S t a t u s > T R < / c i t i z e n s h i p S t a t u s > 
 
 	 	 	 < t i m e L i v e d I n C a n a d a   y e a r s = " 3 "   m o n t h s = " 8 " / > 
 
 	 	 	 < i m m i g E x p L i s t   o t h e r I m m i g E x p = " o t h e r   i m m i g r a t i o n   e x p e r i e n c e   t e x t " > 
 
 	 	 	 	 < v a l u e > 2 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 3 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 7 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 8 < / v a l u e > 
 
 	 	 	 < / i m m i g E x p L i s t > 
 
 	 	 	 < d i s c r i m E x p L i s t   o t h e r D i s c r i m E x p = " o t h e r   d i s c r i m i n a t i o n   e x p e r i e n c e   t e x t " > 
 
 	 	 	 	 < v a l u e > 2 1 1 3 4 0 0 2 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 3 6 5 8 7 3 0 0 7 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 4 1 0 5 1 5 0 0 3 < / v a l u e > 
 
 	 	 	 < / d i s c r i m E x p L i s t > 
 
 	 	 	 < p r e f L a n g > f r a < / p r e f L a n g > 
 
 	 	 	 < s e r v i c e L a n g > e n g < / s e r v i c e L a n g > 
 
 	 	 	 < l e g a l I s s u e s > C i v i l < / l e g a l I s s u e s > 
 
 	 	 	 < l e g a l S t a t u s L i s t > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 1 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 2 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 3 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 8 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 9 < / l e g a l S t a t u s > 
 
 	 	 	 < / l e g a l S t a t u s L i s t > 
 
 	 	 	 < e x i t D i s p o s i t i o n > 0 1 9 - 0 6 < / e x i t D i s p o s i t i o n > 
 
 	 	 < / c l i e n t R e c o r d > 
 
 	 	 < O C A N D o m a i n s > 
 
 	 	 	 < d o m a i n   n a m e = " 0 1 - a c c o m m o d a t i o n " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 	 < r e s i d e n c e T y p e > 0 2 4 - 1 9 < / r e s i d e n c e T y p e > 
 
 	 	 	 	 < r e s i d e n c e S u p p o r t > 2 4 A - 0 5 < / r e s i d e n c e S u p p o r t > 
 
 	 	 	 	 < l i v i n g A r r a n g e m e n t T y p e > 0 2 3 - 0 8 < / l i v i n g A r r a n g e m e n t T y p e > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 2 - f o o d " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 3 - l o o k i n g   a f t e r   t h e   h o m e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 4 - s e l f - c a r e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 5 - d a y t i m e   a c t i v i t i e s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 3 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 	 < e m p l o y S t a t u s > 2 2 4 3 6 6 0 0 4 < / e m p l o y S t a t u s > 
 
 	 	 	 	 < e d u c a t i o n P r o g r a m S t a t u s > 2 2 4 8 7 0 0 0 1 < / e d u c a t i o n P r o g r a m S t a t u s > 
 
 	 	 	 	 < r i s k U n e m p l o y m e n t L i s t > 
 
 	 	 	 	 	 < r i s k U n e m p l o y m e n t > U D E R - 0 2 < / r i s k U n e m p l o y m e n t > 
 
 	 	 	 	 	 < r i s k U n e m p l o y m e n t > U D E R - 0 3 < / r i s k U n e m p l o y m e n t > 
 
 	 	 	 	 	 < r i s k U n e m p l o y m e n t > U D E R - 0 6 < / r i s k U n e m p l o y m e n t > 
 
 	 	 	 	 < / r i s k U n e m p l o y m e n t L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 6 - p h y s i c a l   h e a l t h " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < m e d i c a l C o n d i t i o n L i s t   a u t i s m D e t a i l = " a u t i s m   s p e c i f i c   t e x t "   o t h e r D e t a i l = " o t h e r   m e d i c a l   t e x t " > 
 
 	 	 	 	 	 < m e d i c a l C o n d i t i o n > 4 0 8 8 5 6 0 0 3 < / m e d i c a l C o n d i t i o n > 
 
 	 	 	 	 	 < m e d i c a l C o n d i t i o n > 4 1 0 5 1 5 0 0 3 < / m e d i c a l C o n d i t i o n > 
 
 	 	 	 	 	 < m e d i c a l C o n d i t i o n > 4 1 4 9 1 6 0 0 1 < / m e d i c a l C o n d i t i o n > 
 
 	 	 	 	 < / m e d i c a l C o n d i t i o n L i s t > 
 
 	 	 	 	 < p h y s i c a l H e a l t h C o n c e r n > T R U E < / p h y s i c a l H e a l t h C o n c e r n > 
 
 	 	 	 	 < c o n c e r n A r e a L i s t   o t h e r C o n c e r n A r e a = " o t h e r   c o n c e r n   t e x t " > 
 
 	 	 	 	 	 < c o n c e r n A r e a > 1 1 9 4 1 5 0 0 7 < / c o n c e r n A r e a > 
 
 	 	 	 	 	 < c o n c e r n A r e a > 1 1 8 9 5 2 0 0 5 < / c o n c e r n A r e a > 
 
 	 	 	 	 	 < c o n c e r n A r e a > 4 1 0 5 1 5 0 0 3 < / c o n c e r n A r e a > 
 
 	 	 	 	 < / c o n c e r n A r e a L i s t > 
 
 	 	 	 	 < m e d i c a t i o n L i s t > 
 
 	 	 	 	 	 < m e d i c a t i o n D e t a i l   t a k e n A s P r e s c r i b e d = " T R U E "   i s H e l p P r o v i d e d = " F A L S E "   i s H e l p N e e d e d = " T R U E " / > 
 
 	 	 	 	 	 < m e d i c a t i o n D e t a i l   t a k e n A s P r e s c r i b e d = " F A L S E "   i s H e l p P r o v i d e d = " U N K "   i s H e l p N e e d e d = " T R U E " / > 
 
 	 	 	 	 	 < m e d i c a t i o n D e t a i l   t a k e n A s P r e s c r i b e d = " U N K "   i s H e l p P r o v i d e d = " T R U E "   i s H e l p N e e d e d = " F A L S E " / > 
 
 	 	 	 	 < / m e d i c a t i o n L i s t > 
 
 	 	 	 	 < s i d e E f f e c t s > T R U E < / s i d e E f f e c t s > 
 
 	 	 	 	 < d a i l y L i v i n g A f f e c t e d > T R U E < / d a i l y L i v i n g A f f e c t e d > 
 
 	 	 	 	 < s i d e E f f e c t s D e t a i l L i s t   o t h e r S i d e E f f e c t s D e t a i l = " o t h e r   s i d e   e f f e c t   t e x t " > 
 
 	 	 	 	 	 < s i d e E f f e c t s D e t a i l > 2 4 6 6 3 6 0 0 8 < / s i d e E f f e c t s D e t a i l > 
 
 	 	 	 	 	 < s i d e E f f e c t s D e t a i l > 5 3 6 1 9 0 0 0 < / s i d e E f f e c t s D e t a i l > 
 
 	 	 	 	 	 < s i d e E f f e c t s D e t a i l > 4 1 0 5 1 5 0 0 3 < / s i d e E f f e c t s D e t a i l > 
 
 	 	 	 	 < / s i d e E f f e c t s D e t a i l L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 7 - p s y c h o t i c   s y m p t o m s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 	 < h o s p i t a l i z e d P a s t T w o Y e a r s > T R U E < / h o s p i t a l i z e d P a s t T w o Y e a r s > 
 
 	 	 	 	 < t o t a l A d m i s s i o n s > 5 < / t o t a l A d m i s s i o n s > 
 
 	 	 	 	 < t o t a l H o s p i t a l D a y s > 7 8 < / t o t a l H o s p i t a l D a y s > 
 
 	 	 	 	 < c o m m u n i t y T r e a t O r d e r > 0 1 5 - 0 3 < / c o m m u n i t y T r e a t O r d e r > 
 
 	 	 	 	 < s y m p t o m L i s t   o t h e r S y m p t o m = " o t h e r   s y m p t o m   t e x t " > 
 
 	 	 	 	 	 < s y m p t o m > 4 1 0 5 1 6 0 0 2 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 7 5 4 0 8 0 0 8 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 2 0 7 3 0 0 0 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 1 4 0 2 0 0 1 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 5 5 9 2 9 0 0 7 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 4 1 0 5 1 5 0 0 3 < / s y m p t o m > 
 
 	 	 	 	 < / s y m p t o m L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 8 - c o n d i t i o n   a n d   t r e a t m e n t " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 	 < d i a g n o s t i c L i s t > 
 
 	 	 	 	 	 < d i a g n o s t i c > 2 7 7 6 0 0 0 < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > M D G M C < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > 8 7 8 5 8 0 0 2 < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > S R D D H < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > D H < / d i a g n o s t i c > 
 
 	 	 	 	 < / d i a g n o s t i c L i s t > 
 
 	 	 	 	 < o t h e r I l l n e s s L i s t > 
 
 	 	 	 	 	 < o t h e r I l l n e s s > 0 1 6 A - 0 1 < / o t h e r I l l n e s s > 
 
 	 	 	 	 	 < o t h e r I l l n e s s > 0 1 6 A - 0 3 < / o t h e r I l l n e s s > 
 
 	 	 	 	 < / o t h e r I l l n e s s L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 9 - p s y c h o l o g i c a l   d i s t r e s s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 0 - s a f e t y   t o   s e l f " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < s u i c i d e A t t e m p t > T R U E < / s u i c i d e A t t e m p t > 
 
 	 	 	 	 < s u i c i d e T h o u g h t s > T R U E < / s u i c i d e T h o u g h t s > 
 
 	 	 	 	 < s a f e t y C o n c e r n S e l f > T R U E < / s a f e t y C o n c e r n S e l f > 
 
 	 	 	 	 < s a f e t y T o S e l f R i s k L i s t   o t h e r S a f e t y T o S e l f R i s k = " o t h e r   r i s k   t o   s e l f   t e x t " > 
 
 	 	 	 	 	 < s a f e t y T o S e l f R i s k > 2 2 5 9 1 5 0 0 6 < / s a f e t y T o S e l f R i s k > 
 
 	 	 	 	 	 < s a f e t y T o S e l f R i s k > 4 0 1 2 0 6 0 0 8 < / s a f e t y T o S e l f R i s k > 
 
 	 	 	 	 	 < s a f e t y T o S e l f R i s k > 4 1 0 5 1 5 0 0 3 < / s a f e t y T o S e l f R i s k > 
 
 	 	 	 	 < / s a f e t y T o S e l f R i s k L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 1 - s a f e t y   t o   o t h e r s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 2 - a l c o h o l " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 	 < d r i n k A l c o h o l   q u a n t i t y = " 6 "   f r e q u e n c y = " 1 " / > 
 
 	 	 	 	 < s t a g e O f C h a n g e A l c o h o l > 5 < / s t a g e O f C h a n g e A l c o h o l > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 3 - d r u g s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 2 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 	 < d r u g U s e L i s t   i n j e c t e d = " 6 " > 
 
 	 	 	 	 	 < d r u g U s e   n a m e = " 3 9 8 7 0 5 0 0 4 "   f r e q u e n c y = " 5 " / > 
 
 	 	 	 	 	 < d r u g U s e   n a m e = " 2 2 6 0 4 4 0 0 4 "   f r e q u e n c y = " 6 " / > 
 
 	 	 	 	 	 < d r u g U s e   n a m e = " 4 1 0 5 1 5 0 0 3 "   f r e q u e n c y = " 5 " / > 
 
 	 	 	 	 < / d r u g U s e L i s t > 
 
 	 	 	 	 < s t a g e O f C h a n g e D r u g s > 2 < / s t a g e O f C h a n g e D r u g s > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 4 - o t h e r   a d d i c t i o n s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 	 < a d d i c t i o n T y p e L i s t   o t h e r A d d i c t i o n T y p e = " o t h e r   a d d i c t i o n   t e x t " > 
 
 	 	 	 	 	 < a d d i c t i o n T y p e > 1 0 5 5 2 3 0 0 9 < / a d d i c t i o n T y p e > 
 
 	 	 	 	 	 < a d d i c t i o n T y p e > 5 6 2 9 4 0 0 8 < / a d d i c t i o n T y p e > 
 
 	 	 	 	 	 < a d d i c t i o n T y p e > 4 1 0 5 1 5 0 0 3 < / a d d i c t i o n T y p e > 
 
 	 	 	 	 < / a d d i c t i o n T y p e L i s t > 
 
 	 	 	 	 < s t a g e O f C h a n g e A d d i c t i o n s > 3 < / s t a g e O f C h a n g e A d d i c t i o n s > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 5 - c o m p a n y " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 2 " / > 
 
 	 	 	 	 < c h a n g e d S o c i a l P a t t e r n s > C D A < / c h a n g e d S o c i a l P a t t e r n s > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 6 - i n t i m a t e   r e l a t i o n s h i p s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 7 - s e x u a l   e x p r e s s i o n " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 8 - c h i l d c a r e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 9 - o t h e r   d e p e n d e n t s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 0 - b a s i c   e d u c a t i o n " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < h i g h e s t E d u c a t i o n L e v e l > H L E S - 6 < / h i g h e s t E d u c a t i o n L e v e l > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 1 - t e l e p h o n e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 2 - t r a n s p o r t " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 3 - m o n e y " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 	 < s o u r c e O f I n c o m e > 0 3 1 - 1 0 < / s o u r c e O f I n c o m e > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 4 - b e n e f i t s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 < / O C A N D o m a i n s > 
 
 	 	 < a d d i t i o n a l E l e m e n t s > 
 
 	 	 	 < ! - -   B e g i n   F r e e   T e x t   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < c l i e n t H o p e s F o r F u t u r e / > 
 
 	 	 	 < c l i e n t N e e d T o G e t T h e r e / > 
 
 	 	 	 < c l i e n t V i e w M e n t a l H e a l t h / > 
 
 	 	 	 < c l i e n t S p i r i t u a l i t y I m p o r t a n c e / > 
 
 	 	 	 < c l i e n t C u l t u r e H e r i t a g e I m p o r t a n c e / > 
 
 	 	 	 < ! - -   E n d   F r e e   T e x t   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < p r e s e n t i n g I s s u e L i s t > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 5 " / > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 9 " / > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 1 1 " / > 
 
 	 	 	 < / p r e s e n t i n g I s s u e L i s t > 
 
 	 	 	 < a c t i o n L i s t > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 1 "   d o m a i n = " 0 1 - a c c o m m o d a t i o n " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 2 "   d o m a i n = " 0 2 - f o o d " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 3 "   d o m a i n = " 2 1 - t e l e p h o n e " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 4 "   d o m a i n = " 2 2 - t r a n s p o r t " / > 
 
 	 	 	 < / a c t i o n L i s t > 
 
 	 	 	 < r e f e r r a l L i s t > 
 
 	 	 	 	 < r e f e r r a l   o p t i m a l = " A D D "   s p e c i f y O p t i m a l = " s p e c i f y   t e x t   f o r   o p t i m a l   r e f e r r a l "   a c t u a l = " C H I "   s p e c i f y A c t u a l = " s p e c i f y   t e x t   f o r   a c t u a l   r e f e r r a l "   d i f f e r e n c e R e a s o n = " R D - 1 "   s t a t u s = " R S - 2 " / > 
 
 	 	 	 	 < r e f e r r a l   o p t i m a l = " C H I "   s p e c i f y O p t i m a l = " s p e c i f y   t e x t   f o r   o p t i m a l   r e f e r r a l "   a c t u a l = " A D D "   s p e c i f y A c t u a l = " s p e c i f y   t e x t   f o r   a c t u a l   r e f e r r a l "   d i f f e r e n c e R e a s o n = " R D - 3 "   s t a t u s = " R S - 1 " / > 
 
 	 	 	 < / r e f e r r a l L i s t > 
 
 	 	 < / a d d i t i o n a l E l e m e n t s > 
 
 	 < / O C A N S u b m i s s i o n R e c o r d > 
 
 
 
 	 < ! - -   a s s e s s m e n t   r e c o r d #   6 :   t h i s   a s s e s s m e n t   s h o w s   a   r e a s s e s s m e n t   - - > 
 
 	 < O C A N S u b m i s s i o n R e c o r d   a s s e s s m e n t I D = " 1 0 0 6 "   s t a r t D a t e = " 2 0 0 9 - 0 1 - 1 1 Z "   c o m p l e t i o n D a t e = " 2 0 0 9 - 0 1 - 1 6 Z "   a s s e s s m e n t S t a t u s = " C o m p l e t e " > 
 
 	 	 < o r g a n i z a t i o n R e c o r d > 
 
 	 	 	 < s e r v i c e O r g   n a m e = " N E   L H I N   T e s t   O r g "   n u m b e r = " 1 2 3 " / > 
 
 	 	 	 < p r o g r a m   n a m e = " I n t e n s i v e   C a s e   M a n a g e m e n t "   n u m b e r = " 3 0 2 5 " / > 
 
 	 	 	 < M I S F u n c t i o n   v a l u e = " 7 2 5   5 1   7 6   2 0 " / > 
 
 	 	 < / o r g a n i z a t i o n R e c o r d > 
 
 	 	 < c l i e n t R e c o r d > 
 
 	 	 	 < c l i e n t I D   o r g C l i e n t I D = " 1 2 3 6 0 0 " / > 
 
 	 	 	 < ! - -   B e g i n   P H I   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < c l i e n t N a m e   l a s t = " "   f i r s t = " " / > 
 
 	 	 	 < c l i e n t A d d r e s s   l i n e 1 = " "   l i n e 2 = " "   c i t y = " "   p r o v i n c e = " "   p o s t a l C o d e = " " / > 
 
 	 	 	 < c l i e n t P h o n e / > 
 
 	 	 	 < c l i e n t O H I P   n u m b e r = " "   v e r s i o n = " " / > 
 
 	 	 	 < c l i e n t C u l t u r e / > 
 
 	 	 	 < ! - -   E n d   P H I   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < r e a s o n F o r A s s e s s m e n t   v a l u e = " R A "   o t h e r = " " / > 
 
 	 	 	 < c l i e n t C o n t a c t > 
 
 	 	 	 	 < d o c t o r C o n t a c t   d o c t o r = " T R U E "   l a s t S e e n = " L S - 6 " / > 
 
 	 	 	 	 < p s y c h i a t r i s t C o n t a c t   p s y c h i a t r i s t = " T R U E "   l a s t S e e n = " L S - 1 2 " / > 
 
 	 	 	 	 < o t h e r P r a c t i t i o n e r C o n t a c t   p r a c t i t i o n e r T y p e = " 3 1 1 1 "   l a s t S e e n = " L S - 1 " / > 
 
 	 	 	 	 < o t h e r P r a c t i t i o n e r C o n t a c t   p r a c t i t i o n e r T y p e = " 3 1 1 2 "   l a s t S e e n = " L S - 1 3 " / > 
 
 	 	 	 	 < o t h e r P r a c t i t i o n e r C o n t a c t   p r a c t i t i o n e r T y p e = " 4 2 1 7 "   l a s t S e e n = " L S - 6 " / > 
 
 	 	 	 	 < o t h e r A g e n c y C o n t a c t   l a s t S e e n = " L S - 1 2 " / > 
 
 	 	 	 < / c l i e n t C o n t a c t > 
 
 	 	 	 < s e r v i c e R e c i p i e n t L o c a t i o n > 0 1 0 - 5 2 < / s e r v i c e R e c i p i e n t L o c a t i o n > 
 
 	 	 	 < s e r v i c e R e c i p i e n t L H I N > 1 2 < / s e r v i c e R e c i p i e n t L H I N > 
 
 	 	 	 < s e r v i c e D e l i v e r y L H I N > 5 < / s e r v i c e D e l i v e r y L H I N > 
 
 	 	 	 < c l i e n t D O B > 1 9 3 0 - 0 9 - 1 1 Z < / c l i e n t D O B > 
 
 	 	 	 < g e n d e r > O T H < / g e n d e r > 
 
 	 	 	 < m a r i t a l S t a t u s > 1 2 5 6 8 1 0 0 6 < / m a r i t a l S t a t u s > 
 
 	 	 	 < c l i e n t C a p a c i t y   p r o p e r t y = " T R U E "   p e r s o n a l C a r e = " F A L S E "   l e g a l G u a r d i a n = " C D A " / > 
 
 	 	 	 < r e f e r r a l S o u r c e > 0 1 8 - 0 5 < / r e f e r r a l S o u r c e > 
 
 	 	 	 < a b o r i g i n a l O r i g i n > 0 1 1 - 0 2 < / a b o r i g i n a l O r i g i n > 
 
 	 	 	 < c i t i z e n s h i p S t a t u s > P R < / c i t i z e n s h i p S t a t u s > 
 
 	 	 	 < t i m e L i v e d I n C a n a d a   y e a r s = " 3 "   m o n t h s = " 8 " / > 
 
 	 	 	 < i m m i g E x p L i s t   o t h e r I m m i g E x p = " o t h e r   i m m i g r a t i o n   e x p e r i e n c e   t e x t " > 
 
 	 	 	 	 < v a l u e > 2 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 3 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 7 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 8 < / v a l u e > 
 
 	 	 	 < / i m m i g E x p L i s t > 
 
 	 	 	 < d i s c r i m E x p L i s t   o t h e r D i s c r i m E x p = " o t h e r   d i s c r i m i n a t i o n   e x p e r i e n c e   t e x t " > 
 
 	 	 	 	 < v a l u e > 2 1 1 3 4 0 0 2 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 3 6 5 8 7 3 0 0 7 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 4 1 0 5 1 5 0 0 3 < / v a l u e > 
 
 	 	 	 < / d i s c r i m E x p L i s t > 
 
 	 	 	 < p r e f L a n g > f r a < / p r e f L a n g > 
 
 	 	 	 < s e r v i c e L a n g > e n g < / s e r v i c e L a n g > 
 
 	 	 	 < l e g a l I s s u e s > C i v i l < / l e g a l I s s u e s > 
 
 	 	 	 < l e g a l S t a t u s L i s t > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 1 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 2 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 3 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 8 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 9 < / l e g a l S t a t u s > 
 
 	 	 	 < / l e g a l S t a t u s L i s t > 
 
 	 	 	 < e x i t D i s p o s i t i o n > 0 1 9 - 0 6 < / e x i t D i s p o s i t i o n > 
 
 	 	 < / c l i e n t R e c o r d > 
 
 	 	 < O C A N D o m a i n s > 
 
 	 	 	 < d o m a i n   n a m e = " 0 1 - a c c o m m o d a t i o n " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 	 < r e s i d e n c e T y p e > 0 2 4 - 1 9 < / r e s i d e n c e T y p e > 
 
 	 	 	 	 < r e s i d e n c e S u p p o r t > 2 4 A - 0 5 < / r e s i d e n c e S u p p o r t > 
 
 	 	 	 	 < l i v i n g A r r a n g e m e n t T y p e > 0 2 3 - 0 8 < / l i v i n g A r r a n g e m e n t T y p e > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 2 - f o o d " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 3 - l o o k i n g   a f t e r   t h e   h o m e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 4 - s e l f - c a r e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 5 - d a y t i m e   a c t i v i t i e s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 3 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 	 < e m p l o y S t a t u s > 2 2 4 3 6 6 0 0 4 < / e m p l o y S t a t u s > 
 
 	 	 	 	 < e d u c a t i o n P r o g r a m S t a t u s > 2 2 4 8 7 0 0 0 1 < / e d u c a t i o n P r o g r a m S t a t u s > 
 
 	 	 	 	 < r i s k U n e m p l o y m e n t L i s t > 
 
 	 	 	 	 	 < r i s k U n e m p l o y m e n t > U D E R - 0 2 < / r i s k U n e m p l o y m e n t > 
 
 	 	 	 	 	 < r i s k U n e m p l o y m e n t > U D E R - 0 3 < / r i s k U n e m p l o y m e n t > 
 
 	 	 	 	 	 < r i s k U n e m p l o y m e n t > U D E R - 0 6 < / r i s k U n e m p l o y m e n t > 
 
 	 	 	 	 < / r i s k U n e m p l o y m e n t L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 6 - p h y s i c a l   h e a l t h " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < m e d i c a l C o n d i t i o n L i s t   a u t i s m D e t a i l = " a u t i s m   s p e c i f i c   t e x t "   o t h e r D e t a i l = " o t h e r   m e d i c a l   t e x t " > 
 
 	 	 	 	 	 < m e d i c a l C o n d i t i o n > 4 0 8 8 5 6 0 0 3 < / m e d i c a l C o n d i t i o n > 
 
 	 	 	 	 	 < m e d i c a l C o n d i t i o n > 4 1 0 5 1 5 0 0 3 < / m e d i c a l C o n d i t i o n > 
 
 	 	 	 	 	 < m e d i c a l C o n d i t i o n > 4 1 4 9 1 6 0 0 1 < / m e d i c a l C o n d i t i o n > 
 
 	 	 	 	 < / m e d i c a l C o n d i t i o n L i s t > 
 
 	 	 	 	 < p h y s i c a l H e a l t h C o n c e r n > T R U E < / p h y s i c a l H e a l t h C o n c e r n > 
 
 	 	 	 	 < c o n c e r n A r e a L i s t   o t h e r C o n c e r n A r e a = " o t h e r   c o n c e r n   t e x t " > 
 
 	 	 	 	 	 < c o n c e r n A r e a > 1 1 9 4 1 5 0 0 7 < / c o n c e r n A r e a > 
 
 	 	 	 	 	 < c o n c e r n A r e a > 1 1 8 9 5 2 0 0 5 < / c o n c e r n A r e a > 
 
 	 	 	 	 	 < c o n c e r n A r e a > 4 1 0 5 1 5 0 0 3 < / c o n c e r n A r e a > 
 
 	 	 	 	 < / c o n c e r n A r e a L i s t > 
 
 	 	 	 	 < m e d i c a t i o n L i s t > 
 
 	 	 	 	 	 < m e d i c a t i o n D e t a i l   t a k e n A s P r e s c r i b e d = " T R U E "   i s H e l p P r o v i d e d = " F A L S E "   i s H e l p N e e d e d = " T R U E " / > 
 
 	 	 	 	 	 < m e d i c a t i o n D e t a i l   t a k e n A s P r e s c r i b e d = " F A L S E "   i s H e l p P r o v i d e d = " U N K "   i s H e l p N e e d e d = " T R U E " / > 
 
 	 	 	 	 	 < m e d i c a t i o n D e t a i l   t a k e n A s P r e s c r i b e d = " U N K "   i s H e l p P r o v i d e d = " T R U E "   i s H e l p N e e d e d = " F A L S E " / > 
 
 	 	 	 	 < / m e d i c a t i o n L i s t > 
 
 	 	 	 	 < s i d e E f f e c t s > T R U E < / s i d e E f f e c t s > 
 
 	 	 	 	 < d a i l y L i v i n g A f f e c t e d > T R U E < / d a i l y L i v i n g A f f e c t e d > 
 
 	 	 	 	 < s i d e E f f e c t s D e t a i l L i s t   o t h e r S i d e E f f e c t s D e t a i l = " o t h e r   s i d e   e f f e c t   t e x t " > 
 
 	 	 	 	 	 < s i d e E f f e c t s D e t a i l > 2 4 6 6 3 6 0 0 8 < / s i d e E f f e c t s D e t a i l > 
 
 	 	 	 	 	 < s i d e E f f e c t s D e t a i l > 5 3 6 1 9 0 0 0 < / s i d e E f f e c t s D e t a i l > 
 
 	 	 	 	 	 < s i d e E f f e c t s D e t a i l > 4 1 0 5 1 5 0 0 3 < / s i d e E f f e c t s D e t a i l > 
 
 	 	 	 	 < / s i d e E f f e c t s D e t a i l L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 7 - p s y c h o t i c   s y m p t o m s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 	 < h o s p i t a l i z e d P a s t T w o Y e a r s > T R U E < / h o s p i t a l i z e d P a s t T w o Y e a r s > 
 
 	 	 	 	 < t o t a l A d m i s s i o n s > 5 < / t o t a l A d m i s s i o n s > 
 
 	 	 	 	 < t o t a l H o s p i t a l D a y s > 7 8 < / t o t a l H o s p i t a l D a y s > 
 
 	 	 	 	 < c o m m u n i t y T r e a t O r d e r > 0 1 5 - 0 3 < / c o m m u n i t y T r e a t O r d e r > 
 
 	 	 	 	 < s y m p t o m L i s t   o t h e r S y m p t o m = " o t h e r   s y m p t o m   t e x t " > 
 
 	 	 	 	 	 < s y m p t o m > 4 1 0 5 1 6 0 0 2 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 7 5 4 0 8 0 0 8 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 2 0 7 3 0 0 0 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 1 4 0 2 0 0 1 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 5 5 9 2 9 0 0 7 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 4 1 0 5 1 5 0 0 3 < / s y m p t o m > 
 
 	 	 	 	 < / s y m p t o m L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 8 - c o n d i t i o n   a n d   t r e a t m e n t " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 	 < d i a g n o s t i c L i s t > 
 
 	 	 	 	 	 < d i a g n o s t i c > 2 7 7 6 0 0 0 < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > M D G M C < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > 8 7 8 5 8 0 0 2 < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > S R D D H < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > D H < / d i a g n o s t i c > 
 
 	 	 	 	 < / d i a g n o s t i c L i s t > 
 
 	 	 	 	 < o t h e r I l l n e s s L i s t > 
 
 	 	 	 	 	 < o t h e r I l l n e s s > 0 1 6 A - 0 1 < / o t h e r I l l n e s s > 
 
 	 	 	 	 	 < o t h e r I l l n e s s > 0 1 6 A - 0 3 < / o t h e r I l l n e s s > 
 
 	 	 	 	 < / o t h e r I l l n e s s L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 9 - p s y c h o l o g i c a l   d i s t r e s s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 0 - s a f e t y   t o   s e l f " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < s u i c i d e A t t e m p t > T R U E < / s u i c i d e A t t e m p t > 
 
 	 	 	 	 < s u i c i d e T h o u g h t s > T R U E < / s u i c i d e T h o u g h t s > 
 
 	 	 	 	 < s a f e t y C o n c e r n S e l f > T R U E < / s a f e t y C o n c e r n S e l f > 
 
 	 	 	 	 < s a f e t y T o S e l f R i s k L i s t   o t h e r S a f e t y T o S e l f R i s k = " o t h e r   r i s k   t o   s e l f   t e x t " > 
 
 	 	 	 	 	 < s a f e t y T o S e l f R i s k > 2 2 5 9 1 5 0 0 6 < / s a f e t y T o S e l f R i s k > 
 
 	 	 	 	 	 < s a f e t y T o S e l f R i s k > 4 0 1 2 0 6 0 0 8 < / s a f e t y T o S e l f R i s k > 
 
 	 	 	 	 	 < s a f e t y T o S e l f R i s k > 4 1 0 5 1 5 0 0 3 < / s a f e t y T o S e l f R i s k > 
 
 	 	 	 	 < / s a f e t y T o S e l f R i s k L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 1 - s a f e t y   t o   o t h e r s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 2 - a l c o h o l " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 	 < d r i n k A l c o h o l   q u a n t i t y = " 6 "   f r e q u e n c y = " 1 " / > 
 
 	 	 	 	 < s t a g e O f C h a n g e A l c o h o l > 5 < / s t a g e O f C h a n g e A l c o h o l > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 3 - d r u g s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 2 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 	 < d r u g U s e L i s t   i n j e c t e d = " 6 " > 
 
 	 	 	 	 	 < d r u g U s e   n a m e = " 3 9 8 7 0 5 0 0 4 "   f r e q u e n c y = " 5 " / > 
 
 	 	 	 	 	 < d r u g U s e   n a m e = " 2 2 6 0 4 4 0 0 4 "   f r e q u e n c y = " 6 " / > 
 
 	 	 	 	 	 < d r u g U s e   n a m e = " 4 1 0 5 1 5 0 0 3 "   f r e q u e n c y = " 5 " / > 
 
 	 	 	 	 < / d r u g U s e L i s t > 
 
 	 	 	 	 < s t a g e O f C h a n g e D r u g s > 2 < / s t a g e O f C h a n g e D r u g s > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 4 - o t h e r   a d d i c t i o n s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 	 < a d d i c t i o n T y p e L i s t   o t h e r A d d i c t i o n T y p e = " o t h e r   a d d i c t i o n   t e x t " > 
 
 	 	 	 	 	 < a d d i c t i o n T y p e > 1 0 5 5 2 3 0 0 9 < / a d d i c t i o n T y p e > 
 
 	 	 	 	 	 < a d d i c t i o n T y p e > 5 6 2 9 4 0 0 8 < / a d d i c t i o n T y p e > 
 
 	 	 	 	 	 < a d d i c t i o n T y p e > 4 1 0 5 1 5 0 0 3 < / a d d i c t i o n T y p e > 
 
 	 	 	 	 < / a d d i c t i o n T y p e L i s t > 
 
 	 	 	 	 < s t a g e O f C h a n g e A d d i c t i o n s > 3 < / s t a g e O f C h a n g e A d d i c t i o n s > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 5 - c o m p a n y " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 2 " / > 
 
 	 	 	 	 < c h a n g e d S o c i a l P a t t e r n s > C D A < / c h a n g e d S o c i a l P a t t e r n s > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 6 - i n t i m a t e   r e l a t i o n s h i p s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 7 - s e x u a l   e x p r e s s i o n " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 8 - c h i l d c a r e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 9 - o t h e r   d e p e n d e n t s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 0 - b a s i c   e d u c a t i o n " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < h i g h e s t E d u c a t i o n L e v e l > H L E S - 6 < / h i g h e s t E d u c a t i o n L e v e l > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 1 - t e l e p h o n e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 2 - t r a n s p o r t " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 3 - m o n e y " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 	 < s o u r c e O f I n c o m e > 0 3 1 - 1 0 < / s o u r c e O f I n c o m e > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 4 - b e n e f i t s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 < / O C A N D o m a i n s > 
 
 	 	 < a d d i t i o n a l E l e m e n t s > 
 
 	 	 	 < ! - -   B e g i n   F r e e   T e x t   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < c l i e n t H o p e s F o r F u t u r e / > 
 
 	 	 	 < c l i e n t N e e d T o G e t T h e r e / > 
 
 	 	 	 < c l i e n t V i e w M e n t a l H e a l t h / > 
 
 	 	 	 < c l i e n t S p i r i t u a l i t y I m p o r t a n c e / > 
 
 	 	 	 < c l i e n t C u l t u r e H e r i t a g e I m p o r t a n c e / > 
 
 	 	 	 < ! - -   E n d   F r e e   T e x t   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < p r e s e n t i n g I s s u e L i s t > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 1 " / > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 3 " / > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 5 " / > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 8 " / > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 9 " / > 
 
 	 	 	 < / p r e s e n t i n g I s s u e L i s t > 
 
 	 	 	 < a c t i o n L i s t > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 1 "   d o m a i n = " 1 3 - d r u g s " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 2 "   d o m a i n = " 1 5 - c o m p a n y " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 3 "   d o m a i n = " 2 0 - b a s i c   e d u c a t i o n " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 4 "   d o m a i n = " 2 3 - m o n e y " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 5 "   d o m a i n = " 1 0 - s a f e t y   t o   s e l f " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 6 "   d o m a i n = " 2 4 - b e n e f i t s " / > 
 
 	 	 	 < / a c t i o n L i s t > 
 
 	 	 	 < r e f e r r a l L i s t > 
 
 	 	 	 	 < r e f e r r a l   o p t i m a l = " 0 A S "   s p e c i f y O p t i m a l = " s p e c i f y   t e x t   f o r   o p t i m a l   r e f e r r a l "   a c t u a l = " F O R "   s p e c i f y A c t u a l = " s p e c i f y   t e x t   f o r   a c t u a l   r e f e r r a l "   d i f f e r e n c e R e a s o n = " R D - 4 "   s t a t u s = " R S - 1 " / > 
 
 	 	 	 	 < r e f e r r a l   o p t i m a l = " 0 A B "   s p e c i f y O p t i m a l = " s p e c i f y   t e x t   f o r   o p t i m a l   r e f e r r a l "   a c t u a l = " H P A "   s p e c i f y A c t u a l = " s p e c i f y   t e x t   f o r   a c t u a l   r e f e r r a l "   d i f f e r e n c e R e a s o n = " R D - 2 "   s t a t u s = " R S - 2 " / > 
 
 	 	 	 	 < r e f e r r a l   o p t i m a l = " E A T "   s p e c i f y O p t i m a l = " s p e c i f y   t e x t   f o r   o p t i m a l   r e f e r r a l "   a c t u a l = " H S C "   s p e c i f y A c t u a l = " s p e c i f y   t e x t   f o r   a c t u a l   r e f e r r a l "   d i f f e r e n c e R e a s o n = " R D - 3 "   s t a t u s = " R S - 1 " / > 
 
 	 	 	 	 < r e f e r r a l   o p t i m a l = " 0 F I "   s p e c i f y O p t i m a l = " s p e c i f y   t e x t   f o r   o p t i m a l   r e f e r r a l "   a c t u a l = " 0 S R "   s p e c i f y A c t u a l = " s p e c i f y   t e x t   f o r   a c t u a l   r e f e r r a l "   d i f f e r e n c e R e a s o n = " R D - 1 "   s t a t u s = " R S - 2 " / > 
 
 	 	 	 < / r e f e r r a l L i s t > 
 
 	 	 < / a d d i t i o n a l E l e m e n t s > 
 
 	 < / O C A N S u b m i s s i o n R e c o r d > 
 
 
 
 	 < ! - -   a s s e s s m e n t   r e c o r d #   7 :   j u s t   a n o t h e r   a s s e s s m e n t   - - > 
 
 	 < O C A N S u b m i s s i o n R e c o r d   a s s e s s m e n t I D = " 1 0 0 7 "   s t a r t D a t e = " 2 0 0 9 - 0 1 - 1 1 Z "   c o m p l e t i o n D a t e = " 2 0 0 9 - 0 1 - 1 6 Z "   a s s e s s m e n t S t a t u s = " C o m p l e t e " > 
 
 	 	 < o r g a n i z a t i o n R e c o r d > 
 
 	 	 	 < s e r v i c e O r g   n a m e = " N E   L H I N   T e s t   O r g "   n u m b e r = " 1 2 3 " / > 
 
 	 	 	 < p r o g r a m   n a m e = " I n t e n s i v e   C a s e   M a n a g e m e n t "   n u m b e r = " 3 0 2 5 " / > 
 
 	 	 	 < M I S F u n c t i o n   v a l u e = " 7 2 5   5 1   7 6   2 0 " / > 
 
 	 	 < / o r g a n i z a t i o n R e c o r d > 
 
 	 	 < c l i e n t R e c o r d > 
 
 	 	 	 < c l i e n t I D   o r g C l i e n t I D = " 1 2 3 7 0 0 " / > 
 
 	 	 	 < ! - -   B e g i n   P H I   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < c l i e n t N a m e   l a s t = " "   f i r s t = " " / > 
 
 	 	 	 < c l i e n t A d d r e s s   l i n e 1 = " "   l i n e 2 = " "   c i t y = " "   p r o v i n c e = " "   p o s t a l C o d e = " " / > 
 
 	 	 	 < c l i e n t P h o n e / > 
 
 	 	 	 < c l i e n t O H I P   n u m b e r = " "   v e r s i o n = " " / > 
 
 	 	 	 < c l i e n t C u l t u r e / > 
 
 	 	 	 < ! - -   E n d   P H I   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < r e a s o n F o r A s s e s s m e n t   v a l u e = " I A "   o t h e r = " " / > 
 
 	 	 	 < c l i e n t C o n t a c t > 
 
 	 	 	 	 < d o c t o r C o n t a c t   d o c t o r = " T R U E "   l a s t S e e n = " L S - 6 " / > 
 
 	 	 	 	 < p s y c h i a t r i s t C o n t a c t   p s y c h i a t r i s t = " T R U E "   l a s t S e e n = " L S - 1 2 " / > 
 
 	 	 	 	 < o t h e r P r a c t i t i o n e r C o n t a c t   p r a c t i t i o n e r T y p e = " 3 1 1 1 "   l a s t S e e n = " L S - 1 " / > 
 
 	 	 	 	 < o t h e r P r a c t i t i o n e r C o n t a c t   p r a c t i t i o n e r T y p e = " 3 1 1 2 "   l a s t S e e n = " L S - 1 3 " / > 
 
 	 	 	 	 < o t h e r P r a c t i t i o n e r C o n t a c t   p r a c t i t i o n e r T y p e = " 4 2 1 7 "   l a s t S e e n = " L S - 6 " / > 
 
 	 	 	 	 < o t h e r A g e n c y C o n t a c t   l a s t S e e n = " L S - 1 2 " / > 
 
 	 	 	 < / c l i e n t C o n t a c t > 
 
 	 	 	 < s e r v i c e R e c i p i e n t L o c a t i o n > 0 1 0 - 5 2 < / s e r v i c e R e c i p i e n t L o c a t i o n > 
 
 	 	 	 < s e r v i c e R e c i p i e n t L H I N > 1 2 < / s e r v i c e R e c i p i e n t L H I N > 
 
 	 	 	 < s e r v i c e D e l i v e r y L H I N > 5 < / s e r v i c e D e l i v e r y L H I N > 
 
 	 	 	 < c l i e n t D O B > 1 9 9 9 - 0 9 - 1 5 Z < / c l i e n t D O B > 
 
 	 	 	 < g e n d e r > M < / g e n d e r > 
 
 	 	 	 < m a r i t a l S t a t u s > 1 2 5 6 8 1 0 0 6 < / m a r i t a l S t a t u s > 
 
 	 	 	 < c l i e n t C a p a c i t y   p r o p e r t y = " T R U E "   p e r s o n a l C a r e = " F A L S E "   l e g a l G u a r d i a n = " C D A " / > 
 
 	 	 	 < r e f e r r a l S o u r c e > 0 1 8 - 0 5 < / r e f e r r a l S o u r c e > 
 
 	 	 	 < a b o r i g i n a l O r i g i n > 0 1 1 - 0 2 < / a b o r i g i n a l O r i g i n > 
 
 	 	 	 < c i t i z e n s h i p S t a t u s > C D N < / c i t i z e n s h i p S t a t u s > 
 
 	 	 	 < t i m e L i v e d I n C a n a d a   y e a r s = " 3 "   m o n t h s = " 8 " / > 
 
 	 	 	 < i m m i g E x p L i s t   o t h e r I m m i g E x p = " o t h e r   i m m i g r a t i o n   e x p e r i e n c e   t e x t " > 
 
 	 	 	 	 < v a l u e > 2 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 3 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 7 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 8 < / v a l u e > 
 
 	 	 	 < / i m m i g E x p L i s t > 
 
 	 	 	 < d i s c r i m E x p L i s t   o t h e r D i s c r i m E x p = " o t h e r   d i s c r i m i n a t i o n   e x p e r i e n c e   t e x t " > 
 
 	 	 	 	 < v a l u e > 2 1 1 3 4 0 0 2 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 3 6 5 8 7 3 0 0 7 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 4 1 0 5 1 5 0 0 3 < / v a l u e > 
 
 	 	 	 < / d i s c r i m E x p L i s t > 
 
 	 	 	 < p r e f L a n g > f r a < / p r e f L a n g > 
 
 	 	 	 < s e r v i c e L a n g > e n g < / s e r v i c e L a n g > 
 
 	 	 	 < l e g a l I s s u e s > C i v i l < / l e g a l I s s u e s > 
 
 	 	 	 < l e g a l S t a t u s L i s t > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 1 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 2 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 3 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 8 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 9 < / l e g a l S t a t u s > 
 
 	 	 	 < / l e g a l S t a t u s L i s t > 
 
 	 	 	 < e x i t D i s p o s i t i o n > 0 1 9 - 0 6 < / e x i t D i s p o s i t i o n > 
 
 	 	 < / c l i e n t R e c o r d > 
 
 	 	 < O C A N D o m a i n s > 
 
 	 	 	 < d o m a i n   n a m e = " 0 1 - a c c o m m o d a t i o n " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 	 < r e s i d e n c e T y p e > 0 2 4 - 1 9 < / r e s i d e n c e T y p e > 
 
 	 	 	 	 < r e s i d e n c e S u p p o r t > 2 4 A - 0 5 < / r e s i d e n c e S u p p o r t > 
 
 	 	 	 	 < l i v i n g A r r a n g e m e n t T y p e > 0 2 3 - 0 8 < / l i v i n g A r r a n g e m e n t T y p e > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 2 - f o o d " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 3 - l o o k i n g   a f t e r   t h e   h o m e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 4 - s e l f - c a r e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 5 - d a y t i m e   a c t i v i t i e s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 3 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 	 < e m p l o y S t a t u s > 2 2 4 3 6 6 0 0 4 < / e m p l o y S t a t u s > 
 
 	 	 	 	 < e d u c a t i o n P r o g r a m S t a t u s > 2 2 4 8 7 0 0 0 1 < / e d u c a t i o n P r o g r a m S t a t u s > 
 
 	 	 	 	 < r i s k U n e m p l o y m e n t L i s t > 
 
 	 	 	 	 	 < r i s k U n e m p l o y m e n t > U D E R - 0 2 < / r i s k U n e m p l o y m e n t > 
 
 	 	 	 	 	 < r i s k U n e m p l o y m e n t > U D E R - 0 3 < / r i s k U n e m p l o y m e n t > 
 
 	 	 	 	 	 < r i s k U n e m p l o y m e n t > U D E R - 0 6 < / r i s k U n e m p l o y m e n t > 
 
 	 	 	 	 < / r i s k U n e m p l o y m e n t L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 6 - p h y s i c a l   h e a l t h " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < m e d i c a l C o n d i t i o n L i s t   a u t i s m D e t a i l = " a u t i s m   s p e c i f i c   t e x t "   o t h e r D e t a i l = " o t h e r   m e d i c a l   t e x t " > 
 
 	 	 	 	 	 < m e d i c a l C o n d i t i o n > 4 0 8 8 5 6 0 0 3 < / m e d i c a l C o n d i t i o n > 
 
 	 	 	 	 	 < m e d i c a l C o n d i t i o n > 4 1 0 5 1 5 0 0 3 < / m e d i c a l C o n d i t i o n > 
 
 	 	 	 	 	 < m e d i c a l C o n d i t i o n > 4 1 4 9 1 6 0 0 1 < / m e d i c a l C o n d i t i o n > 
 
 	 	 	 	 < / m e d i c a l C o n d i t i o n L i s t > 
 
 	 	 	 	 < p h y s i c a l H e a l t h C o n c e r n > T R U E < / p h y s i c a l H e a l t h C o n c e r n > 
 
 	 	 	 	 < c o n c e r n A r e a L i s t   o t h e r C o n c e r n A r e a = " o t h e r   c o n c e r n   t e x t " > 
 
 	 	 	 	 	 < c o n c e r n A r e a > 1 1 9 4 1 5 0 0 7 < / c o n c e r n A r e a > 
 
 	 	 	 	 	 < c o n c e r n A r e a > 1 1 8 9 5 2 0 0 5 < / c o n c e r n A r e a > 
 
 	 	 	 	 	 < c o n c e r n A r e a > 4 1 0 5 1 5 0 0 3 < / c o n c e r n A r e a > 
 
 	 	 	 	 < / c o n c e r n A r e a L i s t > 
 
 	 	 	 	 < m e d i c a t i o n L i s t > 
 
 	 	 	 	 	 < m e d i c a t i o n D e t a i l   t a k e n A s P r e s c r i b e d = " T R U E "   i s H e l p P r o v i d e d = " F A L S E "   i s H e l p N e e d e d = " T R U E " / > 
 
 	 	 	 	 	 < m e d i c a t i o n D e t a i l   t a k e n A s P r e s c r i b e d = " F A L S E "   i s H e l p P r o v i d e d = " U N K "   i s H e l p N e e d e d = " T R U E " / > 
 
 	 	 	 	 	 < m e d i c a t i o n D e t a i l   t a k e n A s P r e s c r i b e d = " U N K "   i s H e l p P r o v i d e d = " T R U E "   i s H e l p N e e d e d = " F A L S E " / > 
 
 	 	 	 	 < / m e d i c a t i o n L i s t > 
 
 	 	 	 	 < s i d e E f f e c t s > T R U E < / s i d e E f f e c t s > 
 
 	 	 	 	 < d a i l y L i v i n g A f f e c t e d > T R U E < / d a i l y L i v i n g A f f e c t e d > 
 
 	 	 	 	 < s i d e E f f e c t s D e t a i l L i s t   o t h e r S i d e E f f e c t s D e t a i l = " o t h e r   s i d e   e f f e c t   t e x t " > 
 
 	 	 	 	 	 < s i d e E f f e c t s D e t a i l > 2 4 6 6 3 6 0 0 8 < / s i d e E f f e c t s D e t a i l > 
 
 	 	 	 	 	 < s i d e E f f e c t s D e t a i l > 5 3 6 1 9 0 0 0 < / s i d e E f f e c t s D e t a i l > 
 
 	 	 	 	 	 < s i d e E f f e c t s D e t a i l > 4 1 0 5 1 5 0 0 3 < / s i d e E f f e c t s D e t a i l > 
 
 	 	 	 	 < / s i d e E f f e c t s D e t a i l L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 7 - p s y c h o t i c   s y m p t o m s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 	 < h o s p i t a l i z e d P a s t T w o Y e a r s > T R U E < / h o s p i t a l i z e d P a s t T w o Y e a r s > 
 
 	 	 	 	 < t o t a l A d m i s s i o n s > 5 < / t o t a l A d m i s s i o n s > 
 
 	 	 	 	 < t o t a l H o s p i t a l D a y s > 7 8 < / t o t a l H o s p i t a l D a y s > 
 
 	 	 	 	 < c o m m u n i t y T r e a t O r d e r > 0 1 5 - 0 3 < / c o m m u n i t y T r e a t O r d e r > 
 
 	 	 	 	 < s y m p t o m L i s t   o t h e r S y m p t o m = " o t h e r   s y m p t o m   t e x t " > 
 
 	 	 	 	 	 < s y m p t o m > 4 1 0 5 1 6 0 0 2 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 7 5 4 0 8 0 0 8 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 2 0 7 3 0 0 0 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 1 4 0 2 0 0 1 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 5 5 9 2 9 0 0 7 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 4 1 0 5 1 5 0 0 3 < / s y m p t o m > 
 
 	 	 	 	 < / s y m p t o m L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 8 - c o n d i t i o n   a n d   t r e a t m e n t " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 	 < d i a g n o s t i c L i s t > 
 
 	 	 	 	 	 < d i a g n o s t i c > 2 7 7 6 0 0 0 < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > M D G M C < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > 8 7 8 5 8 0 0 2 < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > S R D D H < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > D H < / d i a g n o s t i c > 
 
 	 	 	 	 < / d i a g n o s t i c L i s t > 
 
 	 	 	 	 < o t h e r I l l n e s s L i s t > 
 
 	 	 	 	 	 < o t h e r I l l n e s s > 0 1 6 A - 0 1 < / o t h e r I l l n e s s > 
 
 	 	 	 	 	 < o t h e r I l l n e s s > 0 1 6 A - 0 3 < / o t h e r I l l n e s s > 
 
 	 	 	 	 < / o t h e r I l l n e s s L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 9 - p s y c h o l o g i c a l   d i s t r e s s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 0 - s a f e t y   t o   s e l f " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < s u i c i d e A t t e m p t > T R U E < / s u i c i d e A t t e m p t > 
 
 	 	 	 	 < s u i c i d e T h o u g h t s > T R U E < / s u i c i d e T h o u g h t s > 
 
 	 	 	 	 < s a f e t y C o n c e r n S e l f > T R U E < / s a f e t y C o n c e r n S e l f > 
 
 	 	 	 	 < s a f e t y T o S e l f R i s k L i s t   o t h e r S a f e t y T o S e l f R i s k = " o t h e r   r i s k   t o   s e l f   t e x t " > 
 
 	 	 	 	 	 < s a f e t y T o S e l f R i s k > 2 2 5 9 1 5 0 0 6 < / s a f e t y T o S e l f R i s k > 
 
 	 	 	 	 	 < s a f e t y T o S e l f R i s k > 4 0 1 2 0 6 0 0 8 < / s a f e t y T o S e l f R i s k > 
 
 	 	 	 	 	 < s a f e t y T o S e l f R i s k > 4 1 0 5 1 5 0 0 3 < / s a f e t y T o S e l f R i s k > 
 
 	 	 	 	 < / s a f e t y T o S e l f R i s k L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 1 - s a f e t y   t o   o t h e r s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 2 - a l c o h o l " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 	 < d r i n k A l c o h o l   q u a n t i t y = " 6 "   f r e q u e n c y = " 1 " / > 
 
 	 	 	 	 < s t a g e O f C h a n g e A l c o h o l > 5 < / s t a g e O f C h a n g e A l c o h o l > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 3 - d r u g s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 2 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 	 < d r u g U s e L i s t   i n j e c t e d = " 6 " > 
 
 	 	 	 	 	 < d r u g U s e   n a m e = " 3 9 8 7 0 5 0 0 4 "   f r e q u e n c y = " 5 " / > 
 
 	 	 	 	 	 < d r u g U s e   n a m e = " 2 2 6 0 4 4 0 0 4 "   f r e q u e n c y = " 6 " / > 
 
 	 	 	 	 	 < d r u g U s e   n a m e = " 4 1 0 5 1 5 0 0 3 "   f r e q u e n c y = " 5 " / > 
 
 	 	 	 	 < / d r u g U s e L i s t > 
 
 	 	 	 	 < s t a g e O f C h a n g e D r u g s > 2 < / s t a g e O f C h a n g e D r u g s > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 4 - o t h e r   a d d i c t i o n s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 	 < a d d i c t i o n T y p e L i s t   o t h e r A d d i c t i o n T y p e = " o t h e r   a d d i c t i o n   t e x t " > 
 
 	 	 	 	 	 < a d d i c t i o n T y p e > 1 0 5 5 2 3 0 0 9 < / a d d i c t i o n T y p e > 
 
 	 	 	 	 	 < a d d i c t i o n T y p e > 5 6 2 9 4 0 0 8 < / a d d i c t i o n T y p e > 
 
 	 	 	 	 	 < a d d i c t i o n T y p e > 4 1 0 5 1 5 0 0 3 < / a d d i c t i o n T y p e > 
 
 	 	 	 	 < / a d d i c t i o n T y p e L i s t > 
 
 	 	 	 	 < s t a g e O f C h a n g e A d d i c t i o n s > 3 < / s t a g e O f C h a n g e A d d i c t i o n s > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 5 - c o m p a n y " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 2 " / > 
 
 	 	 	 	 < c h a n g e d S o c i a l P a t t e r n s > C D A < / c h a n g e d S o c i a l P a t t e r n s > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 6 - i n t i m a t e   r e l a t i o n s h i p s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 7 - s e x u a l   e x p r e s s i o n " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 8 - c h i l d c a r e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 9 - o t h e r   d e p e n d e n t s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 0 - b a s i c   e d u c a t i o n " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < h i g h e s t E d u c a t i o n L e v e l > H L E S - 6 < / h i g h e s t E d u c a t i o n L e v e l > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 1 - t e l e p h o n e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 2 - t r a n s p o r t " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 3 - m o n e y " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 	 < s o u r c e O f I n c o m e > 0 3 1 - 1 0 < / s o u r c e O f I n c o m e > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 4 - b e n e f i t s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 < / O C A N D o m a i n s > 
 
 	 	 < a d d i t i o n a l E l e m e n t s > 
 
 	 	 	 < ! - -   B e g i n   F r e e   T e x t   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < c l i e n t H o p e s F o r F u t u r e / > 
 
 	 	 	 < c l i e n t N e e d T o G e t T h e r e / > 
 
 	 	 	 < c l i e n t V i e w M e n t a l H e a l t h / > 
 
 	 	 	 < c l i e n t S p i r i t u a l i t y I m p o r t a n c e / > 
 
 	 	 	 < c l i e n t C u l t u r e H e r i t a g e I m p o r t a n c e / > 
 
 	 	 	 < ! - -   E n d   F r e e   T e x t   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < p r e s e n t i n g I s s u e L i s t > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 1 " / > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 3 " / > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 5 " / > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 8 " / > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 9 " / > 
 
 	 	 	 < / p r e s e n t i n g I s s u e L i s t > 
 
 	 	 	 < a c t i o n L i s t > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 1 "   d o m a i n = " 1 3 - d r u g s " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 2 "   d o m a i n = " 1 5 - c o m p a n y " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 3 "   d o m a i n = " 2 0 - b a s i c   e d u c a t i o n " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 4 "   d o m a i n = " 2 3 - m o n e y " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 5 "   d o m a i n = " 1 0 - s a f e t y   t o   s e l f " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 6 "   d o m a i n = " 2 4 - b e n e f i t s " / > 
 
 	 	 	 < / a c t i o n L i s t > 
 
 	 	 	 < r e f e r r a l L i s t > 
 
 	 	 	 	 < r e f e r r a l   o p t i m a l = " 0 A S "   s p e c i f y O p t i m a l = " s p e c i f y   t e x t   f o r   o p t i m a l   r e f e r r a l "   a c t u a l = " F O R "   s p e c i f y A c t u a l = " s p e c i f y   t e x t   f o r   a c t u a l   r e f e r r a l "   d i f f e r e n c e R e a s o n = " R D - 4 "   s t a t u s = " R S - 1 " / > 
 
 	 	 	 	 < r e f e r r a l   o p t i m a l = " 0 A B "   s p e c i f y O p t i m a l = " s p e c i f y   t e x t   f o r   o p t i m a l   r e f e r r a l "   a c t u a l = " H P A "   s p e c i f y A c t u a l = " s p e c i f y   t e x t   f o r   a c t u a l   r e f e r r a l "   d i f f e r e n c e R e a s o n = " R D - 2 "   s t a t u s = " R S - 2 " / > 
 
 	 	 	 	 < r e f e r r a l   o p t i m a l = " E A T "   s p e c i f y O p t i m a l = " s p e c i f y   t e x t   f o r   o p t i m a l   r e f e r r a l "   a c t u a l = " H S C "   s p e c i f y A c t u a l = " s p e c i f y   t e x t   f o r   a c t u a l   r e f e r r a l "   d i f f e r e n c e R e a s o n = " R D - 3 "   s t a t u s = " R S - 1 " / > 
 
 	 	 	 	 < r e f e r r a l   o p t i m a l = " 0 F I "   s p e c i f y O p t i m a l = " s p e c i f y   t e x t   f o r   o p t i m a l   r e f e r r a l "   a c t u a l = " 0 S R "   s p e c i f y A c t u a l = " s p e c i f y   t e x t   f o r   a c t u a l   r e f e r r a l "   d i f f e r e n c e R e a s o n = " R D - 1 "   s t a t u s = " R S - 2 " / > 
 
 	 	 	 < / r e f e r r a l L i s t > 
 
 	 	 < / a d d i t i o n a l E l e m e n t s > 
 
 	 < / O C A N S u b m i s s i o n R e c o r d > 
 
 
 
 	 < ! - -   a s s e s s m e n t   r e c o r d #   8 :   j u s t   a n o t h e r   a s s e s s m e n t   - - > 
 
 	 < O C A N S u b m i s s i o n R e c o r d   a s s e s s m e n t I D = " 1 0 0 8 "   s t a r t D a t e = " 2 0 0 9 - 0 1 - 1 1 Z "   c o m p l e t i o n D a t e = " 2 0 0 9 - 0 1 - 1 6 Z "   a s s e s s m e n t S t a t u s = " C o m p l e t e " > 
 
 	 	 < o r g a n i z a t i o n R e c o r d > 
 
 	 	 	 < s e r v i c e O r g   n a m e = " N E   L H I N   T e s t   O r g "   n u m b e r = " 1 2 3 " / > 
 
 	 	 	 < p r o g r a m   n a m e = " I n t e n s i v e   C a s e   M a n a g e m e n t "   n u m b e r = " 3 0 2 5 " / > 
 
 	 	 	 < M I S F u n c t i o n   v a l u e = " 7 2 5   5 1   7 6   2 0 " / > 
 
 	 	 < / o r g a n i z a t i o n R e c o r d > 
 
 	 	 < c l i e n t R e c o r d > 
 
 	 	 	 < c l i e n t I D   o r g C l i e n t I D = " 1 2 3 8 0 0 " / > 
 
 	 	 	 < ! - -   B e g i n   P H I   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < c l i e n t N a m e   l a s t = " "   f i r s t = " " / > 
 
 	 	 	 < c l i e n t A d d r e s s   l i n e 1 = " "   l i n e 2 = " "   c i t y = " "   p r o v i n c e = " "   p o s t a l C o d e = " " / > 
 
 	 	 	 < c l i e n t P h o n e / > 
 
 	 	 	 < c l i e n t O H I P   n u m b e r = " "   v e r s i o n = " " / > 
 
 	 	 	 < c l i e n t C u l t u r e / > 
 
 	 	 	 < ! - -   E n d   P H I   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < r e a s o n F o r A s s e s s m e n t   v a l u e = " I A "   o t h e r = " " / > 
 
 	 	 	 < c l i e n t C o n t a c t > 
 
 	 	 	 	 < d o c t o r C o n t a c t   d o c t o r = " T R U E "   l a s t S e e n = " L S - 6 " / > 
 
 	 	 	 	 < p s y c h i a t r i s t C o n t a c t   p s y c h i a t r i s t = " T R U E "   l a s t S e e n = " L S - 1 2 " / > 
 
 	 	 	 	 < o t h e r P r a c t i t i o n e r C o n t a c t   p r a c t i t i o n e r T y p e = " 3 1 1 1 "   l a s t S e e n = " L S - 1 " / > 
 
 	 	 	 	 < o t h e r P r a c t i t i o n e r C o n t a c t   p r a c t i t i o n e r T y p e = " 3 1 1 2 "   l a s t S e e n = " L S - 1 3 " / > 
 
 	 	 	 	 < o t h e r P r a c t i t i o n e r C o n t a c t   p r a c t i t i o n e r T y p e = " 4 2 1 7 "   l a s t S e e n = " L S - 6 " / > 
 
 	 	 	 	 < o t h e r A g e n c y C o n t a c t   l a s t S e e n = " L S - 1 2 " / > 
 
 	 	 	 < / c l i e n t C o n t a c t > 
 
 	 	 	 < s e r v i c e R e c i p i e n t L o c a t i o n > 0 1 0 - 5 2 < / s e r v i c e R e c i p i e n t L o c a t i o n > 
 
 	 	 	 < s e r v i c e R e c i p i e n t L H I N > 1 2 < / s e r v i c e R e c i p i e n t L H I N > 
 
 	 	 	 < s e r v i c e D e l i v e r y L H I N > 5 < / s e r v i c e D e l i v e r y L H I N > 
 
 	 	 	 < c l i e n t D O B > 2 0 0 2 - 0 2 - 1 0 Z < / c l i e n t D O B > 
 
 	 	 	 < g e n d e r > C D A < / g e n d e r > 
 
 	 	 	 < m a r i t a l S t a t u s > 1 2 5 6 8 1 0 0 6 < / m a r i t a l S t a t u s > 
 
 	 	 	 < c l i e n t C a p a c i t y   p r o p e r t y = " T R U E "   p e r s o n a l C a r e = " F A L S E "   l e g a l G u a r d i a n = " C D A " / > 
 
 	 	 	 < r e f e r r a l S o u r c e > 0 1 8 - 0 5 < / r e f e r r a l S o u r c e > 
 
 	 	 	 < a b o r i g i n a l O r i g i n > 0 1 1 - 0 2 < / a b o r i g i n a l O r i g i n > 
 
 	 	 	 < c i t i z e n s h i p S t a t u s > P R < / c i t i z e n s h i p S t a t u s > 
 
 	 	 	 < t i m e L i v e d I n C a n a d a   y e a r s = " 3 "   m o n t h s = " 8 " / > 
 
 	 	 	 < i m m i g E x p L i s t   o t h e r I m m i g E x p = " o t h e r   i m m i g r a t i o n   e x p e r i e n c e   t e x t " > 
 
 	 	 	 	 < v a l u e > 2 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 3 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 7 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 8 < / v a l u e > 
 
 	 	 	 < / i m m i g E x p L i s t > 
 
 	 	 	 < d i s c r i m E x p L i s t   o t h e r D i s c r i m E x p = " o t h e r   d i s c r i m i n a t i o n   e x p e r i e n c e   t e x t " > 
 
 	 	 	 	 < v a l u e > 2 1 1 3 4 0 0 2 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 3 6 5 8 7 3 0 0 7 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 4 1 0 5 1 5 0 0 3 < / v a l u e > 
 
 	 	 	 < / d i s c r i m E x p L i s t > 
 
 	 	 	 < p r e f L a n g > f r a < / p r e f L a n g > 
 
 	 	 	 < s e r v i c e L a n g > e n g < / s e r v i c e L a n g > 
 
 	 	 	 < l e g a l I s s u e s > C i v i l < / l e g a l I s s u e s > 
 
 	 	 	 < l e g a l S t a t u s L i s t > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 1 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 2 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 3 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 8 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 9 < / l e g a l S t a t u s > 
 
 	 	 	 < / l e g a l S t a t u s L i s t > 
 
 	 	 	 < e x i t D i s p o s i t i o n > 0 1 9 - 0 6 < / e x i t D i s p o s i t i o n > 
 
 	 	 < / c l i e n t R e c o r d > 
 
 	 	 < O C A N D o m a i n s > 
 
 	 	 	 < d o m a i n   n a m e = " 0 1 - a c c o m m o d a t i o n " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 	 < r e s i d e n c e T y p e > 0 2 4 - 1 9 < / r e s i d e n c e T y p e > 
 
 	 	 	 	 < r e s i d e n c e S u p p o r t > 2 4 A - 0 5 < / r e s i d e n c e S u p p o r t > 
 
 	 	 	 	 < l i v i n g A r r a n g e m e n t T y p e > 0 2 3 - 0 8 < / l i v i n g A r r a n g e m e n t T y p e > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 2 - f o o d " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 3 - l o o k i n g   a f t e r   t h e   h o m e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 4 - s e l f - c a r e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 5 - d a y t i m e   a c t i v i t i e s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 3 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 	 < e m p l o y S t a t u s > 2 2 4 3 6 6 0 0 4 < / e m p l o y S t a t u s > 
 
 	 	 	 	 < e d u c a t i o n P r o g r a m S t a t u s > 2 2 4 8 7 0 0 0 1 < / e d u c a t i o n P r o g r a m S t a t u s > 
 
 	 	 	 	 < r i s k U n e m p l o y m e n t L i s t > 
 
 	 	 	 	 	 < r i s k U n e m p l o y m e n t > U D E R - 0 2 < / r i s k U n e m p l o y m e n t > 
 
 	 	 	 	 	 < r i s k U n e m p l o y m e n t > U D E R - 0 3 < / r i s k U n e m p l o y m e n t > 
 
 	 	 	 	 	 < r i s k U n e m p l o y m e n t > U D E R - 0 6 < / r i s k U n e m p l o y m e n t > 
 
 	 	 	 	 < / r i s k U n e m p l o y m e n t L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 6 - p h y s i c a l   h e a l t h " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < m e d i c a l C o n d i t i o n L i s t   a u t i s m D e t a i l = " a u t i s m   s p e c i f i c   t e x t "   o t h e r D e t a i l = " o t h e r   m e d i c a l   t e x t " > 
 
 	 	 	 	 	 < m e d i c a l C o n d i t i o n > 4 0 8 8 5 6 0 0 3 < / m e d i c a l C o n d i t i o n > 
 
 	 	 	 	 	 < m e d i c a l C o n d i t i o n > 4 1 0 5 1 5 0 0 3 < / m e d i c a l C o n d i t i o n > 
 
 	 	 	 	 	 < m e d i c a l C o n d i t i o n > 4 1 4 9 1 6 0 0 1 < / m e d i c a l C o n d i t i o n > 
 
 	 	 	 	 < / m e d i c a l C o n d i t i o n L i s t > 
 
 	 	 	 	 < p h y s i c a l H e a l t h C o n c e r n > T R U E < / p h y s i c a l H e a l t h C o n c e r n > 
 
 	 	 	 	 < c o n c e r n A r e a L i s t   o t h e r C o n c e r n A r e a = " o t h e r   c o n c e r n   t e x t " > 
 
 	 	 	 	 	 < c o n c e r n A r e a > 1 1 9 4 1 5 0 0 7 < / c o n c e r n A r e a > 
 
 	 	 	 	 	 < c o n c e r n A r e a > 1 1 8 9 5 2 0 0 5 < / c o n c e r n A r e a > 
 
 	 	 	 	 	 < c o n c e r n A r e a > 4 1 0 5 1 5 0 0 3 < / c o n c e r n A r e a > 
 
 	 	 	 	 < / c o n c e r n A r e a L i s t > 
 
 	 	 	 	 < m e d i c a t i o n L i s t > 
 
 	 	 	 	 	 < m e d i c a t i o n D e t a i l   t a k e n A s P r e s c r i b e d = " T R U E "   i s H e l p P r o v i d e d = " F A L S E "   i s H e l p N e e d e d = " T R U E " / > 
 
 	 	 	 	 	 < m e d i c a t i o n D e t a i l   t a k e n A s P r e s c r i b e d = " F A L S E "   i s H e l p P r o v i d e d = " U N K "   i s H e l p N e e d e d = " T R U E " / > 
 
 	 	 	 	 	 < m e d i c a t i o n D e t a i l   t a k e n A s P r e s c r i b e d = " U N K "   i s H e l p P r o v i d e d = " T R U E "   i s H e l p N e e d e d = " F A L S E " / > 
 
 	 	 	 	 < / m e d i c a t i o n L i s t > 
 
 	 	 	 	 < s i d e E f f e c t s > T R U E < / s i d e E f f e c t s > 
 
 	 	 	 	 < d a i l y L i v i n g A f f e c t e d > T R U E < / d a i l y L i v i n g A f f e c t e d > 
 
 	 	 	 	 < s i d e E f f e c t s D e t a i l L i s t   o t h e r S i d e E f f e c t s D e t a i l = " o t h e r   s i d e   e f f e c t   t e x t " > 
 
 	 	 	 	 	 < s i d e E f f e c t s D e t a i l > 2 4 6 6 3 6 0 0 8 < / s i d e E f f e c t s D e t a i l > 
 
 	 	 	 	 	 < s i d e E f f e c t s D e t a i l > 5 3 6 1 9 0 0 0 < / s i d e E f f e c t s D e t a i l > 
 
 	 	 	 	 	 < s i d e E f f e c t s D e t a i l > 4 1 0 5 1 5 0 0 3 < / s i d e E f f e c t s D e t a i l > 
 
 	 	 	 	 < / s i d e E f f e c t s D e t a i l L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 7 - p s y c h o t i c   s y m p t o m s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 	 < h o s p i t a l i z e d P a s t T w o Y e a r s > T R U E < / h o s p i t a l i z e d P a s t T w o Y e a r s > 
 
 	 	 	 	 < t o t a l A d m i s s i o n s > 5 < / t o t a l A d m i s s i o n s > 
 
 	 	 	 	 < t o t a l H o s p i t a l D a y s > 7 8 < / t o t a l H o s p i t a l D a y s > 
 
 	 	 	 	 < c o m m u n i t y T r e a t O r d e r > 0 1 5 - 0 3 < / c o m m u n i t y T r e a t O r d e r > 
 
 	 	 	 	 < s y m p t o m L i s t   o t h e r S y m p t o m = " o t h e r   s y m p t o m   t e x t " > 
 
 	 	 	 	 	 < s y m p t o m > 4 1 0 5 1 6 0 0 2 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 7 5 4 0 8 0 0 8 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 2 0 7 3 0 0 0 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 1 4 0 2 0 0 1 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 5 5 9 2 9 0 0 7 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 4 1 0 5 1 5 0 0 3 < / s y m p t o m > 
 
 	 	 	 	 < / s y m p t o m L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 8 - c o n d i t i o n   a n d   t r e a t m e n t " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 	 < d i a g n o s t i c L i s t > 
 
 	 	 	 	 	 < d i a g n o s t i c > 2 7 7 6 0 0 0 < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > M D G M C < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > 8 7 8 5 8 0 0 2 < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > S R D D H < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > D H < / d i a g n o s t i c > 
 
 	 	 	 	 < / d i a g n o s t i c L i s t > 
 
 	 	 	 	 < o t h e r I l l n e s s L i s t > 
 
 	 	 	 	 	 < o t h e r I l l n e s s > 0 1 6 A - 0 1 < / o t h e r I l l n e s s > 
 
 	 	 	 	 	 < o t h e r I l l n e s s > 0 1 6 A - 0 3 < / o t h e r I l l n e s s > 
 
 	 	 	 	 < / o t h e r I l l n e s s L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 9 - p s y c h o l o g i c a l   d i s t r e s s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 0 - s a f e t y   t o   s e l f " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < s u i c i d e A t t e m p t > T R U E < / s u i c i d e A t t e m p t > 
 
 	 	 	 	 < s u i c i d e T h o u g h t s > T R U E < / s u i c i d e T h o u g h t s > 
 
 	 	 	 	 < s a f e t y C o n c e r n S e l f > T R U E < / s a f e t y C o n c e r n S e l f > 
 
 	 	 	 	 < s a f e t y T o S e l f R i s k L i s t   o t h e r S a f e t y T o S e l f R i s k = " o t h e r   r i s k   t o   s e l f   t e x t " > 
 
 	 	 	 	 	 < s a f e t y T o S e l f R i s k > 2 2 5 9 1 5 0 0 6 < / s a f e t y T o S e l f R i s k > 
 
 	 	 	 	 	 < s a f e t y T o S e l f R i s k > 4 0 1 2 0 6 0 0 8 < / s a f e t y T o S e l f R i s k > 
 
 	 	 	 	 	 < s a f e t y T o S e l f R i s k > 4 1 0 5 1 5 0 0 3 < / s a f e t y T o S e l f R i s k > 
 
 	 	 	 	 < / s a f e t y T o S e l f R i s k L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 1 - s a f e t y   t o   o t h e r s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 2 - a l c o h o l " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 	 < d r i n k A l c o h o l   q u a n t i t y = " 6 "   f r e q u e n c y = " 1 " / > 
 
 	 	 	 	 < s t a g e O f C h a n g e A l c o h o l > 5 < / s t a g e O f C h a n g e A l c o h o l > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 3 - d r u g s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 2 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 	 < d r u g U s e L i s t   i n j e c t e d = " 6 " > 
 
 	 	 	 	 	 < d r u g U s e   n a m e = " 3 9 8 7 0 5 0 0 4 "   f r e q u e n c y = " 5 " / > 
 
 	 	 	 	 	 < d r u g U s e   n a m e = " 2 2 6 0 4 4 0 0 4 "   f r e q u e n c y = " 6 " / > 
 
 	 	 	 	 	 < d r u g U s e   n a m e = " 4 1 0 5 1 5 0 0 3 "   f r e q u e n c y = " 5 " / > 
 
 	 	 	 	 < / d r u g U s e L i s t > 
 
 	 	 	 	 < s t a g e O f C h a n g e D r u g s > 2 < / s t a g e O f C h a n g e D r u g s > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 4 - o t h e r   a d d i c t i o n s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 	 < a d d i c t i o n T y p e L i s t   o t h e r A d d i c t i o n T y p e = " o t h e r   a d d i c t i o n   t e x t " > 
 
 	 	 	 	 	 < a d d i c t i o n T y p e > 1 0 5 5 2 3 0 0 9 < / a d d i c t i o n T y p e > 
 
 	 	 	 	 	 < a d d i c t i o n T y p e > 5 6 2 9 4 0 0 8 < / a d d i c t i o n T y p e > 
 
 	 	 	 	 	 < a d d i c t i o n T y p e > 4 1 0 5 1 5 0 0 3 < / a d d i c t i o n T y p e > 
 
 	 	 	 	 < / a d d i c t i o n T y p e L i s t > 
 
 	 	 	 	 < s t a g e O f C h a n g e A d d i c t i o n s > 3 < / s t a g e O f C h a n g e A d d i c t i o n s > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 5 - c o m p a n y " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 2 " / > 
 
 	 	 	 	 < c h a n g e d S o c i a l P a t t e r n s > C D A < / c h a n g e d S o c i a l P a t t e r n s > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 6 - i n t i m a t e   r e l a t i o n s h i p s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 7 - s e x u a l   e x p r e s s i o n " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 8 - c h i l d c a r e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 9 - o t h e r   d e p e n d e n t s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 0 - b a s i c   e d u c a t i o n " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < h i g h e s t E d u c a t i o n L e v e l > H L E S - 6 < / h i g h e s t E d u c a t i o n L e v e l > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 1 - t e l e p h o n e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 2 - t r a n s p o r t " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 3 - m o n e y " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 	 < s o u r c e O f I n c o m e > 0 3 1 - 1 0 < / s o u r c e O f I n c o m e > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 4 - b e n e f i t s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 < / O C A N D o m a i n s > 
 
 	 	 < a d d i t i o n a l E l e m e n t s > 
 
 	 	 	 < ! - -   B e g i n   F r e e   T e x t   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < c l i e n t H o p e s F o r F u t u r e / > 
 
 	 	 	 < c l i e n t N e e d T o G e t T h e r e / > 
 
 	 	 	 < c l i e n t V i e w M e n t a l H e a l t h / > 
 
 	 	 	 < c l i e n t S p i r i t u a l i t y I m p o r t a n c e / > 
 
 	 	 	 < c l i e n t C u l t u r e H e r i t a g e I m p o r t a n c e / > 
 
 	 	 	 < ! - -   E n d   F r e e   T e x t   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < p r e s e n t i n g I s s u e L i s t > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 1 " / > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 3 " / > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 5 " / > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 8 " / > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 9 " / > 
 
 	 	 	 < / p r e s e n t i n g I s s u e L i s t > 
 
 	 	 	 < a c t i o n L i s t > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 1 "   d o m a i n = " 1 3 - d r u g s " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 2 "   d o m a i n = " 1 5 - c o m p a n y " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 3 "   d o m a i n = " 2 0 - b a s i c   e d u c a t i o n " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 4 "   d o m a i n = " 2 3 - m o n e y " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 5 "   d o m a i n = " 1 0 - s a f e t y   t o   s e l f " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 6 "   d o m a i n = " 2 4 - b e n e f i t s " / > 
 
 	 	 	 < / a c t i o n L i s t > 
 
 	 	 	 < r e f e r r a l L i s t > 
 
 	 	 	 	 < r e f e r r a l   o p t i m a l = " 0 A S "   s p e c i f y O p t i m a l = " s p e c i f y   t e x t   f o r   o p t i m a l   r e f e r r a l "   a c t u a l = " F O R "   s p e c i f y A c t u a l = " s p e c i f y   t e x t   f o r   a c t u a l   r e f e r r a l "   d i f f e r e n c e R e a s o n = " R D - 4 "   s t a t u s = " R S - 1 " / > 
 
 	 	 	 	 < r e f e r r a l   o p t i m a l = " 0 A B "   s p e c i f y O p t i m a l = " s p e c i f y   t e x t   f o r   o p t i m a l   r e f e r r a l "   a c t u a l = " H P A "   s p e c i f y A c t u a l = " s p e c i f y   t e x t   f o r   a c t u a l   r e f e r r a l "   d i f f e r e n c e R e a s o n = " R D - 2 "   s t a t u s = " R S - 2 " / > 
 
 	 	 	 	 < r e f e r r a l   o p t i m a l = " E A T "   s p e c i f y O p t i m a l = " s p e c i f y   t e x t   f o r   o p t i m a l   r e f e r r a l "   a c t u a l = " H S C "   s p e c i f y A c t u a l = " s p e c i f y   t e x t   f o r   a c t u a l   r e f e r r a l "   d i f f e r e n c e R e a s o n = " R D - 3 "   s t a t u s = " R S - 1 " / > 
 
 	 	 	 	 < r e f e r r a l   o p t i m a l = " 0 F I "   s p e c i f y O p t i m a l = " s p e c i f y   t e x t   f o r   o p t i m a l   r e f e r r a l "   a c t u a l = " 0 S R "   s p e c i f y A c t u a l = " s p e c i f y   t e x t   f o r   a c t u a l   r e f e r r a l "   d i f f e r e n c e R e a s o n = " R D - 1 "   s t a t u s = " R S - 2 " / > 
 
 	 	 	 < / r e f e r r a l L i s t > 
 
 	 	 < / a d d i t i o n a l E l e m e n t s > 
 
 	 < / O C A N S u b m i s s i o n R e c o r d > 
 
 
 
 	 < ! - -   a s s e s s m e n t   r e c o r d #   9 :   j u s t   a n o t h e r   a s s e s s m e n t   - - > 
 
 	 < O C A N S u b m i s s i o n R e c o r d   a s s e s s m e n t I D = " 1 0 0 9 "   s t a r t D a t e = " 2 0 0 9 - 0 1 - 1 1 Z "   c o m p l e t i o n D a t e = " 2 0 0 9 - 0 1 - 1 6 Z "   a s s e s s m e n t S t a t u s = " C o m p l e t e " > 
 
 	 	 < o r g a n i z a t i o n R e c o r d > 
 
 	 	 	 < s e r v i c e O r g   n a m e = " N E   L H I N   T e s t   O r g "   n u m b e r = " 1 2 3 " / > 
 
 	 	 	 < p r o g r a m   n a m e = " I n t e n s i v e   C a s e   M a n a g e m e n t "   n u m b e r = " 3 0 2 5 " / > 
 
 	 	 	 < M I S F u n c t i o n   v a l u e = " 7 2 5   5 1   7 6   2 0 " / > 
 
 	 	 < / o r g a n i z a t i o n R e c o r d > 
 
 	 	 < c l i e n t R e c o r d > 
 
 	 	 	 < c l i e n t I D   o r g C l i e n t I D = " 1 2 3 9 0 0 " / > 
 
 	 	 	 < ! - -   B e g i n   P H I   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < c l i e n t N a m e   l a s t = " "   f i r s t = " " / > 
 
 	 	 	 < c l i e n t A d d r e s s   l i n e 1 = " "   l i n e 2 = " "   c i t y = " "   p r o v i n c e = " "   p o s t a l C o d e = " " / > 
 
 	 	 	 < c l i e n t P h o n e / > 
 
 	 	 	 < c l i e n t O H I P   n u m b e r = " "   v e r s i o n = " " / > 
 
 	 	 	 < c l i e n t C u l t u r e / > 
 
 	 	 	 < ! - -   E n d   P H I   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < r e a s o n F o r A s s e s s m e n t   v a l u e = " I A "   o t h e r = " " / > 
 
 	 	 	 < c l i e n t C o n t a c t > 
 
 	 	 	 	 < d o c t o r C o n t a c t   d o c t o r = " T R U E "   l a s t S e e n = " L S - 6 " / > 
 
 	 	 	 	 < p s y c h i a t r i s t C o n t a c t   p s y c h i a t r i s t = " T R U E "   l a s t S e e n = " L S - 1 2 " / > 
 
 	 	 	 	 < o t h e r P r a c t i t i o n e r C o n t a c t   p r a c t i t i o n e r T y p e = " 3 1 1 1 "   l a s t S e e n = " L S - 1 " / > 
 
 	 	 	 	 < o t h e r P r a c t i t i o n e r C o n t a c t   p r a c t i t i o n e r T y p e = " 3 1 1 2 "   l a s t S e e n = " L S - 1 3 " / > 
 
 	 	 	 	 < o t h e r P r a c t i t i o n e r C o n t a c t   p r a c t i t i o n e r T y p e = " 4 2 1 7 "   l a s t S e e n = " L S - 6 " / > 
 
 	 	 	 	 < o t h e r A g e n c y C o n t a c t   l a s t S e e n = " L S - 1 2 " / > 
 
 	 	 	 < / c l i e n t C o n t a c t > 
 
 	 	 	 < s e r v i c e R e c i p i e n t L o c a t i o n > 0 1 0 - 5 2 < / s e r v i c e R e c i p i e n t L o c a t i o n > 
 
 	 	 	 < s e r v i c e R e c i p i e n t L H I N > 1 2 < / s e r v i c e R e c i p i e n t L H I N > 
 
 	 	 	 < s e r v i c e D e l i v e r y L H I N > 5 < / s e r v i c e D e l i v e r y L H I N > 
 
 	 	 	 < c l i e n t D O B > 1 9 7 7 - 1 0 - 2 2 Z < / c l i e n t D O B > 
 
 	 	 	 < g e n d e r > U N K < / g e n d e r > 
 
 	 	 	 < m a r i t a l S t a t u s > 1 2 5 6 8 1 0 0 6 < / m a r i t a l S t a t u s > 
 
 	 	 	 < c l i e n t C a p a c i t y   p r o p e r t y = " T R U E "   p e r s o n a l C a r e = " F A L S E "   l e g a l G u a r d i a n = " C D A " / > 
 
 	 	 	 < r e f e r r a l S o u r c e > 0 1 8 - 0 5 < / r e f e r r a l S o u r c e > 
 
 	 	 	 < a b o r i g i n a l O r i g i n > 0 1 1 - 0 2 < / a b o r i g i n a l O r i g i n > 
 
 	 	 	 < c i t i z e n s h i p S t a t u s > C D N < / c i t i z e n s h i p S t a t u s > 
 
 	 	 	 < t i m e L i v e d I n C a n a d a   y e a r s = " 3 "   m o n t h s = " 8 " / > 
 
 	 	 	 < i m m i g E x p L i s t   o t h e r I m m i g E x p = " o t h e r   i m m i g r a t i o n   e x p e r i e n c e   t e x t " > 
 
 	 	 	 	 < v a l u e > 2 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 3 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 7 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 8 < / v a l u e > 
 
 	 	 	 < / i m m i g E x p L i s t > 
 
 	 	 	 < d i s c r i m E x p L i s t   o t h e r D i s c r i m E x p = " o t h e r   d i s c r i m i n a t i o n   e x p e r i e n c e   t e x t " > 
 
 	 	 	 	 < v a l u e > 2 1 1 3 4 0 0 2 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 3 6 5 8 7 3 0 0 7 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 4 1 0 5 1 5 0 0 3 < / v a l u e > 
 
 	 	 	 < / d i s c r i m E x p L i s t > 
 
 	 	 	 < p r e f L a n g > f r a < / p r e f L a n g > 
 
 	 	 	 < s e r v i c e L a n g > e n g < / s e r v i c e L a n g > 
 
 	 	 	 < l e g a l I s s u e s > C i v i l < / l e g a l I s s u e s > 
 
 	 	 	 < l e g a l S t a t u s L i s t > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 1 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 2 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 3 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 8 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 9 < / l e g a l S t a t u s > 
 
 	 	 	 < / l e g a l S t a t u s L i s t > 
 
 	 	 	 < e x i t D i s p o s i t i o n > 0 1 9 - 0 6 < / e x i t D i s p o s i t i o n > 
 
 	 	 < / c l i e n t R e c o r d > 
 
 	 	 < O C A N D o m a i n s > 
 
 	 	 	 < d o m a i n   n a m e = " 0 1 - a c c o m m o d a t i o n " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 	 < r e s i d e n c e T y p e > 0 2 4 - 1 9 < / r e s i d e n c e T y p e > 
 
 	 	 	 	 < r e s i d e n c e S u p p o r t > 2 4 A - 0 5 < / r e s i d e n c e S u p p o r t > 
 
 	 	 	 	 < l i v i n g A r r a n g e m e n t T y p e > 0 2 3 - 0 8 < / l i v i n g A r r a n g e m e n t T y p e > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 2 - f o o d " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 3 - l o o k i n g   a f t e r   t h e   h o m e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 4 - s e l f - c a r e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 5 - d a y t i m e   a c t i v i t i e s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 3 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 	 < e m p l o y S t a t u s > 2 2 4 3 6 6 0 0 4 < / e m p l o y S t a t u s > 
 
 	 	 	 	 < e d u c a t i o n P r o g r a m S t a t u s > 2 2 4 8 7 0 0 0 1 < / e d u c a t i o n P r o g r a m S t a t u s > 
 
 	 	 	 	 < r i s k U n e m p l o y m e n t L i s t > 
 
 	 	 	 	 	 < r i s k U n e m p l o y m e n t > U D E R - 0 2 < / r i s k U n e m p l o y m e n t > 
 
 	 	 	 	 	 < r i s k U n e m p l o y m e n t > U D E R - 0 3 < / r i s k U n e m p l o y m e n t > 
 
 	 	 	 	 	 < r i s k U n e m p l o y m e n t > U D E R - 0 6 < / r i s k U n e m p l o y m e n t > 
 
 	 	 	 	 < / r i s k U n e m p l o y m e n t L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 6 - p h y s i c a l   h e a l t h " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < m e d i c a l C o n d i t i o n L i s t   a u t i s m D e t a i l = " a u t i s m   s p e c i f i c   t e x t "   o t h e r D e t a i l = " o t h e r   m e d i c a l   t e x t " > 
 
 	 	 	 	 	 < m e d i c a l C o n d i t i o n > 4 0 8 8 5 6 0 0 3 < / m e d i c a l C o n d i t i o n > 
 
 	 	 	 	 	 < m e d i c a l C o n d i t i o n > 4 1 0 5 1 5 0 0 3 < / m e d i c a l C o n d i t i o n > 
 
 	 	 	 	 	 < m e d i c a l C o n d i t i o n > 4 1 4 9 1 6 0 0 1 < / m e d i c a l C o n d i t i o n > 
 
 	 	 	 	 < / m e d i c a l C o n d i t i o n L i s t > 
 
 	 	 	 	 < p h y s i c a l H e a l t h C o n c e r n > T R U E < / p h y s i c a l H e a l t h C o n c e r n > 
 
 	 	 	 	 < c o n c e r n A r e a L i s t   o t h e r C o n c e r n A r e a = " o t h e r   c o n c e r n   t e x t " > 
 
 	 	 	 	 	 < c o n c e r n A r e a > 1 1 9 4 1 5 0 0 7 < / c o n c e r n A r e a > 
 
 	 	 	 	 	 < c o n c e r n A r e a > 1 1 8 9 5 2 0 0 5 < / c o n c e r n A r e a > 
 
 	 	 	 	 	 < c o n c e r n A r e a > 4 1 0 5 1 5 0 0 3 < / c o n c e r n A r e a > 
 
 	 	 	 	 < / c o n c e r n A r e a L i s t > 
 
 	 	 	 	 < m e d i c a t i o n L i s t > 
 
 	 	 	 	 	 < m e d i c a t i o n D e t a i l   t a k e n A s P r e s c r i b e d = " T R U E "   i s H e l p P r o v i d e d = " F A L S E "   i s H e l p N e e d e d = " T R U E " / > 
 
 	 	 	 	 	 < m e d i c a t i o n D e t a i l   t a k e n A s P r e s c r i b e d = " F A L S E "   i s H e l p P r o v i d e d = " U N K "   i s H e l p N e e d e d = " T R U E " / > 
 
 	 	 	 	 	 < m e d i c a t i o n D e t a i l   t a k e n A s P r e s c r i b e d = " U N K "   i s H e l p P r o v i d e d = " T R U E "   i s H e l p N e e d e d = " F A L S E " / > 
 
 	 	 	 	 < / m e d i c a t i o n L i s t > 
 
 	 	 	 	 < s i d e E f f e c t s > T R U E < / s i d e E f f e c t s > 
 
 	 	 	 	 < d a i l y L i v i n g A f f e c t e d > T R U E < / d a i l y L i v i n g A f f e c t e d > 
 
 	 	 	 	 < s i d e E f f e c t s D e t a i l L i s t   o t h e r S i d e E f f e c t s D e t a i l = " o t h e r   s i d e   e f f e c t   t e x t " > 
 
 	 	 	 	 	 < s i d e E f f e c t s D e t a i l > 2 4 6 6 3 6 0 0 8 < / s i d e E f f e c t s D e t a i l > 
 
 	 	 	 	 	 < s i d e E f f e c t s D e t a i l > 5 3 6 1 9 0 0 0 < / s i d e E f f e c t s D e t a i l > 
 
 	 	 	 	 	 < s i d e E f f e c t s D e t a i l > 4 1 0 5 1 5 0 0 3 < / s i d e E f f e c t s D e t a i l > 
 
 	 	 	 	 < / s i d e E f f e c t s D e t a i l L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 7 - p s y c h o t i c   s y m p t o m s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 	 < h o s p i t a l i z e d P a s t T w o Y e a r s > T R U E < / h o s p i t a l i z e d P a s t T w o Y e a r s > 
 
 	 	 	 	 < t o t a l A d m i s s i o n s > 5 < / t o t a l A d m i s s i o n s > 
 
 	 	 	 	 < t o t a l H o s p i t a l D a y s > 7 8 < / t o t a l H o s p i t a l D a y s > 
 
 	 	 	 	 < c o m m u n i t y T r e a t O r d e r > 0 1 5 - 0 3 < / c o m m u n i t y T r e a t O r d e r > 
 
 	 	 	 	 < s y m p t o m L i s t   o t h e r S y m p t o m = " o t h e r   s y m p t o m   t e x t " > 
 
 	 	 	 	 	 < s y m p t o m > 4 1 0 5 1 6 0 0 2 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 7 5 4 0 8 0 0 8 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 2 0 7 3 0 0 0 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 1 4 0 2 0 0 1 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 5 5 9 2 9 0 0 7 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 4 1 0 5 1 5 0 0 3 < / s y m p t o m > 
 
 	 	 	 	 < / s y m p t o m L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 8 - c o n d i t i o n   a n d   t r e a t m e n t " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 	 < d i a g n o s t i c L i s t > 
 
 	 	 	 	 	 < d i a g n o s t i c > 2 7 7 6 0 0 0 < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > M D G M C < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > 8 7 8 5 8 0 0 2 < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > S R D D H < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > D H < / d i a g n o s t i c > 
 
 	 	 	 	 < / d i a g n o s t i c L i s t > 
 
 	 	 	 	 < o t h e r I l l n e s s L i s t > 
 
 	 	 	 	 	 < o t h e r I l l n e s s > 0 1 6 A - 0 1 < / o t h e r I l l n e s s > 
 
 	 	 	 	 	 < o t h e r I l l n e s s > 0 1 6 A - 0 3 < / o t h e r I l l n e s s > 
 
 	 	 	 	 < / o t h e r I l l n e s s L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 9 - p s y c h o l o g i c a l   d i s t r e s s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 0 - s a f e t y   t o   s e l f " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < s u i c i d e A t t e m p t > T R U E < / s u i c i d e A t t e m p t > 
 
 	 	 	 	 < s u i c i d e T h o u g h t s > T R U E < / s u i c i d e T h o u g h t s > 
 
 	 	 	 	 < s a f e t y C o n c e r n S e l f > T R U E < / s a f e t y C o n c e r n S e l f > 
 
 	 	 	 	 < s a f e t y T o S e l f R i s k L i s t   o t h e r S a f e t y T o S e l f R i s k = " o t h e r   r i s k   t o   s e l f   t e x t " > 
 
 	 	 	 	 	 < s a f e t y T o S e l f R i s k > 2 2 5 9 1 5 0 0 6 < / s a f e t y T o S e l f R i s k > 
 
 	 	 	 	 	 < s a f e t y T o S e l f R i s k > 4 0 1 2 0 6 0 0 8 < / s a f e t y T o S e l f R i s k > 
 
 	 	 	 	 	 < s a f e t y T o S e l f R i s k > 4 1 0 5 1 5 0 0 3 < / s a f e t y T o S e l f R i s k > 
 
 	 	 	 	 < / s a f e t y T o S e l f R i s k L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 1 - s a f e t y   t o   o t h e r s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 2 - a l c o h o l " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 	 < d r i n k A l c o h o l   q u a n t i t y = " 6 "   f r e q u e n c y = " 1 " / > 
 
 	 	 	 	 < s t a g e O f C h a n g e A l c o h o l > 5 < / s t a g e O f C h a n g e A l c o h o l > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 3 - d r u g s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 2 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 	 < d r u g U s e L i s t   i n j e c t e d = " 6 " > 
 
 	 	 	 	 	 < d r u g U s e   n a m e = " 3 9 8 7 0 5 0 0 4 "   f r e q u e n c y = " 5 " / > 
 
 	 	 	 	 	 < d r u g U s e   n a m e = " 2 2 6 0 4 4 0 0 4 "   f r e q u e n c y = " 6 " / > 
 
 	 	 	 	 	 < d r u g U s e   n a m e = " 4 1 0 5 1 5 0 0 3 "   f r e q u e n c y = " 5 " / > 
 
 	 	 	 	 < / d r u g U s e L i s t > 
 
 	 	 	 	 < s t a g e O f C h a n g e D r u g s > 2 < / s t a g e O f C h a n g e D r u g s > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 4 - o t h e r   a d d i c t i o n s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 	 < a d d i c t i o n T y p e L i s t   o t h e r A d d i c t i o n T y p e = " o t h e r   a d d i c t i o n   t e x t " > 
 
 	 	 	 	 	 < a d d i c t i o n T y p e > 1 0 5 5 2 3 0 0 9 < / a d d i c t i o n T y p e > 
 
 	 	 	 	 	 < a d d i c t i o n T y p e > 5 6 2 9 4 0 0 8 < / a d d i c t i o n T y p e > 
 
 	 	 	 	 	 < a d d i c t i o n T y p e > 4 1 0 5 1 5 0 0 3 < / a d d i c t i o n T y p e > 
 
 	 	 	 	 < / a d d i c t i o n T y p e L i s t > 
 
 	 	 	 	 < s t a g e O f C h a n g e A d d i c t i o n s > 3 < / s t a g e O f C h a n g e A d d i c t i o n s > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 5 - c o m p a n y " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 2 " / > 
 
 	 	 	 	 < c h a n g e d S o c i a l P a t t e r n s > C D A < / c h a n g e d S o c i a l P a t t e r n s > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 6 - i n t i m a t e   r e l a t i o n s h i p s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 7 - s e x u a l   e x p r e s s i o n " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 8 - c h i l d c a r e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 9 - o t h e r   d e p e n d e n t s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 0 - b a s i c   e d u c a t i o n " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < h i g h e s t E d u c a t i o n L e v e l > H L E S - 6 < / h i g h e s t E d u c a t i o n L e v e l > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 1 - t e l e p h o n e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 2 - t r a n s p o r t " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 3 - m o n e y " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 	 < s o u r c e O f I n c o m e > 0 3 1 - 1 0 < / s o u r c e O f I n c o m e > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 4 - b e n e f i t s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 < / O C A N D o m a i n s > 
 
 	 	 < a d d i t i o n a l E l e m e n t s > 
 
 	 	 	 < ! - -   B e g i n   F r e e   T e x t   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < c l i e n t H o p e s F o r F u t u r e / > 
 
 	 	 	 < c l i e n t N e e d T o G e t T h e r e / > 
 
 	 	 	 < c l i e n t V i e w M e n t a l H e a l t h / > 
 
 	 	 	 < c l i e n t S p i r i t u a l i t y I m p o r t a n c e / > 
 
 	 	 	 < c l i e n t C u l t u r e H e r i t a g e I m p o r t a n c e / > 
 
 	 	 	 < ! - -   E n d   F r e e   T e x t   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < p r e s e n t i n g I s s u e L i s t > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 1 " / > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 3 " / > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 5 " / > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 8 " / > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 9 " / > 
 
 	 	 	 < / p r e s e n t i n g I s s u e L i s t > 
 
 	 	 	 < a c t i o n L i s t > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 1 "   d o m a i n = " 1 3 - d r u g s " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 2 "   d o m a i n = " 1 5 - c o m p a n y " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 3 "   d o m a i n = " 2 0 - b a s i c   e d u c a t i o n " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 4 "   d o m a i n = " 2 3 - m o n e y " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 5 "   d o m a i n = " 1 0 - s a f e t y   t o   s e l f " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 6 "   d o m a i n = " 2 4 - b e n e f i t s " / > 
 
 	 	 	 < / a c t i o n L i s t > 
 
 	 	 	 < r e f e r r a l L i s t > 
 
 	 	 	 	 < r e f e r r a l   o p t i m a l = " 0 A S "   s p e c i f y O p t i m a l = " s p e c i f y   t e x t   f o r   o p t i m a l   r e f e r r a l "   a c t u a l = " F O R "   s p e c i f y A c t u a l = " s p e c i f y   t e x t   f o r   a c t u a l   r e f e r r a l "   d i f f e r e n c e R e a s o n = " R D - 4 "   s t a t u s = " R S - 1 " / > 
 
 	 	 	 	 < r e f e r r a l   o p t i m a l = " 0 A B "   s p e c i f y O p t i m a l = " s p e c i f y   t e x t   f o r   o p t i m a l   r e f e r r a l "   a c t u a l = " H P A "   s p e c i f y A c t u a l = " s p e c i f y   t e x t   f o r   a c t u a l   r e f e r r a l "   d i f f e r e n c e R e a s o n = " R D - 2 "   s t a t u s = " R S - 2 " / > 
 
 	 	 	 	 < r e f e r r a l   o p t i m a l = " E A T "   s p e c i f y O p t i m a l = " s p e c i f y   t e x t   f o r   o p t i m a l   r e f e r r a l "   a c t u a l = " H S C "   s p e c i f y A c t u a l = " s p e c i f y   t e x t   f o r   a c t u a l   r e f e r r a l "   d i f f e r e n c e R e a s o n = " R D - 3 "   s t a t u s = " R S - 1 " / > 
 
 	 	 	 	 < r e f e r r a l   o p t i m a l = " 0 F I "   s p e c i f y O p t i m a l = " s p e c i f y   t e x t   f o r   o p t i m a l   r e f e r r a l "   a c t u a l = " 0 S R "   s p e c i f y A c t u a l = " s p e c i f y   t e x t   f o r   a c t u a l   r e f e r r a l "   d i f f e r e n c e R e a s o n = " R D - 1 "   s t a t u s = " R S - 2 " / > 
 
 	 	 	 < / r e f e r r a l L i s t > 
 
 	 	 < / a d d i t i o n a l E l e m e n t s > 
 
 	 < / O C A N S u b m i s s i o n R e c o r d > 
 
 
 
 	 < ! - -   a s s e s s m e n t   r e c o r d #   1 0 :   j u s t   a n o t h e r   a s s e s s m e n t   - - > 
 
 	 < O C A N S u b m i s s i o n R e c o r d   a s s e s s m e n t I D = " 1 0 1 0 "   s t a r t D a t e = " 2 0 0 9 - 0 1 - 1 1 Z "   c o m p l e t i o n D a t e = " 2 0 0 9 - 0 1 - 1 6 Z "   a s s e s s m e n t S t a t u s = " C o m p l e t e " > 
 
 	 	 < o r g a n i z a t i o n R e c o r d > 
 
 	 	 	 < s e r v i c e O r g   n a m e = " N E   L H I N   T e s t   O r g "   n u m b e r = " 1 2 3 " / > 
 
 	 	 	 < p r o g r a m   n a m e = " I n t e n s i v e   C a s e   M a n a g e m e n t "   n u m b e r = " 3 0 2 5 " / > 
 
 	 	 	 < M I S F u n c t i o n   v a l u e = " 7 2 5   5 1   7 6   2 0 " / > 
 
 	 	 < / o r g a n i z a t i o n R e c o r d > 
 
 	 	 < c l i e n t R e c o r d > 
 
 	 	 	 < c l i e n t I D   o r g C l i e n t I D = " 1 2 3 1 5 0 " / > 
 
 	 	 	 < ! - -   B e g i n   P H I   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < c l i e n t N a m e   l a s t = " "   f i r s t = " " / > 
 
 	 	 	 < c l i e n t A d d r e s s   l i n e 1 = " "   l i n e 2 = " "   c i t y = " "   p r o v i n c e = " "   p o s t a l C o d e = " " / > 
 
 	 	 	 < c l i e n t P h o n e / > 
 
 	 	 	 < c l i e n t O H I P   n u m b e r = " "   v e r s i o n = " " / > 
 
 	 	 	 < c l i e n t C u l t u r e / > 
 
 	 	 	 < ! - -   E n d   P H I   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < r e a s o n F o r A s s e s s m e n t   v a l u e = " I A "   o t h e r = " " / > 
 
 	 	 	 < c l i e n t C o n t a c t > 
 
 	 	 	 	 < d o c t o r C o n t a c t   d o c t o r = " T R U E "   l a s t S e e n = " L S - 6 " / > 
 
 	 	 	 	 < p s y c h i a t r i s t C o n t a c t   p s y c h i a t r i s t = " T R U E "   l a s t S e e n = " L S - 1 2 " / > 
 
 	 	 	 	 < o t h e r P r a c t i t i o n e r C o n t a c t   p r a c t i t i o n e r T y p e = " 3 1 1 1 "   l a s t S e e n = " L S - 1 " / > 
 
 	 	 	 	 < o t h e r P r a c t i t i o n e r C o n t a c t   p r a c t i t i o n e r T y p e = " 3 1 1 2 "   l a s t S e e n = " L S - 1 3 " / > 
 
 	 	 	 	 < o t h e r P r a c t i t i o n e r C o n t a c t   p r a c t i t i o n e r T y p e = " 4 2 1 7 "   l a s t S e e n = " L S - 6 " / > 
 
 	 	 	 	 < o t h e r A g e n c y C o n t a c t   l a s t S e e n = " L S - 1 2 " / > 
 
 	 	 	 < / c l i e n t C o n t a c t > 
 
 	 	 	 < s e r v i c e R e c i p i e n t L o c a t i o n > 0 1 0 - 5 2 < / s e r v i c e R e c i p i e n t L o c a t i o n > 
 
 	 	 	 < s e r v i c e R e c i p i e n t L H I N > 1 2 < / s e r v i c e R e c i p i e n t L H I N > 
 
 	 	 	 < s e r v i c e D e l i v e r y L H I N > 5 < / s e r v i c e D e l i v e r y L H I N > 
 
 	 	 	 < c l i e n t D O B > 1 9 5 5 - 0 8 - 0 9 Z < / c l i e n t D O B > 
 
 	 	 	 < g e n d e r > F < / g e n d e r > 
 
 	 	 	 < m a r i t a l S t a t u s > 1 2 5 6 8 1 0 0 6 < / m a r i t a l S t a t u s > 
 
 	 	 	 < c l i e n t C a p a c i t y   p r o p e r t y = " T R U E "   p e r s o n a l C a r e = " F A L S E "   l e g a l G u a r d i a n = " C D A " / > 
 
 	 	 	 < r e f e r r a l S o u r c e > 0 1 8 - 0 5 < / r e f e r r a l S o u r c e > 
 
 	 	 	 < a b o r i g i n a l O r i g i n > 0 1 1 - 0 2 < / a b o r i g i n a l O r i g i n > 
 
 	 	 	 < c i t i z e n s h i p S t a t u s > C D A < / c i t i z e n s h i p S t a t u s > 
 
 	 	 	 < t i m e L i v e d I n C a n a d a   y e a r s = " 3 "   m o n t h s = " 8 " / > 
 
 	 	 	 < i m m i g E x p L i s t   o t h e r I m m i g E x p = " o t h e r   i m m i g r a t i o n   e x p e r i e n c e   t e x t " > 
 
 	 	 	 	 < v a l u e > 2 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 3 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 7 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 8 < / v a l u e > 
 
 	 	 	 < / i m m i g E x p L i s t > 
 
 	 	 	 < d i s c r i m E x p L i s t   o t h e r D i s c r i m E x p = " o t h e r   d i s c r i m i n a t i o n   e x p e r i e n c e   t e x t " > 
 
 	 	 	 	 < v a l u e > 2 1 1 3 4 0 0 2 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 3 6 5 8 7 3 0 0 7 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 4 1 0 5 1 5 0 0 3 < / v a l u e > 
 
 	 	 	 < / d i s c r i m E x p L i s t > 
 
 	 	 	 < p r e f L a n g > f r a < / p r e f L a n g > 
 
 	 	 	 < s e r v i c e L a n g > e n g < / s e r v i c e L a n g > 
 
 	 	 	 < l e g a l I s s u e s > C i v i l < / l e g a l I s s u e s > 
 
 	 	 	 < l e g a l S t a t u s L i s t > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 1 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 2 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 3 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 8 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 9 < / l e g a l S t a t u s > 
 
 	 	 	 < / l e g a l S t a t u s L i s t > 
 
 	 	 	 < e x i t D i s p o s i t i o n > 0 1 9 - 0 6 < / e x i t D i s p o s i t i o n > 
 
 	 	 < / c l i e n t R e c o r d > 
 
 	 	 < O C A N D o m a i n s > 
 
 	 	 	 < d o m a i n   n a m e = " 0 1 - a c c o m m o d a t i o n " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 	 < r e s i d e n c e T y p e > 0 2 4 - 1 9 < / r e s i d e n c e T y p e > 
 
 	 	 	 	 < r e s i d e n c e S u p p o r t > 2 4 A - 0 5 < / r e s i d e n c e S u p p o r t > 
 
 	 	 	 	 < l i v i n g A r r a n g e m e n t T y p e > 0 2 3 - 0 8 < / l i v i n g A r r a n g e m e n t T y p e > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 2 - f o o d " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 3 - l o o k i n g   a f t e r   t h e   h o m e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 4 - s e l f - c a r e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 5 - d a y t i m e   a c t i v i t i e s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 3 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 	 < e m p l o y S t a t u s > 2 2 4 3 6 6 0 0 4 < / e m p l o y S t a t u s > 
 
 	 	 	 	 < e d u c a t i o n P r o g r a m S t a t u s > 2 2 4 8 7 0 0 0 1 < / e d u c a t i o n P r o g r a m S t a t u s > 
 
 	 	 	 	 < r i s k U n e m p l o y m e n t L i s t > 
 
 	 	 	 	 	 < r i s k U n e m p l o y m e n t > U D E R - 0 2 < / r i s k U n e m p l o y m e n t > 
 
 	 	 	 	 	 < r i s k U n e m p l o y m e n t > U D E R - 0 3 < / r i s k U n e m p l o y m e n t > 
 
 	 	 	 	 	 < r i s k U n e m p l o y m e n t > U D E R - 0 6 < / r i s k U n e m p l o y m e n t > 
 
 	 	 	 	 < / r i s k U n e m p l o y m e n t L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 6 - p h y s i c a l   h e a l t h " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < m e d i c a l C o n d i t i o n L i s t   a u t i s m D e t a i l = " a u t i s m   s p e c i f i c   t e x t "   o t h e r D e t a i l = " o t h e r   m e d i c a l   t e x t " > 
 
 	 	 	 	 	 < m e d i c a l C o n d i t i o n > 4 0 8 8 5 6 0 0 3 < / m e d i c a l C o n d i t i o n > 
 
 	 	 	 	 	 < m e d i c a l C o n d i t i o n > 4 1 0 5 1 5 0 0 3 < / m e d i c a l C o n d i t i o n > 
 
 	 	 	 	 	 < m e d i c a l C o n d i t i o n > 4 1 4 9 1 6 0 0 1 < / m e d i c a l C o n d i t i o n > 
 
 	 	 	 	 < / m e d i c a l C o n d i t i o n L i s t > 
 
 	 	 	 	 < p h y s i c a l H e a l t h C o n c e r n > T R U E < / p h y s i c a l H e a l t h C o n c e r n > 
 
 	 	 	 	 < c o n c e r n A r e a L i s t   o t h e r C o n c e r n A r e a = " o t h e r   c o n c e r n   t e x t " > 
 
 	 	 	 	 	 < c o n c e r n A r e a > 1 1 9 4 1 5 0 0 7 < / c o n c e r n A r e a > 
 
 	 	 	 	 	 < c o n c e r n A r e a > 1 1 8 9 5 2 0 0 5 < / c o n c e r n A r e a > 
 
 	 	 	 	 	 < c o n c e r n A r e a > 4 1 0 5 1 5 0 0 3 < / c o n c e r n A r e a > 
 
 	 	 	 	 < / c o n c e r n A r e a L i s t > 
 
 	 	 	 	 < m e d i c a t i o n L i s t > 
 
 	 	 	 	 	 < m e d i c a t i o n D e t a i l   t a k e n A s P r e s c r i b e d = " T R U E "   i s H e l p P r o v i d e d = " F A L S E "   i s H e l p N e e d e d = " T R U E " / > 
 
 	 	 	 	 	 < m e d i c a t i o n D e t a i l   t a k e n A s P r e s c r i b e d = " F A L S E "   i s H e l p P r o v i d e d = " U N K "   i s H e l p N e e d e d = " T R U E " / > 
 
 	 	 	 	 	 < m e d i c a t i o n D e t a i l   t a k e n A s P r e s c r i b e d = " U N K "   i s H e l p P r o v i d e d = " T R U E "   i s H e l p N e e d e d = " F A L S E " / > 
 
 	 	 	 	 < / m e d i c a t i o n L i s t > 
 
 	 	 	 	 < s i d e E f f e c t s > T R U E < / s i d e E f f e c t s > 
 
 	 	 	 	 < d a i l y L i v i n g A f f e c t e d > T R U E < / d a i l y L i v i n g A f f e c t e d > 
 
 	 	 	 	 < s i d e E f f e c t s D e t a i l L i s t   o t h e r S i d e E f f e c t s D e t a i l = " o t h e r   s i d e   e f f e c t   t e x t " > 
 
 	 	 	 	 	 < s i d e E f f e c t s D e t a i l > 2 4 6 6 3 6 0 0 8 < / s i d e E f f e c t s D e t a i l > 
 
 	 	 	 	 	 < s i d e E f f e c t s D e t a i l > 5 3 6 1 9 0 0 0 < / s i d e E f f e c t s D e t a i l > 
 
 	 	 	 	 	 < s i d e E f f e c t s D e t a i l > 4 1 0 5 1 5 0 0 3 < / s i d e E f f e c t s D e t a i l > 
 
 	 	 	 	 < / s i d e E f f e c t s D e t a i l L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 7 - p s y c h o t i c   s y m p t o m s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 	 < h o s p i t a l i z e d P a s t T w o Y e a r s > T R U E < / h o s p i t a l i z e d P a s t T w o Y e a r s > 
 
 	 	 	 	 < t o t a l A d m i s s i o n s > 5 < / t o t a l A d m i s s i o n s > 
 
 	 	 	 	 < t o t a l H o s p i t a l D a y s > 7 8 < / t o t a l H o s p i t a l D a y s > 
 
 	 	 	 	 < c o m m u n i t y T r e a t O r d e r > 0 1 5 - 0 3 < / c o m m u n i t y T r e a t O r d e r > 
 
 	 	 	 	 < s y m p t o m L i s t   o t h e r S y m p t o m = " o t h e r   s y m p t o m   t e x t " > 
 
 	 	 	 	 	 < s y m p t o m > 4 1 0 5 1 6 0 0 2 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 7 5 4 0 8 0 0 8 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 2 0 7 3 0 0 0 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 1 4 0 2 0 0 1 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 5 5 9 2 9 0 0 7 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 4 1 0 5 1 5 0 0 3 < / s y m p t o m > 
 
 	 	 	 	 < / s y m p t o m L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 8 - c o n d i t i o n   a n d   t r e a t m e n t " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 	 < d i a g n o s t i c L i s t > 
 
 	 	 	 	 	 < d i a g n o s t i c > 2 7 7 6 0 0 0 < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > M D G M C < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > 8 7 8 5 8 0 0 2 < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > S R D D H < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > D H < / d i a g n o s t i c > 
 
 	 	 	 	 < / d i a g n o s t i c L i s t > 
 
 	 	 	 	 < o t h e r I l l n e s s L i s t > 
 
 	 	 	 	 	 < o t h e r I l l n e s s > 0 1 6 A - 0 1 < / o t h e r I l l n e s s > 
 
 	 	 	 	 	 < o t h e r I l l n e s s > 0 1 6 A - 0 3 < / o t h e r I l l n e s s > 
 
 	 	 	 	 < / o t h e r I l l n e s s L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 9 - p s y c h o l o g i c a l   d i s t r e s s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 0 - s a f e t y   t o   s e l f " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < s u i c i d e A t t e m p t > T R U E < / s u i c i d e A t t e m p t > 
 
 	 	 	 	 < s u i c i d e T h o u g h t s > T R U E < / s u i c i d e T h o u g h t s > 
 
 	 	 	 	 < s a f e t y C o n c e r n S e l f > T R U E < / s a f e t y C o n c e r n S e l f > 
 
 	 	 	 	 < s a f e t y T o S e l f R i s k L i s t   o t h e r S a f e t y T o S e l f R i s k = " o t h e r   r i s k   t o   s e l f   t e x t " > 
 
 	 	 	 	 	 < s a f e t y T o S e l f R i s k > 2 2 5 9 1 5 0 0 6 < / s a f e t y T o S e l f R i s k > 
 
 	 	 	 	 	 < s a f e t y T o S e l f R i s k > 4 0 1 2 0 6 0 0 8 < / s a f e t y T o S e l f R i s k > 
 
 	 	 	 	 	 < s a f e t y T o S e l f R i s k > 4 1 0 5 1 5 0 0 3 < / s a f e t y T o S e l f R i s k > 
 
 	 	 	 	 < / s a f e t y T o S e l f R i s k L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 1 - s a f e t y   t o   o t h e r s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 2 - a l c o h o l " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 	 < d r i n k A l c o h o l   q u a n t i t y = " 6 "   f r e q u e n c y = " 1 " / > 
 
 	 	 	 	 < s t a g e O f C h a n g e A l c o h o l > 5 < / s t a g e O f C h a n g e A l c o h o l > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 3 - d r u g s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 2 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 	 < d r u g U s e L i s t   i n j e c t e d = " 6 " > 
 
 	 	 	 	 	 < d r u g U s e   n a m e = " 3 9 8 7 0 5 0 0 4 "   f r e q u e n c y = " 5 " / > 
 
 	 	 	 	 	 < d r u g U s e   n a m e = " 2 2 6 0 4 4 0 0 4 "   f r e q u e n c y = " 6 " / > 
 
 	 	 	 	 	 < d r u g U s e   n a m e = " 4 1 0 5 1 5 0 0 3 "   f r e q u e n c y = " 5 " / > 
 
 	 	 	 	 < / d r u g U s e L i s t > 
 
 	 	 	 	 < s t a g e O f C h a n g e D r u g s > 2 < / s t a g e O f C h a n g e D r u g s > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 4 - o t h e r   a d d i c t i o n s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 	 < a d d i c t i o n T y p e L i s t   o t h e r A d d i c t i o n T y p e = " o t h e r   a d d i c t i o n   t e x t " > 
 
 	 	 	 	 	 < a d d i c t i o n T y p e > 1 0 5 5 2 3 0 0 9 < / a d d i c t i o n T y p e > 
 
 	 	 	 	 	 < a d d i c t i o n T y p e > 5 6 2 9 4 0 0 8 < / a d d i c t i o n T y p e > 
 
 	 	 	 	 	 < a d d i c t i o n T y p e > 4 1 0 5 1 5 0 0 3 < / a d d i c t i o n T y p e > 
 
 	 	 	 	 < / a d d i c t i o n T y p e L i s t > 
 
 	 	 	 	 < s t a g e O f C h a n g e A d d i c t i o n s > 3 < / s t a g e O f C h a n g e A d d i c t i o n s > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 5 - c o m p a n y " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 2 " / > 
 
 	 	 	 	 < c h a n g e d S o c i a l P a t t e r n s > C D A < / c h a n g e d S o c i a l P a t t e r n s > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 6 - i n t i m a t e   r e l a t i o n s h i p s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 7 - s e x u a l   e x p r e s s i o n " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 8 - c h i l d c a r e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 9 - o t h e r   d e p e n d e n t s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 0 - b a s i c   e d u c a t i o n " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < h i g h e s t E d u c a t i o n L e v e l > H L E S - 6 < / h i g h e s t E d u c a t i o n L e v e l > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 1 - t e l e p h o n e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 2 - t r a n s p o r t " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 3 - m o n e y " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 	 < s o u r c e O f I n c o m e > 0 3 1 - 1 0 < / s o u r c e O f I n c o m e > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 4 - b e n e f i t s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 < / O C A N D o m a i n s > 
 
 	 	 < a d d i t i o n a l E l e m e n t s > 
 
 	 	 	 < ! - -   B e g i n   F r e e   T e x t   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < c l i e n t H o p e s F o r F u t u r e / > 
 
 	 	 	 < c l i e n t N e e d T o G e t T h e r e / > 
 
 	 	 	 < c l i e n t V i e w M e n t a l H e a l t h / > 
 
 	 	 	 < c l i e n t S p i r i t u a l i t y I m p o r t a n c e / > 
 
 	 	 	 < c l i e n t C u l t u r e H e r i t a g e I m p o r t a n c e / > 
 
 	 	 	 < ! - -   E n d   F r e e   T e x t   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < p r e s e n t i n g I s s u e L i s t > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 1 " / > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 3 " / > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 5 " / > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 8 " / > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 9 " / > 
 
 	 	 	 < / p r e s e n t i n g I s s u e L i s t > 
 
 	 	 	 < a c t i o n L i s t > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 1 "   d o m a i n = " 1 3 - d r u g s " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 2 "   d o m a i n = " 1 5 - c o m p a n y " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 3 "   d o m a i n = " 2 0 - b a s i c   e d u c a t i o n " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 4 "   d o m a i n = " 2 3 - m o n e y " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 5 "   d o m a i n = " 1 0 - s a f e t y   t o   s e l f " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 6 "   d o m a i n = " 2 4 - b e n e f i t s " / > 
 
 	 	 	 < / a c t i o n L i s t > 
 
 	 	 	 < r e f e r r a l L i s t > 
 
 	 	 	 	 < r e f e r r a l   o p t i m a l = " 0 A S "   s p e c i f y O p t i m a l = " s p e c i f y   t e x t   f o r   o p t i m a l   r e f e r r a l "   a c t u a l = " F O R "   s p e c i f y A c t u a l = " s p e c i f y   t e x t   f o r   a c t u a l   r e f e r r a l "   d i f f e r e n c e R e a s o n = " R D - 4 "   s t a t u s = " R S - 1 " / > 
 
 	 	 	 	 < r e f e r r a l   o p t i m a l = " 0 A B "   s p e c i f y O p t i m a l = " s p e c i f y   t e x t   f o r   o p t i m a l   r e f e r r a l "   a c t u a l = " H P A "   s p e c i f y A c t u a l = " s p e c i f y   t e x t   f o r   a c t u a l   r e f e r r a l "   d i f f e r e n c e R e a s o n = " R D - 2 "   s t a t u s = " R S - 2 " / > 
 
 	 	 	 	 < r e f e r r a l   o p t i m a l = " E A T "   s p e c i f y O p t i m a l = " s p e c i f y   t e x t   f o r   o p t i m a l   r e f e r r a l "   a c t u a l = " H S C "   s p e c i f y A c t u a l = " s p e c i f y   t e x t   f o r   a c t u a l   r e f e r r a l "   d i f f e r e n c e R e a s o n = " R D - 3 "   s t a t u s = " R S - 1 " / > 
 
 	 	 	 	 < r e f e r r a l   o p t i m a l = " 0 F I "   s p e c i f y O p t i m a l = " s p e c i f y   t e x t   f o r   o p t i m a l   r e f e r r a l "   a c t u a l = " 0 S R "   s p e c i f y A c t u a l = " s p e c i f y   t e x t   f o r   a c t u a l   r e f e r r a l "   d i f f e r e n c e R e a s o n = " R D - 1 "   s t a t u s = " R S - 2 " / > 
 
 	 	 	 < / r e f e r r a l L i s t > 
 
 	 	 < / a d d i t i o n a l E l e m e n t s > 
 
 	 < / O C A N S u b m i s s i o n R e c o r d > 
 
 
 
 	 < ! - -   a s s e s s m e n t   r e c o r d #   1 1 :   j u s t   a n o t h e r   a s s e s s m e n t   - - > 
 
 	 < O C A N S u b m i s s i o n R e c o r d   a s s e s s m e n t I D = " 1 0 1 1 "   s t a r t D a t e = " 2 0 0 9 - 0 1 - 1 1 Z "   c o m p l e t i o n D a t e = " 2 0 0 9 - 0 1 - 1 6 Z "   a s s e s s m e n t S t a t u s = " C o m p l e t e " > 
 
 	 	 < o r g a n i z a t i o n R e c o r d > 
 
 	 	 	 < s e r v i c e O r g   n a m e = " N E   L H I N   T e s t   O r g "   n u m b e r = " 1 2 3 " / > 
 
 	 	 	 < p r o g r a m   n a m e = " I n t e n s i v e   C a s e   M a n a g e m e n t "   n u m b e r = " 3 0 2 5 " / > 
 
 	 	 	 < M I S F u n c t i o n   v a l u e = " 7 2 5   5 1   7 6   2 0 " / > 
 
 	 	 < / o r g a n i z a t i o n R e c o r d > 
 
 	 	 < c l i e n t R e c o r d > 
 
 	 	 	 < c l i e n t I D   o r g C l i e n t I D = " 1 2 3 2 5 0 " / > 
 
 	 	 	 < ! - -   B e g i n   P H I   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < c l i e n t N a m e   l a s t = " "   f i r s t = " " / > 
 
 	 	 	 < c l i e n t A d d r e s s   l i n e 1 = " "   l i n e 2 = " "   c i t y = " "   p r o v i n c e = " "   p o s t a l C o d e = " " / > 
 
 	 	 	 < c l i e n t P h o n e / > 
 
 	 	 	 < c l i e n t O H I P   n u m b e r = " "   v e r s i o n = " " / > 
 
 	 	 	 < c l i e n t C u l t u r e / > 
 
 	 	 	 < ! - -   E n d   P H I   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < r e a s o n F o r A s s e s s m e n t   v a l u e = " I A "   o t h e r = " " / > 
 
 	 	 	 < c l i e n t C o n t a c t > 
 
 	 	 	 	 < d o c t o r C o n t a c t   d o c t o r = " T R U E "   l a s t S e e n = " L S - 6 " / > 
 
 	 	 	 	 < p s y c h i a t r i s t C o n t a c t   p s y c h i a t r i s t = " T R U E "   l a s t S e e n = " L S - 1 2 " / > 
 
 	 	 	 	 < o t h e r P r a c t i t i o n e r C o n t a c t   p r a c t i t i o n e r T y p e = " 3 1 1 1 "   l a s t S e e n = " L S - 1 " / > 
 
 	 	 	 	 < o t h e r P r a c t i t i o n e r C o n t a c t   p r a c t i t i o n e r T y p e = " 3 1 1 2 "   l a s t S e e n = " L S - 1 3 " / > 
 
 	 	 	 	 < o t h e r P r a c t i t i o n e r C o n t a c t   p r a c t i t i o n e r T y p e = " 4 2 1 7 "   l a s t S e e n = " L S - 6 " / > 
 
 	 	 	 	 < o t h e r A g e n c y C o n t a c t   l a s t S e e n = " L S - 1 2 " / > 
 
 	 	 	 < / c l i e n t C o n t a c t > 
 
 	 	 	 < s e r v i c e R e c i p i e n t L o c a t i o n > 0 1 0 - 5 2 < / s e r v i c e R e c i p i e n t L o c a t i o n > 
 
 	 	 	 < s e r v i c e R e c i p i e n t L H I N > 1 2 < / s e r v i c e R e c i p i e n t L H I N > 
 
 	 	 	 < s e r v i c e D e l i v e r y L H I N > 5 < / s e r v i c e D e l i v e r y L H I N > 
 
 	 	 	 < c l i e n t D O B > 1 9 8 9 - 0 4 - 0 4 Z < / c l i e n t D O B > 
 
 	 	 	 < g e n d e r > F < / g e n d e r > 
 
 	 	 	 < m a r i t a l S t a t u s > 1 2 5 6 8 1 0 0 6 < / m a r i t a l S t a t u s > 
 
 	 	 	 < c l i e n t C a p a c i t y   p r o p e r t y = " T R U E "   p e r s o n a l C a r e = " F A L S E "   l e g a l G u a r d i a n = " C D A " / > 
 
 	 	 	 < r e f e r r a l S o u r c e > 0 1 8 - 0 5 < / r e f e r r a l S o u r c e > 
 
 	 	 	 < a b o r i g i n a l O r i g i n > 0 1 1 - 0 2 < / a b o r i g i n a l O r i g i n > 
 
 	 	 	 < c i t i z e n s h i p S t a t u s > C D N < / c i t i z e n s h i p S t a t u s > 
 
 	 	 	 < t i m e L i v e d I n C a n a d a   y e a r s = " 3 "   m o n t h s = " 8 " / > 
 
 	 	 	 < i m m i g E x p L i s t   o t h e r I m m i g E x p = " o t h e r   i m m i g r a t i o n   e x p e r i e n c e   t e x t " > 
 
 	 	 	 	 < v a l u e > 2 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 3 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 7 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 8 < / v a l u e > 
 
 	 	 	 < / i m m i g E x p L i s t > 
 
 	 	 	 < d i s c r i m E x p L i s t   o t h e r D i s c r i m E x p = " o t h e r   d i s c r i m i n a t i o n   e x p e r i e n c e   t e x t " > 
 
 	 	 	 	 < v a l u e > 2 1 1 3 4 0 0 2 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 3 6 5 8 7 3 0 0 7 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 4 1 0 5 1 5 0 0 3 < / v a l u e > 
 
 	 	 	 < / d i s c r i m E x p L i s t > 
 
 	 	 	 < p r e f L a n g > f r a < / p r e f L a n g > 
 
 	 	 	 < s e r v i c e L a n g > e n g < / s e r v i c e L a n g > 
 
 	 	 	 < l e g a l I s s u e s > C i v i l < / l e g a l I s s u e s > 
 
 	 	 	 < l e g a l S t a t u s L i s t > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 1 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 2 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 3 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 8 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 9 < / l e g a l S t a t u s > 
 
 	 	 	 < / l e g a l S t a t u s L i s t > 
 
 	 	 	 < e x i t D i s p o s i t i o n > 0 1 9 - 0 6 < / e x i t D i s p o s i t i o n > 
 
 	 	 < / c l i e n t R e c o r d > 
 
 	 	 < O C A N D o m a i n s > 
 
 	 	 	 < d o m a i n   n a m e = " 0 1 - a c c o m m o d a t i o n " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 	 < r e s i d e n c e T y p e > 0 2 4 - 1 9 < / r e s i d e n c e T y p e > 
 
 	 	 	 	 < r e s i d e n c e S u p p o r t > 2 4 A - 0 5 < / r e s i d e n c e S u p p o r t > 
 
 	 	 	 	 < l i v i n g A r r a n g e m e n t T y p e > 0 2 3 - 0 8 < / l i v i n g A r r a n g e m e n t T y p e > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 2 - f o o d " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 3 - l o o k i n g   a f t e r   t h e   h o m e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 4 - s e l f - c a r e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 5 - d a y t i m e   a c t i v i t i e s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 3 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 	 < e m p l o y S t a t u s > 2 2 4 3 6 6 0 0 4 < / e m p l o y S t a t u s > 
 
 	 	 	 	 < e d u c a t i o n P r o g r a m S t a t u s > 2 2 4 8 7 0 0 0 1 < / e d u c a t i o n P r o g r a m S t a t u s > 
 
 	 	 	 	 < r i s k U n e m p l o y m e n t L i s t > 
 
 	 	 	 	 	 < r i s k U n e m p l o y m e n t > U D E R - 0 2 < / r i s k U n e m p l o y m e n t > 
 
 	 	 	 	 	 < r i s k U n e m p l o y m e n t > U D E R - 0 3 < / r i s k U n e m p l o y m e n t > 
 
 	 	 	 	 	 < r i s k U n e m p l o y m e n t > U D E R - 0 6 < / r i s k U n e m p l o y m e n t > 
 
 	 	 	 	 < / r i s k U n e m p l o y m e n t L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 6 - p h y s i c a l   h e a l t h " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < m e d i c a l C o n d i t i o n L i s t   a u t i s m D e t a i l = " a u t i s m   s p e c i f i c   t e x t "   o t h e r D e t a i l = " o t h e r   m e d i c a l   t e x t " > 
 
 	 	 	 	 	 < m e d i c a l C o n d i t i o n > 4 0 8 8 5 6 0 0 3 < / m e d i c a l C o n d i t i o n > 
 
 	 	 	 	 	 < m e d i c a l C o n d i t i o n > 4 1 0 5 1 5 0 0 3 < / m e d i c a l C o n d i t i o n > 
 
 	 	 	 	 	 < m e d i c a l C o n d i t i o n > 4 1 4 9 1 6 0 0 1 < / m e d i c a l C o n d i t i o n > 
 
 	 	 	 	 < / m e d i c a l C o n d i t i o n L i s t > 
 
 	 	 	 	 < p h y s i c a l H e a l t h C o n c e r n > T R U E < / p h y s i c a l H e a l t h C o n c e r n > 
 
 	 	 	 	 < c o n c e r n A r e a L i s t   o t h e r C o n c e r n A r e a = " o t h e r   c o n c e r n   t e x t " > 
 
 	 	 	 	 	 < c o n c e r n A r e a > 1 1 9 4 1 5 0 0 7 < / c o n c e r n A r e a > 
 
 	 	 	 	 	 < c o n c e r n A r e a > 1 1 8 9 5 2 0 0 5 < / c o n c e r n A r e a > 
 
 	 	 	 	 	 < c o n c e r n A r e a > 4 1 0 5 1 5 0 0 3 < / c o n c e r n A r e a > 
 
 	 	 	 	 < / c o n c e r n A r e a L i s t > 
 
 	 	 	 	 < m e d i c a t i o n L i s t > 
 
 	 	 	 	 	 < m e d i c a t i o n D e t a i l   t a k e n A s P r e s c r i b e d = " T R U E "   i s H e l p P r o v i d e d = " F A L S E "   i s H e l p N e e d e d = " T R U E " / > 
 
 	 	 	 	 	 < m e d i c a t i o n D e t a i l   t a k e n A s P r e s c r i b e d = " F A L S E "   i s H e l p P r o v i d e d = " U N K "   i s H e l p N e e d e d = " T R U E " / > 
 
 	 	 	 	 	 < m e d i c a t i o n D e t a i l   t a k e n A s P r e s c r i b e d = " U N K "   i s H e l p P r o v i d e d = " T R U E "   i s H e l p N e e d e d = " F A L S E " / > 
 
 	 	 	 	 < / m e d i c a t i o n L i s t > 
 
 	 	 	 	 < s i d e E f f e c t s > T R U E < / s i d e E f f e c t s > 
 
 	 	 	 	 < d a i l y L i v i n g A f f e c t e d > T R U E < / d a i l y L i v i n g A f f e c t e d > 
 
 	 	 	 	 < s i d e E f f e c t s D e t a i l L i s t   o t h e r S i d e E f f e c t s D e t a i l = " o t h e r   s i d e   e f f e c t   t e x t " > 
 
 	 	 	 	 	 < s i d e E f f e c t s D e t a i l > 2 4 6 6 3 6 0 0 8 < / s i d e E f f e c t s D e t a i l > 
 
 	 	 	 	 	 < s i d e E f f e c t s D e t a i l > 5 3 6 1 9 0 0 0 < / s i d e E f f e c t s D e t a i l > 
 
 	 	 	 	 	 < s i d e E f f e c t s D e t a i l > 4 1 0 5 1 5 0 0 3 < / s i d e E f f e c t s D e t a i l > 
 
 	 	 	 	 < / s i d e E f f e c t s D e t a i l L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 7 - p s y c h o t i c   s y m p t o m s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 	 < h o s p i t a l i z e d P a s t T w o Y e a r s > T R U E < / h o s p i t a l i z e d P a s t T w o Y e a r s > 
 
 	 	 	 	 < t o t a l A d m i s s i o n s > 5 < / t o t a l A d m i s s i o n s > 
 
 	 	 	 	 < t o t a l H o s p i t a l D a y s > 7 8 < / t o t a l H o s p i t a l D a y s > 
 
 	 	 	 	 < c o m m u n i t y T r e a t O r d e r > 0 1 5 - 0 3 < / c o m m u n i t y T r e a t O r d e r > 
 
 	 	 	 	 < s y m p t o m L i s t   o t h e r S y m p t o m = " o t h e r   s y m p t o m   t e x t " > 
 
 	 	 	 	 	 < s y m p t o m > 4 1 0 5 1 6 0 0 2 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 7 5 4 0 8 0 0 8 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 2 0 7 3 0 0 0 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 1 4 0 2 0 0 1 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 5 5 9 2 9 0 0 7 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 4 1 0 5 1 5 0 0 3 < / s y m p t o m > 
 
 	 	 	 	 < / s y m p t o m L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 8 - c o n d i t i o n   a n d   t r e a t m e n t " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 	 < d i a g n o s t i c L i s t > 
 
 	 	 	 	 	 < d i a g n o s t i c > 2 7 7 6 0 0 0 < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > M D G M C < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > 8 7 8 5 8 0 0 2 < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > S R D D H < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > D H < / d i a g n o s t i c > 
 
 	 	 	 	 < / d i a g n o s t i c L i s t > 
 
 	 	 	 	 < o t h e r I l l n e s s L i s t > 
 
 	 	 	 	 	 < o t h e r I l l n e s s > 0 1 6 A - 0 1 < / o t h e r I l l n e s s > 
 
 	 	 	 	 	 < o t h e r I l l n e s s > 0 1 6 A - 0 3 < / o t h e r I l l n e s s > 
 
 	 	 	 	 < / o t h e r I l l n e s s L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 9 - p s y c h o l o g i c a l   d i s t r e s s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 0 - s a f e t y   t o   s e l f " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < s u i c i d e A t t e m p t > T R U E < / s u i c i d e A t t e m p t > 
 
 	 	 	 	 < s u i c i d e T h o u g h t s > T R U E < / s u i c i d e T h o u g h t s > 
 
 	 	 	 	 < s a f e t y C o n c e r n S e l f > T R U E < / s a f e t y C o n c e r n S e l f > 
 
 	 	 	 	 < s a f e t y T o S e l f R i s k L i s t   o t h e r S a f e t y T o S e l f R i s k = " o t h e r   r i s k   t o   s e l f   t e x t " > 
 
 	 	 	 	 	 < s a f e t y T o S e l f R i s k > 2 2 5 9 1 5 0 0 6 < / s a f e t y T o S e l f R i s k > 
 
 	 	 	 	 	 < s a f e t y T o S e l f R i s k > 4 0 1 2 0 6 0 0 8 < / s a f e t y T o S e l f R i s k > 
 
 	 	 	 	 	 < s a f e t y T o S e l f R i s k > 4 1 0 5 1 5 0 0 3 < / s a f e t y T o S e l f R i s k > 
 
 	 	 	 	 < / s a f e t y T o S e l f R i s k L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 1 - s a f e t y   t o   o t h e r s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 2 - a l c o h o l " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 	 < d r i n k A l c o h o l   q u a n t i t y = " 6 "   f r e q u e n c y = " 1 " / > 
 
 	 	 	 	 < s t a g e O f C h a n g e A l c o h o l > 5 < / s t a g e O f C h a n g e A l c o h o l > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 3 - d r u g s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 2 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 	 < d r u g U s e L i s t   i n j e c t e d = " 6 " > 
 
 	 	 	 	 	 < d r u g U s e   n a m e = " 3 9 8 7 0 5 0 0 4 "   f r e q u e n c y = " 5 " / > 
 
 	 	 	 	 	 < d r u g U s e   n a m e = " 2 2 6 0 4 4 0 0 4 "   f r e q u e n c y = " 6 " / > 
 
 	 	 	 	 	 < d r u g U s e   n a m e = " 4 1 0 5 1 5 0 0 3 "   f r e q u e n c y = " 5 " / > 
 
 	 	 	 	 < / d r u g U s e L i s t > 
 
 	 	 	 	 < s t a g e O f C h a n g e D r u g s > 2 < / s t a g e O f C h a n g e D r u g s > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 4 - o t h e r   a d d i c t i o n s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 	 < a d d i c t i o n T y p e L i s t   o t h e r A d d i c t i o n T y p e = " o t h e r   a d d i c t i o n   t e x t " > 
 
 	 	 	 	 	 < a d d i c t i o n T y p e > 1 0 5 5 2 3 0 0 9 < / a d d i c t i o n T y p e > 
 
 	 	 	 	 	 < a d d i c t i o n T y p e > 5 6 2 9 4 0 0 8 < / a d d i c t i o n T y p e > 
 
 	 	 	 	 	 < a d d i c t i o n T y p e > 4 1 0 5 1 5 0 0 3 < / a d d i c t i o n T y p e > 
 
 	 	 	 	 < / a d d i c t i o n T y p e L i s t > 
 
 	 	 	 	 < s t a g e O f C h a n g e A d d i c t i o n s > 3 < / s t a g e O f C h a n g e A d d i c t i o n s > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 5 - c o m p a n y " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 2 " / > 
 
 	 	 	 	 < c h a n g e d S o c i a l P a t t e r n s > C D A < / c h a n g e d S o c i a l P a t t e r n s > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 6 - i n t i m a t e   r e l a t i o n s h i p s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 7 - s e x u a l   e x p r e s s i o n " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 8 - c h i l d c a r e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 9 - o t h e r   d e p e n d e n t s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 0 - b a s i c   e d u c a t i o n " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < h i g h e s t E d u c a t i o n L e v e l > H L E S - 6 < / h i g h e s t E d u c a t i o n L e v e l > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 1 - t e l e p h o n e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 2 - t r a n s p o r t " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 3 - m o n e y " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 	 < s o u r c e O f I n c o m e > 0 3 1 - 1 0 < / s o u r c e O f I n c o m e > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 4 - b e n e f i t s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 < / O C A N D o m a i n s > 
 
 	 	 < a d d i t i o n a l E l e m e n t s > 
 
 	 	 	 < ! - -   B e g i n   F r e e   T e x t   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < c l i e n t H o p e s F o r F u t u r e / > 
 
 	 	 	 < c l i e n t N e e d T o G e t T h e r e / > 
 
 	 	 	 < c l i e n t V i e w M e n t a l H e a l t h / > 
 
 	 	 	 < c l i e n t S p i r i t u a l i t y I m p o r t a n c e / > 
 
 	 	 	 < c l i e n t C u l t u r e H e r i t a g e I m p o r t a n c e / > 
 
 	 	 	 < ! - -   E n d   F r e e   T e x t   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < p r e s e n t i n g I s s u e L i s t > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 1 " / > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 3 " / > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 5 " / > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 8 " / > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 9 " / > 
 
 	 	 	 < / p r e s e n t i n g I s s u e L i s t > 
 
 	 	 	 < a c t i o n L i s t > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 1 "   d o m a i n = " 1 3 - d r u g s " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 2 "   d o m a i n = " 1 5 - c o m p a n y " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 3 "   d o m a i n = " 2 0 - b a s i c   e d u c a t i o n " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 4 "   d o m a i n = " 2 3 - m o n e y " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 5 "   d o m a i n = " 1 0 - s a f e t y   t o   s e l f " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 6 "   d o m a i n = " 2 4 - b e n e f i t s " / > 
 
 	 	 	 < / a c t i o n L i s t > 
 
 	 	 	 < r e f e r r a l L i s t > 
 
 	 	 	 	 < r e f e r r a l   o p t i m a l = " 0 A S "   s p e c i f y O p t i m a l = " s p e c i f y   t e x t   f o r   o p t i m a l   r e f e r r a l "   a c t u a l = " F O R "   s p e c i f y A c t u a l = " s p e c i f y   t e x t   f o r   a c t u a l   r e f e r r a l "   d i f f e r e n c e R e a s o n = " R D - 4 "   s t a t u s = " R S - 1 " / > 
 
 	 	 	 	 < r e f e r r a l   o p t i m a l = " 0 A B "   s p e c i f y O p t i m a l = " s p e c i f y   t e x t   f o r   o p t i m a l   r e f e r r a l "   a c t u a l = " H P A "   s p e c i f y A c t u a l = " s p e c i f y   t e x t   f o r   a c t u a l   r e f e r r a l "   d i f f e r e n c e R e a s o n = " R D - 2 "   s t a t u s = " R S - 2 " / > 
 
 	 	 	 	 < r e f e r r a l   o p t i m a l = " E A T "   s p e c i f y O p t i m a l = " s p e c i f y   t e x t   f o r   o p t i m a l   r e f e r r a l "   a c t u a l = " H S C "   s p e c i f y A c t u a l = " s p e c i f y   t e x t   f o r   a c t u a l   r e f e r r a l "   d i f f e r e n c e R e a s o n = " R D - 3 "   s t a t u s = " R S - 1 " / > 
 
 	 	 	 	 < r e f e r r a l   o p t i m a l = " 0 F I "   s p e c i f y O p t i m a l = " s p e c i f y   t e x t   f o r   o p t i m a l   r e f e r r a l "   a c t u a l = " 0 S R "   s p e c i f y A c t u a l = " s p e c i f y   t e x t   f o r   a c t u a l   r e f e r r a l "   d i f f e r e n c e R e a s o n = " R D - 1 "   s t a t u s = " R S - 2 " / > 
 
 	 	 	 < / r e f e r r a l L i s t > 
 
 	 	 < / a d d i t i o n a l E l e m e n t s > 
 
 	 < / O C A N S u b m i s s i o n R e c o r d > 
 
 
 
 	 < ! - -   a s s e s s m e n t   r e c o r d #   1 2 :   j u s t   a n o t h e r   a s s e s s m e n t   - - > 
 
 	 < O C A N S u b m i s s i o n R e c o r d   a s s e s s m e n t I D = " 1 0 1 2 "   s t a r t D a t e = " 2 0 0 9 - 0 1 - 1 1 Z "   c o m p l e t i o n D a t e = " 2 0 0 9 - 0 1 - 1 6 Z "   a s s e s s m e n t S t a t u s = " C o m p l e t e " > 
 
 	 	 < o r g a n i z a t i o n R e c o r d > 
 
 	 	 	 < s e r v i c e O r g   n a m e = " N E   L H I N   T e s t   O r g "   n u m b e r = " 1 2 3 " / > 
 
 	 	 	 < p r o g r a m   n a m e = " I n t e n s i v e   C a s e   M a n a g e m e n t "   n u m b e r = " 3 0 2 5 " / > 
 
 	 	 	 < M I S F u n c t i o n   v a l u e = " 7 2 5   5 1   7 6   2 0 " / > 
 
 	 	 < / o r g a n i z a t i o n R e c o r d > 
 
 	 	 < c l i e n t R e c o r d > 
 
 	 	 	 < c l i e n t I D   o r g C l i e n t I D = " 1 2 3 8 5 0 " / > 
 
 	 	 	 < ! - -   B e g i n   P H I   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < c l i e n t N a m e   l a s t = " "   f i r s t = " " / > 
 
 	 	 	 < c l i e n t A d d r e s s   l i n e 1 = " "   l i n e 2 = " "   c i t y = " "   p r o v i n c e = " "   p o s t a l C o d e = " " / > 
 
 	 	 	 < c l i e n t P h o n e / > 
 
 	 	 	 < c l i e n t O H I P   n u m b e r = " "   v e r s i o n = " " / > 
 
 	 	 	 < c l i e n t C u l t u r e / > 
 
 	 	 	 < ! - -   E n d   P H I   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < r e a s o n F o r A s s e s s m e n t   v a l u e = " I A "   o t h e r = " " / > 
 
 	 	 	 < c l i e n t C o n t a c t > 
 
 	 	 	 	 < d o c t o r C o n t a c t   d o c t o r = " T R U E "   l a s t S e e n = " L S - 6 " / > 
 
 	 	 	 	 < p s y c h i a t r i s t C o n t a c t   p s y c h i a t r i s t = " T R U E "   l a s t S e e n = " L S - 1 2 " / > 
 
 	 	 	 	 < o t h e r P r a c t i t i o n e r C o n t a c t   p r a c t i t i o n e r T y p e = " 3 1 1 1 "   l a s t S e e n = " L S - 1 " / > 
 
 	 	 	 	 < o t h e r P r a c t i t i o n e r C o n t a c t   p r a c t i t i o n e r T y p e = " 3 1 1 2 "   l a s t S e e n = " L S - 1 3 " / > 
 
 	 	 	 	 < o t h e r P r a c t i t i o n e r C o n t a c t   p r a c t i t i o n e r T y p e = " 4 2 1 7 "   l a s t S e e n = " L S - 6 " / > 
 
 	 	 	 	 < o t h e r A g e n c y C o n t a c t   l a s t S e e n = " L S - 1 2 " / > 
 
 	 	 	 < / c l i e n t C o n t a c t > 
 
 	 	 	 < s e r v i c e R e c i p i e n t L o c a t i o n > 0 1 0 - 5 2 < / s e r v i c e R e c i p i e n t L o c a t i o n > 
 
 	 	 	 < s e r v i c e R e c i p i e n t L H I N > 1 2 < / s e r v i c e R e c i p i e n t L H I N > 
 
 	 	 	 < s e r v i c e D e l i v e r y L H I N > 5 < / s e r v i c e D e l i v e r y L H I N > 
 
 	 	 	 < c l i e n t D O B > 1 9 3 8 - 0 1 - 0 3 Z < / c l i e n t D O B > 
 
 	 	 	 < g e n d e r > C D A < / g e n d e r > 
 
 	 	 	 < m a r i t a l S t a t u s > 1 2 5 6 8 1 0 0 6 < / m a r i t a l S t a t u s > 
 
 	 	 	 < c l i e n t C a p a c i t y   p r o p e r t y = " T R U E "   p e r s o n a l C a r e = " F A L S E "   l e g a l G u a r d i a n = " C D A " / > 
 
 	 	 	 < r e f e r r a l S o u r c e > 0 1 8 - 0 5 < / r e f e r r a l S o u r c e > 
 
 	 	 	 < a b o r i g i n a l O r i g i n > 0 1 1 - 0 2 < / a b o r i g i n a l O r i g i n > 
 
 	 	 	 < c i t i z e n s h i p S t a t u s > C D A < / c i t i z e n s h i p S t a t u s > 
 
 	 	 	 < t i m e L i v e d I n C a n a d a   y e a r s = " 3 "   m o n t h s = " 8 " / > 
 
 	 	 	 < i m m i g E x p L i s t   o t h e r I m m i g E x p = " o t h e r   i m m i g r a t i o n   e x p e r i e n c e   t e x t " > 
 
 	 	 	 	 < v a l u e > 2 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 3 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 7 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 8 < / v a l u e > 
 
 	 	 	 < / i m m i g E x p L i s t > 
 
 	 	 	 < d i s c r i m E x p L i s t   o t h e r D i s c r i m E x p = " o t h e r   d i s c r i m i n a t i o n   e x p e r i e n c e   t e x t " > 
 
 	 	 	 	 < v a l u e > 2 1 1 3 4 0 0 2 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 3 6 5 8 7 3 0 0 7 < / v a l u e > 
 
 	 	 	 	 < v a l u e > 4 1 0 5 1 5 0 0 3 < / v a l u e > 
 
 	 	 	 < / d i s c r i m E x p L i s t > 
 
 	 	 	 < p r e f L a n g > f r a < / p r e f L a n g > 
 
 	 	 	 < s e r v i c e L a n g > e n g < / s e r v i c e L a n g > 
 
 	 	 	 < l e g a l I s s u e s > C i v i l < / l e g a l I s s u e s > 
 
 	 	 	 < l e g a l S t a t u s L i s t > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 1 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 2 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 3 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 8 < / l e g a l S t a t u s > 
 
 	 	 	 	 < l e g a l S t a t u s > 0 1 3 - 0 9 < / l e g a l S t a t u s > 
 
 	 	 	 < / l e g a l S t a t u s L i s t > 
 
 	 	 	 < e x i t D i s p o s i t i o n > 0 1 9 - 0 6 < / e x i t D i s p o s i t i o n > 
 
 	 	 < / c l i e n t R e c o r d > 
 
 	 	 < O C A N D o m a i n s > 
 
 	 	 	 < d o m a i n   n a m e = " 0 1 - a c c o m m o d a t i o n " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 	 < r e s i d e n c e T y p e > 0 2 4 - 1 9 < / r e s i d e n c e T y p e > 
 
 	 	 	 	 < r e s i d e n c e S u p p o r t > 2 4 A - 0 5 < / r e s i d e n c e S u p p o r t > 
 
 	 	 	 	 < l i v i n g A r r a n g e m e n t T y p e > 0 2 3 - 0 8 < / l i v i n g A r r a n g e m e n t T y p e > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 2 - f o o d " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 3 - l o o k i n g   a f t e r   t h e   h o m e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 4 - s e l f - c a r e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 5 - d a y t i m e   a c t i v i t i e s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 3 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 	 < e m p l o y S t a t u s > 2 2 4 3 6 6 0 0 4 < / e m p l o y S t a t u s > 
 
 	 	 	 	 < e d u c a t i o n P r o g r a m S t a t u s > 2 2 4 8 7 0 0 0 1 < / e d u c a t i o n P r o g r a m S t a t u s > 
 
 	 	 	 	 < r i s k U n e m p l o y m e n t L i s t > 
 
 	 	 	 	 	 < r i s k U n e m p l o y m e n t > U D E R - 0 2 < / r i s k U n e m p l o y m e n t > 
 
 	 	 	 	 	 < r i s k U n e m p l o y m e n t > U D E R - 0 3 < / r i s k U n e m p l o y m e n t > 
 
 	 	 	 	 	 < r i s k U n e m p l o y m e n t > U D E R - 0 6 < / r i s k U n e m p l o y m e n t > 
 
 	 	 	 	 < / r i s k U n e m p l o y m e n t L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 6 - p h y s i c a l   h e a l t h " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < m e d i c a l C o n d i t i o n L i s t   a u t i s m D e t a i l = " a u t i s m   s p e c i f i c   t e x t "   o t h e r D e t a i l = " o t h e r   m e d i c a l   t e x t " > 
 
 	 	 	 	 	 < m e d i c a l C o n d i t i o n > 4 0 8 8 5 6 0 0 3 < / m e d i c a l C o n d i t i o n > 
 
 	 	 	 	 	 < m e d i c a l C o n d i t i o n > 4 1 0 5 1 5 0 0 3 < / m e d i c a l C o n d i t i o n > 
 
 	 	 	 	 	 < m e d i c a l C o n d i t i o n > 4 1 4 9 1 6 0 0 1 < / m e d i c a l C o n d i t i o n > 
 
 	 	 	 	 < / m e d i c a l C o n d i t i o n L i s t > 
 
 	 	 	 	 < p h y s i c a l H e a l t h C o n c e r n > T R U E < / p h y s i c a l H e a l t h C o n c e r n > 
 
 	 	 	 	 < c o n c e r n A r e a L i s t   o t h e r C o n c e r n A r e a = " o t h e r   c o n c e r n   t e x t " > 
 
 	 	 	 	 	 < c o n c e r n A r e a > 1 1 9 4 1 5 0 0 7 < / c o n c e r n A r e a > 
 
 	 	 	 	 	 < c o n c e r n A r e a > 1 1 8 9 5 2 0 0 5 < / c o n c e r n A r e a > 
 
 	 	 	 	 	 < c o n c e r n A r e a > 4 1 0 5 1 5 0 0 3 < / c o n c e r n A r e a > 
 
 	 	 	 	 < / c o n c e r n A r e a L i s t > 
 
 	 	 	 	 < m e d i c a t i o n L i s t > 
 
 	 	 	 	 	 < m e d i c a t i o n D e t a i l   t a k e n A s P r e s c r i b e d = " T R U E "   i s H e l p P r o v i d e d = " F A L S E "   i s H e l p N e e d e d = " T R U E " / > 
 
 	 	 	 	 	 < m e d i c a t i o n D e t a i l   t a k e n A s P r e s c r i b e d = " F A L S E "   i s H e l p P r o v i d e d = " U N K "   i s H e l p N e e d e d = " T R U E " / > 
 
 	 	 	 	 	 < m e d i c a t i o n D e t a i l   t a k e n A s P r e s c r i b e d = " U N K "   i s H e l p P r o v i d e d = " T R U E "   i s H e l p N e e d e d = " F A L S E " / > 
 
 	 	 	 	 < / m e d i c a t i o n L i s t > 
 
 	 	 	 	 < s i d e E f f e c t s > T R U E < / s i d e E f f e c t s > 
 
 	 	 	 	 < d a i l y L i v i n g A f f e c t e d > T R U E < / d a i l y L i v i n g A f f e c t e d > 
 
 	 	 	 	 < s i d e E f f e c t s D e t a i l L i s t   o t h e r S i d e E f f e c t s D e t a i l = " o t h e r   s i d e   e f f e c t   t e x t " > 
 
 	 	 	 	 	 < s i d e E f f e c t s D e t a i l > 2 4 6 6 3 6 0 0 8 < / s i d e E f f e c t s D e t a i l > 
 
 	 	 	 	 	 < s i d e E f f e c t s D e t a i l > 5 3 6 1 9 0 0 0 < / s i d e E f f e c t s D e t a i l > 
 
 	 	 	 	 	 < s i d e E f f e c t s D e t a i l > 4 1 0 5 1 5 0 0 3 < / s i d e E f f e c t s D e t a i l > 
 
 	 	 	 	 < / s i d e E f f e c t s D e t a i l L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 7 - p s y c h o t i c   s y m p t o m s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 	 < h o s p i t a l i z e d P a s t T w o Y e a r s > T R U E < / h o s p i t a l i z e d P a s t T w o Y e a r s > 
 
 	 	 	 	 < t o t a l A d m i s s i o n s > 5 < / t o t a l A d m i s s i o n s > 
 
 	 	 	 	 < t o t a l H o s p i t a l D a y s > 7 8 < / t o t a l H o s p i t a l D a y s > 
 
 	 	 	 	 < c o m m u n i t y T r e a t O r d e r > 0 1 5 - 0 3 < / c o m m u n i t y T r e a t O r d e r > 
 
 	 	 	 	 < s y m p t o m L i s t   o t h e r S y m p t o m = " o t h e r   s y m p t o m   t e x t " > 
 
 	 	 	 	 	 < s y m p t o m > 4 1 0 5 1 6 0 0 2 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 7 5 4 0 8 0 0 8 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 2 0 7 3 0 0 0 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 1 4 0 2 0 0 1 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 5 5 9 2 9 0 0 7 < / s y m p t o m > 
 
 	 	 	 	 	 < s y m p t o m > 4 1 0 5 1 5 0 0 3 < / s y m p t o m > 
 
 	 	 	 	 < / s y m p t o m L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 8 - c o n d i t i o n   a n d   t r e a t m e n t " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 0 " / > 
 
 	 	 	 	 < d i a g n o s t i c L i s t > 
 
 	 	 	 	 	 < d i a g n o s t i c > 2 7 7 6 0 0 0 < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > M D G M C < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > 8 7 8 5 8 0 0 2 < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > S R D D H < / d i a g n o s t i c > 
 
 	 	 	 	 	 < d i a g n o s t i c > D H < / d i a g n o s t i c > 
 
 	 	 	 	 < / d i a g n o s t i c L i s t > 
 
 	 	 	 	 < o t h e r I l l n e s s L i s t > 
 
 	 	 	 	 	 < o t h e r I l l n e s s > 0 1 6 A - 0 1 < / o t h e r I l l n e s s > 
 
 	 	 	 	 	 < o t h e r I l l n e s s > 0 1 6 A - 0 3 < / o t h e r I l l n e s s > 
 
 	 	 	 	 < / o t h e r I l l n e s s L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 0 9 - p s y c h o l o g i c a l   d i s t r e s s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 0 - s a f e t y   t o   s e l f " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < s u i c i d e A t t e m p t > T R U E < / s u i c i d e A t t e m p t > 
 
 	 	 	 	 < s u i c i d e T h o u g h t s > T R U E < / s u i c i d e T h o u g h t s > 
 
 	 	 	 	 < s a f e t y C o n c e r n S e l f > T R U E < / s a f e t y C o n c e r n S e l f > 
 
 	 	 	 	 < s a f e t y T o S e l f R i s k L i s t   o t h e r S a f e t y T o S e l f R i s k = " o t h e r   r i s k   t o   s e l f   t e x t " > 
 
 	 	 	 	 	 < s a f e t y T o S e l f R i s k > 2 2 5 9 1 5 0 0 6 < / s a f e t y T o S e l f R i s k > 
 
 	 	 	 	 	 < s a f e t y T o S e l f R i s k > 4 0 1 2 0 6 0 0 8 < / s a f e t y T o S e l f R i s k > 
 
 	 	 	 	 	 < s a f e t y T o S e l f R i s k > 4 1 0 5 1 5 0 0 3 < / s a f e t y T o S e l f R i s k > 
 
 	 	 	 	 < / s a f e t y T o S e l f R i s k L i s t > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 1 - s a f e t y   t o   o t h e r s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 2 - a l c o h o l " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 	 < d r i n k A l c o h o l   q u a n t i t y = " 6 "   f r e q u e n c y = " 1 " / > 
 
 	 	 	 	 < s t a g e O f C h a n g e A l c o h o l > 5 < / s t a g e O f C h a n g e A l c o h o l > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 3 - d r u g s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 2 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 	 < d r u g U s e L i s t   i n j e c t e d = " 6 " > 
 
 	 	 	 	 	 < d r u g U s e   n a m e = " 3 9 8 7 0 5 0 0 4 "   f r e q u e n c y = " 5 " / > 
 
 	 	 	 	 	 < d r u g U s e   n a m e = " 2 2 6 0 4 4 0 0 4 "   f r e q u e n c y = " 6 " / > 
 
 	 	 	 	 	 < d r u g U s e   n a m e = " 4 1 0 5 1 5 0 0 3 "   f r e q u e n c y = " 5 " / > 
 
 	 	 	 	 < / d r u g U s e L i s t > 
 
 	 	 	 	 < s t a g e O f C h a n g e D r u g s > 2 < / s t a g e O f C h a n g e D r u g s > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 4 - o t h e r   a d d i c t i o n s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 	 < a d d i c t i o n T y p e L i s t   o t h e r A d d i c t i o n T y p e = " o t h e r   a d d i c t i o n   t e x t " > 
 
 	 	 	 	 	 < a d d i c t i o n T y p e > 1 0 5 5 2 3 0 0 9 < / a d d i c t i o n T y p e > 
 
 	 	 	 	 	 < a d d i c t i o n T y p e > 5 6 2 9 4 0 0 8 < / a d d i c t i o n T y p e > 
 
 	 	 	 	 	 < a d d i c t i o n T y p e > 4 1 0 5 1 5 0 0 3 < / a d d i c t i o n T y p e > 
 
 	 	 	 	 < / a d d i c t i o n T y p e L i s t > 
 
 	 	 	 	 < s t a g e O f C h a n g e A d d i c t i o n s > 3 < / s t a g e O f C h a n g e A d d i c t i o n s > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 5 - c o m p a n y " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 2 " / > 
 
 	 	 	 	 < c h a n g e d S o c i a l P a t t e r n s > C D A < / c h a n g e d S o c i a l P a t t e r n s > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 6 - i n t i m a t e   r e l a t i o n s h i p s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 7 - s e x u a l   e x p r e s s i o n " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 0 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 8 - c h i l d c a r e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 0 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 2 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 3 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 1 9 - o t h e r   d e p e n d e n t s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 1 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 1 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 0 - b a s i c   e d u c a t i o n " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " - 1 " / > 
 
 	 	 	 	 < h i g h e s t E d u c a t i o n L e v e l > H L E S - 6 < / h i g h e s t E d u c a t i o n L e v e l > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 1 - t e l e p h o n e " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 2 - t r a n s p o r t " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 3 - m o n e y " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 	 < s o u r c e O f I n c o m e > 0 3 1 - 1 0 < / s o u r c e O f I n c o m e > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 	 < d o m a i n   n a m e = " 2 4 - b e n e f i t s " > 
 
 	 	 	 	 < n e e d R a t i n g   s t a f f = " 9 "   c l i e n t = " - 1 " / > 
 
 	 	 	 	 < i n f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p R e c v d   s t a f f = " 9 " / > 
 
 	 	 	 	 < f o r m a l H e l p N e e d   s t a f f = " 9 " / > 
 
 	 	 	 < / d o m a i n > 
 
 	 	 < / O C A N D o m a i n s > 
 
 	 	 < a d d i t i o n a l E l e m e n t s > 
 
 	 	 	 < ! - -   B e g i n   F r e e   T e x t   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < c l i e n t H o p e s F o r F u t u r e / > 
 
 	 	 	 < c l i e n t N e e d T o G e t T h e r e / > 
 
 	 	 	 < c l i e n t V i e w M e n t a l H e a l t h / > 
 
 	 	 	 < c l i e n t S p i r i t u a l i t y I m p o r t a n c e / > 
 
 	 	 	 < c l i e n t C u l t u r e H e r i t a g e I m p o r t a n c e / > 
 
 	 	 	 < ! - -   E n d   F r e e   T e x t   I n f o r m a t i o n   F o r   F u t u r e   C o l l e c t i o n ,   o n l y   - - > 
 
 	 	 	 < p r e s e n t i n g I s s u e L i s t > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 1 " / > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 3 " / > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 5 " / > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 8 " / > 
 
 	 	 	 	 < p r e s e n t i n g I s s u e   t y p e = " 0 1 7 - 0 9 " / > 
 
 	 	 	 < / p r e s e n t i n g I s s u e L i s t > 
 
 	 	 	 < a c t i o n L i s t > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 1 "   d o m a i n = " 1 3 - d r u g s " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 2 "   d o m a i n = " 1 5 - c o m p a n y " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 3 "   d o m a i n = " 2 0 - b a s i c   e d u c a t i o n " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 4 "   d o m a i n = " 2 3 - m o n e y " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 5 "   d o m a i n = " 1 0 - s a f e t y   t o   s e l f " / > 
 
 	 	 	 	 < a c t i o n   p r i o r i t y = " 6 "   d o m a i n = " 2 4 - b e n e f i t s " / > 
 
 	 	 	 < / a c t i o n L i s t > 
 
 	 	 	 < r e f e r r a l L i s t > 
 
 	 	 	 	 < r e f e r r a l   o p t i m a l = " 0 A S "   s p e c i f y O p t i m a l = " s p e c i f y   t e x t   f o r   o p t i m a l   r e f e r r a l "   a c t u a l = " F O R "   s p e c i f y A c t u a l = " s p e c i f y   t e x t   f o r   a c t u a l   r e f e r r a l "   d i f f e r e n c e R e a s o n = " R D - 4 "   s t a t u s = " R S - 1 " / > 
 
 	 	 	 	 < r e f e r r a l   o p t i m a l = " 0 A B "   s p e c i f y O p t i m a l = " s p e c i f y   t e x t   f o r   o p t i m a l   r e f e r r a l "   a c t u a l = " H P A "   s p e c i f y A c t u a l = " s p e c i f y   t e x t   f o r   a c t u a l   r e f e r r a l "   d i f f e r e n c e R e a s o n = " R D - 2 "   s t a t u s = " R S - 2 " / > 
 
 	 	 	 	 < r e f e r r a l   o p t i m a l = " E A T "   s p e c i f y O p t i m a l = " s p e c i f y   t e x t   f o r   o p t i m a l   r e f e r r a l "   a c t u a l = " H S C "   s p e c i f y A c t u a l = " s p e c i f y   t e x t   f o r   a c t u a l   r e f e r r a l "   d i f f e r e n c e R e a s o n = " R D - 3 "   s t a t u s = " R S - 1 " / > 
 
 	 	 	 	 < r e f e r r a l   o p t i m a l = " 0 F I "   s p e c i f y O p t i m a l = " s p e c i f y   t e x t   f o r   o p t i m a l   r e f e r r a l "   a c t u a l = " 0 S R "   s p e c i f y A c t u a l = " s p e c i f y   t e x t   f o r   a c t u a l   r e f e r r a l "   d i f f e r e n c e R e a s o n = " R D - 1 "   s t a t u s = " R S - 2 " / > 
 
 	 	 	 < / r e f e r r a l L i s t > 
 
 	 	 < / a d d i t i o n a l E l e m e n t s > 
 
 	 < / O C A N S u b m i s s i o n R e c o r d > 
 
 
 
 < / O C A N S u b m i s s i o n F i l e > 
 
 