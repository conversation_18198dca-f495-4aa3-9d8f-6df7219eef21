image: docker:24.0.6

definitions:
  services:
    docker:
      memory: 15360
  caches:
    maven-repo: ~/.m2
  # TODO: Replace step-level caches with a robust solution:
  #  1. Define named Docker volumes in docker/maven/docker-compose.yml for
  #     /home/<USER>/.m2 and /home/<USER>/.npm.
  #  2. Enable Docker Layer Caching (DLC) in repository settings so those
  #     volumes persist across pipeline runs.
  # The current caches (maven-repo) only exists in the outer step
  # container and never benefits the Docker-in-Docker test step.
  steps:
    validate: &validate
      image: oscarpro/maven3-jdk18-bc
      name: Validate
      max-time: 20
      caches:
        - maven-repo
      size: 8x
      script:
        - unset MAVEN_CONFIG
        - bash build.sh
    run_tests: &run_tests
      name: Run All Tests
      services:
        - docker
      size: 8x
      script:
        - apk add --no-cache bash git
        - export DOCKER_BUILDKIT=0
        - rm -rf target
        - bash docker/maven/run-all-tests-ci.sh

pipelines:
  default:
    - parallel:
        steps:
          - step: *validate
          - step: *run_tests