#*
#* Copyright (c) 2001-2002. Department of Family Medicine, McMaster University. All Rights Reserved. *
#* This software is published under the GPL GNU General Public License.
#* This program is free software; you can redistribute it and/or
#* modify it under the terms of the GNU General Public License
#* as published by the Free Software Foundation; either version 2
#* of the License, or (at your option) any later version. *
#* This program is distributed in the hope that it will be useful,
#* but WITHOUT ANY WARRANTY; without even the implied warranty of
#* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
#* GNU General Public License for more details. * * You should have received a copy of the GNU General Public License
#* along with this program; if not, write to the Free Software
#* Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA. *
#*
#* <OSCAR TEAM>
#*
#* This software was written for the
#* Department of Family Medicine
#* McMaster University
#* Hamilton
#* Ontario, Canada
#*

## Mysql connection

# mysql database name
db_name = {oscar_db_name}?zeroDateTimeBehavior=round&useOldAliasMetadataBehavior=true&jdbcCompliantTruncation=false
db_name_readonly = {oscar_db_ro_name}?zeroDateTimeBehavior=round&useOldAliasMetadataBehavior=true&jdbcCompliantTruncation=false

# username
db_username = {oscar_db_username}
db_username_readonly = {oscar_db_ro_username}

# password for the username above
db_password = {oscar_db_user_password}
db_password_readonly = {oscar_db_ro_user_password}

db_type = mysql
db_driver = com.mysql.jdbc.Driver
db_uri = jdbc:mysql:///

#Validation query string for mySql
db_validationQuery = select 1

hibernate.dialect=org.oscarehr.util.persistence.OscarMySQL5Dialect

# true : enable retrieving statistics
# false : disable retrieving statistics
hibernate.generate_statistics=false

# true : enable showing sql statement
# false : disable showing sql statement
hibernate.show_sql=false

## Postgres Connections
#db_type = postgresql
#db_driver = org.postgresql.Driver
#db_uri = ***************************/

## Oracle connections
#db_name =
#db_type = oracle
#db_driver = oracle.jdbc.OracleDriver
#db_uri = ***************************************
#hibernate.dialect=org.hibernate.dialect.Oracle9iDialect

## DrugRef server
drugref_url = http://localhost:8080/drugref/DrugrefService
#drugref_url = http://************:8001

# DrugRef search administration route ("Oral" is preset)
# off : turn off ;  on : turn on
drugref_route_search = off

drugref_route = topical,intravenous,disinfectant

# Disease Registry Coding System
dxResearch_coding_sys = icd9,ichppccode

#Turns on Allergy Checking
RX_ALLERGY_CHECKING=yes

#Turn on RX3, "RX3=yes" to turn on RX3, "RX3=no" to turn off RX3
RX3=yes

#Turns renal dosing on
RENAL_DOSING_DS=yes

#Turns on mydrugref decision support
MYDRUGREF_DS=yes

# Prevention/Immunization
PREVENTION=yes
IMMUNIZATION_IN_PREVENTION=yes


##
#This is the base OscarDocument directory
#If this property is set it replaces all of the other properties with absolute file paths to the OscarDocument directory.
#The needed directories will be created from this directory
#ie. DOCUMENT_DIR will be create as /var/lib/OscarDocument/<Context>/document
#
BASE_DOCUMENT_DIR=/var/lib/OscarDocument/{oscar_practice_id}

# Billing download folder
HOME_DIR = /var/lib/OscarDocument/{oscar_practice_id}/billing/download/

# Documents directory
DOCUMENT_DIR = /var/lib/OscarDocument/{oscar_practice_id}/document/
DOCUMENT_DOWNLOAD_METHOD = stream
DOC_FORWARD: /dms/complete.jsp
RA_FORWORD: /billing/CA/ON/genRA.jsp
EA_FORWORD: /billing/CA/ON/billingEAreport.jsp
TA_FORWARD: /billing/CA/BC/genTA.jsp

# Demographic export/import
# TeMPorary DIRectory for export/import files
TMP_DIR: /var/lib/OscarDocument/{oscar_practice_id}/export/

# Control e-chart note creation when importing demographics with xml-cds format
# Caution: false values may cause loss of data as unknown fields are put into notes
# Default oscar behavior = true
ADD_IMPORT_NOTES_DEMOGRAPHIC_DATA = true
ADD_IMPORT_NOTES_ALLERGIES = true
ADD_IMPORT_NOTES_CLINICAL_NOTES = true
ADD_IMPORT_NOTES_PERSONAL_HIST = true
ADD_IMPORT_NOTES_PROBLEM_LIST = true
ADD_IMPORT_NOTES_RISK_FACTORS = true
ADD_IMPORT_NOTES_ALERTS = true
ADD_IMPORT_NOTES_MEDICATIONS = true
ADD_IMPORT_NOTES_IMMUNIZATIONS = true
ADD_IMPORT_NOTES_REPORTS = true

#Override doctor oscardoc as default provider when importing demographics
#IMPORT_DEFAULT_PROVIDER_FIRST_NAME = doctor
#IMPORT_DEFAULT_PROVIDER_LAST_NAME = oscardoc

#### for IBD clinic
#meditech_id = yes
#mc_number = yes
#eform_in_appointment = yes

#### Spire
spire_server_user=user_name
spire_server_password=password
spire_server_hostname=hostname

spire_download_dir=/var/lib/spire/labs/

#ModuleNames=Spire

#### HL7
hl7_a04_build_dir=/var/lib/adt/
l7_a04_sent_dir=/var/lib/adt/sent/
hl7_a04_fail_dir=/var/lib/adt/failed/

HL7_A04_GENERATION=false

HL7_A04_TRANSPORT_ADDR=127.0.0.1
HL7_A04_TRANSPORT_PORT=3987

# Uncomment to enable sending A04 data to Emerald
#ModuleNames=EmeraldA04


#### Billing

## The new billing system
# true : turn on the new billing system
# all other value will turn off the new billing system
isNewONbilling=true

## Invoice Reports
## yes: turn on Invoice Reports for the new billing system on admin page
## other values turn off Invoice Reports for the new billing system on admin page
TESTING=yes

## Enable 'check all' for updating billing price
SOB_CHECKALL=yes

# instance_type: determines what features are shown in the interface
# Options: ON, BC, AB
instance_type=BC

# billing_type: determines what billing system is used
# Options: ON, BC, CLINICAID
billing_type=CLINICAID

# XXX: DEPRECATED
# bill region has two options: ON or BC
# ON : Ontario
# BC : British Columbia
billregion=BC

# hctype: this property is used to select the default value of HC Type field
# when adding a new demographic. If this property is not specified, the billregion
# is used. Note that users can configure their own personal default option as well,
# by visiting the preference screen and selecting "Set Default HC Type"
#   options: ON - Ontario
#            QC - Quebec
# And so on for other provinces in Canada, or states in US
hctype=BC

# defaultsex has two options. If this property is not specified, users can still
# configure their own personal default option, by visiting the preference screen
# and selecting "Set Default Sex"
# M: Male
# F: Female
# defaultsex=F

# bill center codes
# G stands for Hamilton
# J - Kingston
# P - London
# E - Mississauga
# F - Oshawa
# D - Ottawa
# R - Sudbury
# U - Thunder Bay
# N - Toronto
billcenter = R

default_view = GP
clinic_view = 3866
clinic_no = 3821

# yes will auto fill the admission date on the billing screen
inPatient = no

# visit_type can be: Clinic Visit, Outpatient Visit, Hospital Visit, ER, Nursing Home, and Home Visit
visit_type = Clinic Visit
visittype = A|Practitioner's Office - In Community
visitlocation = 00|GREATER VICTORIA
dataCenterId = 00000
#msp_error_codes = # file location of msp error codes

#Super power for billing
BILLING_SUPERUSER=999998

## yes : TURNS ON NEW BC BILLING
## no : TURNS OFF NEW BC BILLING
NEW_BC_TELEPLAN=yes

## yes: turn on the default BC alt billing
## no : turn off the default BC alt billing
#BC_DEFAULT_ALT_BILLING=yes

## group billing or not
# group_billing = on

## Prepare billing site Ids
#scheduleSiteID=site1|site2|site3

# User rights
SUPERUSER = oscardoc

## eform image file path
eform_image = /var/lib/OscarDocument/{oscar_practice_id}/eform/images/
# eform_databaseap_config = /var/lib/OscarDocument/{oscar_practice_id}/eform/apconfig.xml

## forms
save_as_xml = true
form_record_path = /var/lib/OscarDocument/{oscar_practice_id}/form/records/

FORMS_PROMOTEXT=Created by: OSCAR The open-source EMR www.oscarcanada.org

### send to osdsf thru XMLRPC
#osdsfRPCURL=
#pdfFORMDIR = /var/lib/OscarDocument/{oscar_practice_id}/form


## oscarComm
## When the provider has the program access role of "oscarcomm",
## the link named as "oscarComm" will appear on the CME page if the value for the key "oscarcomm" here is on
# oscarcomm = on

## Path
## adjust the following value according to your factual needs
tomcat_path = /usr/share/tomcat6/
project_home = {oscar_practice_id}
backup_path = /home/<USER>/
oscarMeasurement_css=/var/lib/OscarDocument/{oscar_practice_id}/oscarEncounter/oscarMeasurements/styles/
oscarMeasurement_css_upload_path=/var/lib/OscarDocument/{oscar_practice_id}/oscarEncounter/oscarMeasurements/styles

oscarMeasurement_css_download_method = stream

### Measurement
#MEASUREMENT_DS_DIRECTORY=
#MEASUREMENT_DS_HTML_DIRECTORY=

### Surveillance
#surveillance_directory=
#surveillance_config_file=

### Prevention
#PREVENTION_ITEMS=
#PREVENTION_FILE=

### Workflow
#WORKFLOW_DS_DIRECTORY=

### ECT
## -1 : disable ECT auto save timer
## 1 : enable ECT auto save timer
#ECT_AUTOSAVE_TIMER=-1

# set ECT save feedback timer in milliseconds
#ECT_SAVE_FEEDBACK_TIMER=2500

###
#RX_FOOTER=
#FORMS_PROMOTEXT=
#demographicExt=

### PING
#PING-SERVER=http://127.0.0.1:8080/ping-server/PingServlet

# yes: Send to PING
# no : not send to PING
#PHR=yes


# Login info
login_local_ip = 192.168
login_max_failed_times = 10
login_max_duration = 10
login_lock = true

# Password strength policy
password_min_length = 8
password_min_groups = 3
password_pin_min_length = 4
password_group_lower_chars = abcdefghijklmnopqrstuvwxyz
password_group_upper_chars = ABCDEFGHIJKLMNOPQRSTUVWXYZ
password_group_digits = **********
password_group_special = ! @#$%^&*()_+|~-=\\`{}[]:\";'<>?,./

# Auto refresh settings, -1 to disable
refresh.appointmentprovideradminday.jsp = 180
refresh.encounterLayout.jsp = -1
# refresh.encounterLayout.jsp = 300

# Template code
schedule_templatecode = true

# Label Printing Pref
label.1no = 1
label.2no = 1
label.3no = 1
label.4no = 1
label.5no = 1
label.left = 200
label.top = 0
label.height = 145
label.gap = 0

# New demographic
phoneprefix = 250-

# Login screen
logintitle =
logintext =
loginlogo =

# Alternate view for receptionist - set to 'yes' to view appointment timeslots
# as the size of the template period, set to 'no' to view the timeslots as
# the size of the receptionist's preference
receptionist_alt_view = no

#Show appt reason in the appt view
#SHOW_APPT_REASON=yes

#Enable abilty to change appt statuses
ENABLE_EDIT_APPT_STATUS=yes

# Fax configuration
faxEnable=no
faxLogo =
faxIdentifier = zwf4t%8*9@s
faxURI = https://67.69.12.117:14043/OSCARFaxWebService
faxKeystore = /root/oscarFax/oscarFax.keystore

# Patient status options
inactive_statuses = 'IN','DE','IC','ID','MO','FI'

## Province names
## 2 fields per province delimited by |
## 1st field is value stored in database, 2nd is display value, uncomment for BC
#province_names = AB|AB-Alberta|BC|BC-British Columbia|MB|MB-Manitoba|NB|NB-New Brunswick|NF|NF-Newfoundland & Labrador|NT|NT-Northwest Territory|NS|NS-Nova Scotia|NU|NU-Nunavut|ON|ON-Ontario|PE|PE-Prince Edward Island|QC|QC-Quebec|SK|SK-Saskatchewan|YT|YT-Yukon|US|US resident

##Pathnet
#pathnet_url=Location of Pathnet DLL
#pathnet_username=User to access the system
#pathnet_password=User?s Password

##CDM Reminder Codes
CDM_ALERTS=250,428,4280

##The following list indicates which codes will be used for Individual Counseling alerts
COUNSELING_CODES=18220,18120,17220,17120,16220,16120,13220,12220,12120,00120

##Alert Polling frequency in milliseconds
#ALERT_POLL_FREQUENCY=240000

#HSFO config
#hsfo.loginSiteCode=99
#hsfo.userID=user
#hsfo.loginPassword=paswd
#hsfo.xmlVersionDate=2007-02-12

#hsfo.webServiceURL=
## not null : start quartz scheduler
#hsfo.loginSiteCode=

DX_QUICK_LIST_BILLING_REVIEW=yes
DX_QUICK_LIST_DEFAULT=Chronic Diseases
BILLING_DX_REFERENCE=yes

## CONTROLS WHICH LABS ARE USED
#PATHNET_LABS=yes
HL7TEXT_LABS=yes
#MDS_LABS=yes
#CML_LABS=yes

#LAB_TYPES=

### LAB
## yes : get lab data for all demographic's name
## no : get lab data for one particular client by searching his/her name
#LAB_NOMATCH_NAMES=yes

## TURNS ON THE NEW STYLE ECHART
USE_NEW_ECHART=yes

#TURNS ON THE NEW CASEMANAGEMENT INTERFACE IN THE ENCOUNTER.
CASEMANAGEMENT=all
#ORDER OF DISPLAY FOR ENCOUNTER NOTES
CMESort=UP

### Retrieve more recent saved notes
## off : retrieve all notes saved temporarily
## on : retrieve more recent temporarily saved notes in past 2 weeks
#maxTmpSave=on

#################CAISI##############################

# your host server's IP
host=127.0.0.1

## Cookie-revolver security framework
## off: turn off cookie-revolver security framework
## on: turn on cookie-revolver security framework
cr_security=off

## Load CAISI Application Context
#plugins=on
#caisi=on
#ModuleNames=Caisi
#NEW_CME_SWITCH=on

##caisi plugins

## program module
## off : turn off CAISI program module
## on : turn on CAISI program module
program=on

## ticklerplus control
## off: turn off CAISI tickler module
## on : turn on CAISI tickler module
#ticklerplus=on

## OCAN assessment warning message popup window
## OCAN_warning_window=on

## off : disable client name drop down list on the tickler list page
## on : all clients' names are in the drop down select list
clientdropbox=off


# The following is the amount of time redirect tracking entries are kept.
# As an example, it might not be useful to have link tracking beyond a year
# at which point you would set this to about 1000*60*60*24*365 = 31536000000
# This helps prevent build up of useless data in the database.
# If it's commmented out or set to -1 data will not be culled.
#REDIRECT_TRACKING_DATA_RETENTION_MILLIS=31536000000

## CAISI properties for agencies, starts here

# Old Intake A Form
# true : turn on the old intake A
# false : turn off the old intake A
intakea.enabled=false

# New Intake A Form
# true : enable the new intake A form
# false : disable the new intake A form
intakea.newForm=false

# Old Intake C Form
# true : enable the old intake C
# false : disable the old intake C, and disable streethealth old intakeC report
# If there is no the following entry, or it's set to false,
# streethealth intakeC report will be disabled and not show up on PMM
intakec.enabled=false

# New Intake C Form
# true : enable the new intake C form
# false : disable the new intake C form
intakec.newForm=false

## Enable the Streethealth Mental Health Report on PMM
streethealth=yes

## Notes written on CAISI CME have the password protection
## true : enable the password field
## false : disable the password field
casemgmt.note.password.enabled=true

## Client referral has the option of temporary admission
## true : enable temporary admission
## false : disable tmporary admission
pmm.refer.temporaryAdmission.enabled=true

## client search
## true : allow to search all clients in all programs
## false : only allow the provider search the clients in this provider's program domain
pmm.client.search.outside.of.domain.enabled=true

# Submit OCAN (Ontario Common Assessment of Need) forms via IAR (Integrated Assessment Record) web service
# Replace the user, password, idPrefix with your own ones.
# If you don't have idPrefix (most of you don't have), please comment out ocan.iar.idPrefix or give emptye value like this: ocan.iar.idPrefix=
#ocan.iar.user=caisihsp
#ocan.iar.password=hsp1caisi
#ocan.iar.url=https://iarvt.ccim.on.ca/iarws-2.0/services/SubmissionService
#ocan.iar.idPrefix=TWC

## OHIP health card number
## yes : collect OHIP number
## no : don't collect OHOP number
GET_OHIP_INFO=yes

## Toronto RFQ
## yes : enable Toronto RFQ, which means new features for RFQ will be added and part of caisi/oscar features will be removed.
## no : disable Toronto RFQ
TORONTO_RFQ=no

## facility
## true : filter notes and programs based on the facility
## false : don't filter notes or programs by facility
FILTER_ON_FACILITY=false

## the nested discharge reasons
## yes : use the nested discharge reasons that used in sherbourne health center
## no : not use nested discharge reasons
ALT_DISCHARGE_REASON=no

## auto generated provider no.
## yes or true : the provider no. auto generated
## no or false : the provider no. not auto generated
AUTO_GENERATE_PROVIDER_NO=no

## encrypt PIN number
## yes : encrypt PIN
## no : not encrypt PIN
IS_PIN_ENCRYPTED=no

## CAISI logo
## true : use CAISI logo on PMM
## false : don't use CAISI logo on PMM
USE_CAISI_LOGO=true

# Type of shared issues between facilities, through Integrator
COMMUNITY_ISSUE_CODETYPE=icd10

# integerator update period in milliseconds
INTEGRATOR_UPDATE_PERIOD=43200000

# integerator throttle delay in milliseconds, should be less than 100, it's a delay on each WS call so it adds up quickly. Also it means a 100 delay would set a max of 10 requests per second already which is very slow.
INTEGRATOR_THROTTLE_DELAY=100

# switch to force a full update, if "no" then a hack interm update will be preformed
INTEGRATOR_FORCE_FULL=yes

# Has to be enabled for Ontario MD funded sites.  This will autoselect primaryEMR to yes if roster_Status = RO
#FORCED_ROSTER_INTEGRATOR_LOCAL_STORE=yes


# vmstat logging period in milliseconds
VMSTAT_LOGGING_PERIOD=900000

#Codes that wcb requires a form for.
WCB_FORM_REQUIRED_CODES=19937,19938,19939,19940,19941,19943,19944,19167,19173,19174,19175,19134,19135

# These parameters are for kiosk check ins, the late allowance is the allowable time check ins will be allowed after an appointment time
# is how early some one can check in prior to their appointment time. As an example with late=2 and early=3, if some one checks in at
# say 3pm, it will look for appointments from 1pm to 6pm. These numbers are in hours.
AdtA09Handler.CHECK_IN_LATE_ALLOWANCE=2
AdtA09Handler.CHECK_IN_EARLY_ALLOWANCE=4

#To Print Rx Fax or not to print
RXFAX=yes



# These are the default signature lines in a echart note.
# eg [Signed on 16-Aug-2010 14:11 by doctor oscardoc]
# substitute values must be in the form ${SUBSTITUTE_NAME} right now there are three dynamic values DATE, USERSIGNATURE and ROLENAME
# If the SUBSTITUTE_NAME isn't one of those 3 it will look in the resource bundle for the current locale for a value.
# eg oscarEncounter.class.EctSaveEncounterAction.msgSigned in english is "Signed on"
# If the SUBSTITUTE_NAME isn't found in the Resource Bundle it will be blank in the signing line
ECHART_SIGN_LINE=[${oscarEncounter.class.EctSaveEncounterAction.msgSigned} ${DATE} ${oscarEncounter.class.EctSaveEncounterAction.msgSigBy} ${USERSIGNATURE}]\n
ECHART_VERSIGN_LINE=[${oscarEncounter.class.EctSaveEncounterAction.msgVerAndSig} ${DATE} ${oscarEncounter.class.EctSaveEncounterAction.msgSigBy} ${USERSIGNATURE}]\n

# This value should equal the fully qualified path to the wkhtmltopdf command line utility. It may also work if it's just the command
# and the executable is in the path of the running process.
# This is used for converting html screens to pdf's. As an example to send eforms to MyOscar it first converts the eform to pdf.
# This value should not be null.
WKHTMLTOPDF_COMMAND=wkhtmltopdf

# This is the error correction level QR Codes will be rendered with. Do not change this value unless
# you know what you're doing. This is the com.google.zxing.qrcode.decoder.ErrorCorrectionLevel values
QR_CODE_ERROR_CORRECTION_LEVEL=H

# This sets if the provider default for QR codes is enabled or not, true=enabled, false=disabled
QR_CODE_ENABLED_PROVIDER_DEFAULT=false

# This scales the qr code image by the set factor. The scale needs to be large enough such that the print out is readable
# by scaners and not too fragile to things like folding of the paper or minor scratches etc.
# Do not change this unless you really know what you're doing. Valid values are positive integer values. A value of 1 means no scaling will be done.
QR_CODE_IMAGE_SCALE_FACTOR=2

# For clinic with multiple satellites, so that each provider can be assigned to multiple sites and have different location when set schedule, book appt., billing etc.
## off : turn off multisites support, oscar will behave as before
## on : turn on multisites support, use Admin-Misc-Satellite-sites Admin to manage sites and assign them to the provider in provider profile
# multisites=on


# multisites function. When add a new provide, should system validate the provider id range.
# false : turn off provider id validation (default value for backward compatible)
# true : turn on provider id validation. Range will be defined by sites admin
multioffice.formalize.provider.id=false
# the id range for doctors. other staff's id ranges are defined in the site admin page.
multioffice.formalize.doctor.minimum.provider.id=1
multioffice.formalize.doctor.maximum.provider.id=100

# multisites function: so as to identify the role name of the real admin role (the super root in oscar)
multioffice.admin.role.name=admin

###### MyOSCAR ######
MY_OSCAR = no
# How frequent oscar will send/retrieve documents with indivo (in minutes) (default 5 minutes)
MY_OSCAR_EXCHANGE_INTERVAL = 5
#ModuleNames=Indivo

# url for myoscar client because there's a link in the application to it.
myOSCAR.url=http://127.0.0.1:8095/myoscar_client/

# MyOscarServer base url for the web services end points
myoscar_server_base_url=http://localhost:8090/myoscar_server/ws

# This should be the name of the survey in the myoscar system that corresponds to the posi medication survey / symptom checklist survey
# this value is so the system knows which survey results to ask the myoscar server for when generating the reports.
MYOSCAR_SYMPTOM_CHECKLIST_REPORT_TEMPLATE_NAME=Symptom Checklist

myoscar.account_manager_people_cache_size=1024
# 7200000 = 2 hours
myoscar.account_manager_people_cache_time_ms=7200000

# This should control the date format used the application
DATE_FORMAT=yyyy-MM-dd
TIME_FORMAT=HH:mm:ss

# this will disable the password requirements check, i.e. length, etc.
IGNORE_PASSWORD_REQUIREMENTS=false

#System values for OMD CIHI Data Export
vendorId=vendorId not set
vendorBusinessName=McMaster University
vendorCommonName=McMaster University
softwareName=Open Source Clinical Application Resource
softwareCommonName=OSCAR
version=version not set
versionDate=versionDate not set

# Ontario MD HRM properties
OMD_HRM_USER=mcmu
OMD_HRM_IP=**************
OMD_HRM_PORT=22
OMD_HRM_AUTH_KEY_FILENAME=mcmu_sk.ppk

#Email Configuration
email.host=localhost
email.port=25
email.username=
email.password=
email.protocol=smtp
email.connection_security=ssl
#email.recipient_override=<EMAIL>
#email.print_instead_of_send=true


# cxf web services
https_endpoint_url_base=https://127.0.0.1:8081/oscar/ws

#limit one appointment per demographic per day per group (schedule group).
#Prevents rejected claims for OHIP Fee for Service where multiple appts of the same
#type are booked on the same day for the same demographic causes the claim to be rejected
allowMultipleSameDayGroupAppt=yes

#Indivica contributed diabetes flowsheet with sparklines
new_flowsheet_enabled=true

# RMA Billing
rma_enabled=false

#Patient Workflow enhancments
workflow_enhance=false

indivica_hc_read_enabled=false

#Indivica contributed intake form.
appt_intake_form=off

#size of quick chart in eChart. default is 20 if not specified
#quick_chart_size=20


#Turn this to enable the ability to store a local copy of what is returned from the integrator
#INTEGRATOR_LOCAL_STORE=yes

#CAISI should not search for duplicates outside your program domain when true.
caisi.new_client.strict_search=false


#Turn on display of whether demographic form is complete for the demographic
#shown on the add appointment and edit appointment windows.
#Provide semi-colon seperated list of forms to display
#(just as they appear in encounterForm table's form_table column)
appt_formTbl=

#Indivica Rx Enhancmenets
rx_enhance=false

# True if signatures should be enabled in the rx module and false otherwise.
rx_signature_enabled=true

# True if consultation printing should generate a pdf with attachments and false otherwise.
consultation_pdf_enabled=true


#number of days until tickler text turns red
tickler_warn_period=1

# True if signature should be enabled in eforms and false otherwise.
eform_signature_enabled=true

# EForm Generator flags to include indivica fax, sign and print components.
eform_generator_indivica_signature_enabled=false
eform_generator_indivica_print_enabled=false
eform_generator_indivica_fax_enabled=false

# True if Indivica Rich Text Letter is enabled and false otherwise.
indivica_rich_text_letter_enabled=true

#Seconds until a integrated facilty is considered Stale ( has not pushed to the integrator)
#Seconds until a integrated facilty is considered Stale ( has not pushed to the integrator)
#This will cause a warning in the echart to alert user that this might not be all the data.
#-1 will disable it.  Disabled by default
seconds_till_considered_stale=-1

# wait list email email notifications : true | false
enable_wait_list_email_notifications=false
# period in ms, 86400000 should be about 1 day
wait_list_email_notification_period=86400000
# a csv list of programId's to do email notifications on
wait_list_email_notification_program_ids=

#True: Enable ability to add additional messages to the same tickler and update tickler status
tickler_edit_enabled=true
tickler_email_enabled=false
#The tickler email from address
#tickler_email_from_address=<EMAIL>

#The tickler email subject
#tickler_email_subject=Tickler Reminder Test

# True if using indivica's consultation attachment and false otherwise.
consultation_indivica_attachment_enabled=false

# MOH File Management
moh_file_management_enabled=false
ONEDT_INBOX=/var/lib/OscarDocument/{oscar_practice_id}/onEDTDocs/inbox/
ONEDT_OUTBOX=/var/lib/OscarDocument/{oscar_practice_id}/onEDTDocs/outbox/
ONEDT_SENT=/var/lib/OscarDocument/{oscar_practice_id}/onEDTDocs/sent/
ONEDT_ARCHIVE=/var/lib/OscarDocument/{oscar_practice_id}/onEDTDocs/archive/

#Antenal Enhanced form - lab req to use .. either 07 or 10
onare_labreqver=07

born_env=T
born_ftps_host=ftp.test.com
born_ftps_port=21
born_ftps_username=test
born_ftps_password=test
born_ftps_remote_dir=/

#this is to override for req provider in the labreq 07/10 forms.
#written for Maternity Centre where all labs need to come out as the NP w/ billing no
#lab_req_override=true
#lab_req_provider=Dr Clinic Head
#lab_req_billing_no=654321



# Enable and configure an external prescription service
util.erx.enabled=false
# software vendor, used by SOAP bridge
util.erx.vendor=McMaster University
# software name, used by SOAP bridge
util.erx.software=OSCAR
# software version, used by SOAP bridge
util.erx.version=11
# time interval to synchronise prescription from the External Prescriber, in ms (default 24hrs).
#NOTE: You need to enable the ERx applicationContext module name to enable the interval Task Scheduler. See ModuleNames property.
util.erx.synchronize_interval=86400000
# default username used by SOAP bridge
util.erx.clinic_username=username
# default password used by SOAP bridge
util.erx.clinic_password=password
# default facility ID used by SOAP bridge
util.erx.clinic_facility_id=123
# training mode disabled used by SOAP bridge
util.erx.clinic_training_mode=false
# The local of the clinic, 1 (fr-CA), 2 (en-CA), 3 (en-US), used by SOAP bridge
util.erx.clinic_locale=2
# default URL used by the SOAP Bridge to synchronize prescriptions
util.erx.webservice_url=https://www.zoommed.com/zrx/ZRxPMISBridge/PMISBridge.asmx
# default URL used by in the oscarRx link for the External Prescriber
util.erx.oscarerx_sso_url=https://www.zoommed.com/zrx/ZRxWebMobile/ProductSearch.aspx?

#Display Chart No on prescription
showRxChartNo=false

#Default info for redirect study site
redirectstudysite_default_baseURL = http://competeii.mcmaster.ca/PDSsecurity/login.asp
redirectstudysite_default_username = yilee18
redirectstudysite_default_password = 515750564848564853485353544852485248484851575150

web_service_client.connection_timeout_ms=1500
web_service_client.received_timeout_ms=3000

# Custom Add Demographic Form fields
#cust_demographic_fields = last_name,first_name,official_lang,title,address,city,province,postal,phone,phone2,cellphone,newsletter,email,pin,dob,sex,hin,eff_date,hc_type,countryOfOrigin,sin,cytolNum,doctor,nurse,midwife,resident,referral_doc,family_doc,roster_status,date_rostered,patient_status,chart_no,waiting_list,date_joined,end_date,alert,form_notes
#Brazil:address_no,district,rg,cpf,marital_state,birth_certificate,marriage_certificate,partner_name,father_name,mother_name

# Enable clinicaid billing
# clinicaid_billing=true

# Required if clinicaid_billing is set to true
# clinicaid_domain=http://clinicly.com

# required for accessing the clinicaid API
# clinicaid_api_domain=https://demo.clinicaid.ca

# OscarHost Login page
oscarhost_login=https://login.junoemr.com/login/{oscar_practice_id}

# Set the location to write fax files to
fax_file_location=/var/lib/OscarDocument/{oscar_practice_id}/fax/outgoing/

# Enable full demographics merge
#enable_full_merge = true

# Custom "Search Urgent Labs by Group" feature
#inbox_urgent_search=no

casemgmt_photo_enabled=false

# Autocomplete for billing referral doctors on Demographic Master record
billingreferral_demographic_refdoc_autocomplete=true
#Auto populate billing form with referral doctor
auto_populate_billingreferral_bc=true
# OHSUPPORT-2718
# default the referral type to B="refer By" or T="refer To"
#auto_populate_billingreferral_type_bc=B 
# default the referral physician. overrides appointment auto-fill
#auto_populate_billingreferral_physicianID_bc=999998

# OHSUPPORT-1700 kensington: additional patient info at the top of the eChart
#echart_additional_patient_info=true

# OHSUPPORT-1850 perrier_lepage: appointment reason in week view, with top align
#week_view_reason_display=true
#week_view_reason_preferred_length=30

# OHSUPPORT-1840 social history on consult request
#consult_request_social_history_option=true

# OHSUPPORT-1839 override date of birth format on master file (use SimpleDateFormat format syntax)
#demographic_dob_format_override=MMM dd/yyyy

# OHSUPPORT-1928 appointment reminder emails
#appointment_reminder_enabled=true
#appointment_reminder_from_email_address=<EMAIL>
#appointment_reminder_from_name=From Name
#appointment_reminder_subject=Appointment Reminder
# _java: SimpleDateFormat format, _ruby: DateTime format
#appointment_reminder_appt_date_format_java=EEEE MMMM d yyyy 'at' h:mm a
#appointment_reminder_appt_date_format_ruby=%A %B %-d %Y at %-l:%M %p
#appointment_reminder_automated_update_status=a
# OHSUPPORT-2217 Prevent users from messing with role privileges in Oscar
RESTRICT_ROLE_CHANGES=yes

default_search_mode=search_name

#OHSUPPORT-2456
#custom lab routing for excelleris labs
#Parses custom_lab_routeX properties (starting at 1)
#Property format: custom_lab_routeX=<excelleris_lab_account>,<provider_no>
#EG:
#custom_lab_route1=khatsahlano1,300
#custom_lab_route2=khatsahlano2,100

# OHSUPPORT-2222: when labs are forwarded to a provider, if they had previously filed it, set it back to new
#AUTO_UNFILE_LABS=yes

# OHSUPPORT-2463: invert dark document previews (workaround pdf to png bug where previews had inverted colors)
#INVERT_DARK_DOCUMENT_PREVIEWS=yes

# OHSUPPORT-2541 show uploaded by in the inbox
#INBOX_UPLOADED_BY_COLUMN=yes

#OHSUPPORT-2712 - adds licensed producers dropdown to demographic master files.
#REQUIRES the database patch be applied to create the correct tables.
#show_demographic_licensed_producers=true

# OHSUPPORT-2718
# default the referral type to B="refer By" or T="refer To"
#auto_populate_billingreferral_type_bc=T
# default the billing physician. overrides appointment auto-fill
#auto_populate_billing_bc_billingPhysicianID=999998

# OHSUPORT-2883 - auto-fill billing physician using assigned provider no. 
# overwritten by setting specific ID (above), and appointment auto-fill
#auto_populate_billing_bc_billingPhysician=true

# OHSUPORT-2883 - auto-fill diagnostic/other codes with specific values (from custom consultation invoice link). BC only
#auto_populate_billing_bc_diagnostic_codesVal1_consult=780
#auto_populate_billing_bc_other_codesVal1_consult=3333
#auto_populate_billing_bc_billingreferral_type1_consult=T

#OHSUPPORT-2764
# Customize Report-list default appointment start/end times (use the 24 hour clock. must be int values)
#reportlist_defaultApptStartTime=8
#reportlist_defaultApptEndTime=20

#OHSUPPORT-2883 - adds a link to invoices in the consultation request page.
#enable_consultation_invoice_link=true

#OHSUPPORT-2932 - add encounter notes for faxes
#encounter_notes_add_fax_notes_eform=on
#encounter_notes_add_fax_notes_consult=on

#OHSUPPORT-2929 - disable the 'please reply to' header message in consultation print/fax pages. 
# -- Only for consultation_fax_enabled=true faxing/printing
#consultation_fax_disable_header_message=true

#OHSUPPORT-2362
#Sets default providers based on user IP when creating ticklers. It loops
#through starting at TICKLERIP1/PROVIDER1/SITE1 until there none left. EG:
#TICKLERIP1=***********
#TICKLERPROVIDER1=239
#TICKLERSITE1=2
#TICKLERIP2=*************
#TICKLERPROVIDER2=79
#TICKLERSITE2=1

# Settings for sending documents via email
#document_email_enabled=true                                                     
#document_email_subject=Oscar Document
#document_email_body=Document Sent From Oscar
#document_email_name=Oscar                                    
#document_email_from_address=<EMAIL>

# Maximum memory usage for loading PDFs, in bytes
#PDF_MAX_MEM_USAGE=512000000

display_date_format=yyyy-MM-dd

# Automatically link assigned document to demographic's provider
assign_document.link_docs_to_provider=yes

# Show appointment count for providers on schedule page
schedule.show_appointment_count=no

# Proxy information for teleplan.  Fill out both to enable the proxy.
#teleplan_proxy_host = 127.0.0.1
#teleplan_proxy_port = 3128
