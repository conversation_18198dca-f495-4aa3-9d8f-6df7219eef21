<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">

<html>
<head>
<title>Custom Template</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<style type="text/css">
body {font-size: 1em; font-family:"Times New Roman", Times, serif; background-color: #FFFFFF;}
</style>

<style type="text/css" media="print">
* {color: #000000;}
</style>

</head>
<body contenteditable onLoad="document.designMode = 'on';">

<table style="text-align: left; width: 100%;" border="0" cellpadding="2"
cellspacing="2">
<tbody>
<tr>
<td colspan="1" rowspan="2" style="vertical-align: top;">
<img style="width: 244px; height: 148px;" alt="Oscar" src="http://www.oscarcanada.org/logo.jpg">
</td>
<td style="vertical-align: top; text-align: center;"><big
style="font-family: Helvetica,Arial,sans-serif;"><big><big><big>PHC, MD </big></big></big></big><br>
</td>
</tr>
<tr align="center">
<td style="vertical-align: top;"><big><big><span
style="font-family: Helvetica,Arial,sans-serif;">A Canadian Rural
Doctor Who <span
style="color: red; font-style: italic; text-decoration: underline;">Loves</span>
Oscar</span></big></big><br>
</td>
</tr>
</tbody>
</table>
<br>  
<br>
<table border="1" bordercolor="red">
<tbody>
<tr>
<td>This is a sample template that you can customise to your
purposes. Simply <br>
<ol>
<li>Open the custom.html file (found in your eForm images) in a
html editor such as... this one found in Oscar! 
(vi or <a href="http://www.kompozer.net/">KompoZer</a> are other html editors)
and change whatever you want between the &lt;body&gt; tags.&lt;/body&gt;<br>
</li>
<li>Yeah you can use style elements, practically <span
style="font-weight: bold;">anything</span> you like, it will be
supported in Letter, only the simple stuff is exampled here.</li>
<li>Use any of the OSCAR fields by enclosing them with pairs
of&nbsp; #<span>#</span> signs and they will auto populate from the database.
(sample database fields and their values for the patient in whose chart
you opened this template are listed below)</li>
<li>Make your own fields by enclosing your prompt with pairs of
#<span>#</span> signs and the user will be prompted for the value</li>
<li>Save your version of custom.html to the hard drive.
<li>Delete the custom.html in your eForms images and upload
your version of custom.html and you are off!
</li>
</ol>
&nbsp; Enjoy! (PS you can edit any of the templates this way)<br>
</td>
</tr>
</tbody>
</table>
<br>
#<span>#Please supply the serum rhubarb=default response#</span>#  <br>##Please supply the serum rhubarb=default response##<br>
#<span>#clinic_name#</span>#  <br>##clinic_name##<br>
#<span>#clinic_address#</span>#  <br> ##clinic_address##<br>
#<span>#clinic_addressLine#</span>#  <br> ##clinic_addressLine##<br>
#<span>#clinic_addressLineFull#</span>#  <br> ##clinic_addressLineFull##<br>
#<span>#clinic_label#</span>#  <br> ##clinic_label##<br>
#<span>#clinic_fax#</span>#  <br> ##clinic_fax##<br>
#<span>#clinic_phone#</span>#  <br> ##clinic_phone##<br>
#<span>#clinic_city#</span>#  <br> ##clinic_city##<br>
#<span>#clinic_province#</span>#  <br> ##clinic_province##<br>
#<span>#clinic_postal#</span>#  <br> ##clinic_postal##<br>
#<span>#patient_name#</span>#  <br> ##patient_name##<br>
#<span>#first_last_name#</span>#  <br> ##first_last_name##<br>
#<span>#patient_nameF#</span>#  <br> ##patient_nameF##<br>
#<span>#patient_nameL#</span>#  <br> ##patient_nameL##<br>
#<span>#label#</span>#  <br> ##label##<br>
#<span>#NameAddress#</span>#  <br> ##NameAddress##<br>
#<span>#address#</span>#  <br> ##address##<br>
#<span>#addressline#</span>#  <br> ##addressline##<br>
#<span>#phone#</span>#  <br> ##phone##<br>
#<span>#phone2#</span>#  <br> ##phone2##<br>
#<span>#province#</span>#  <br> ##province##<br>
#<span>#city#</span>#  <br> ##city##<br>
#<span>#postal#</span>#  <br> ##postal##<br>
#<span>#dob#</span>#  <br> ##dob##<br>
#<span>#dobc#</span>#  <br> ##dobc##<br>
#<span>#dobc2#</span>#  <br> ##dobc2##<br>
#<span>#hin#</span>#  <br> ##hin##<br>
#<span>#hinc#</span>#  <br> ##hinc##<br>
#<span>#hinversion#</span>#  <br> ##hinversion##<br>
#<span>#ageComplex#</span>#  <br> ##ageComplex##<br>
#<span>#age#</span>#  <br> ##age##<br>
#<span>#sex#</span>#  <br> ##sex##<br>
#<span>#medical_history#</span>#  <br> ##medical_history##<br>
#<span>#recent_rx#</span>#  <br> ##recent_rx##<br>
#<span>#social_family_history#</span>#  <br> ##social_family_history##<br>
#<span>#other_medications_history#</span>#  <br> ##other_medications_history##<br>
#<span>#reminders#</span>#  <br> ##reminders##<br>
#<span>#ongoingconcerns#</span>#  <br> ##ongoingconcerns##<br>
#<span>#provider_name_first_init#</span>#  <br> ##provider_name_first_init##<br>
#<span>#current_user#</span>#  <br> ##current_user##<br>
#<span>#doctor_work_phone#</span>#  <br> ##doctor_work_phone##<br>
#<span>#doctor#</span>#  <br> ##doctor##<br>
#<span>#today#</span>#  <br> ##today##<br>
#<span>#referral_name#</span>#  <br> ##referral_name##<br>
#<span>#referral_address#</span>#  <br> ##referral_address##<br>
#<span>#referral_phone#</span>#  <br> ##referral_phone##<br>
#<span>#referral_fax#</span>#  <br> ##referral_fax##<br>

</body>
</html>
