<?xml version='1.0' encoding='utf-8'?>

<Server port="8005" shutdown="SHUTDOWN">
	<!-- Security listener. Documentation at /docs/config/listeners.html
	<Listener className="org.apache.catalina.security.SecurityListener" />
	-->
	<!--APR library loader. Documentation at /docs/apr.html -->
	<Listener className="org.apache.catalina.core.AprLifecycleListener" SSLEngine="on" />
	<!--Initialize Jasper prior to webapps are loaded. Documentation at /docs/jasper-howto.html -->
	<Listener className="org.apache.catalina.core.JasperListener" />
	<!-- Prevent memory leaks due to use of particular java/javax APIs-->
	<Listener className="org.apache.catalina.core.JreMemoryLeakPreventionListener" />
	<Listener className="org.apache.catalina.mbeans.GlobalResourcesLifecycleListener" />
	<Listener className="org.apache.catalina.core.ThreadLocalLeakPreventionListener" />

	<Service name="Catalina">

		<!--The connectors can use a shared executor, you can define one or more named thread pools -->
		<!-- <Executor name="tomcatThreadPool" namePrefix="catalina-exec-" maxThreads="150" minSpareThreads="4"/> -->

		<!-- A "Connector" represents an endpoint by which requests are received and responses are returned. Documentation at : Java HTTP Connector: /docs/config/http.html (blocking & non-blocking) Java AJP Connector: /docs/config/ajp.html APR (HTTP/AJP) Connector: 
			/docs/apr.html Define a non-SSL HTTP/1.1 Connector on port 8080 -->

		<Connector port="8080" protocol="org.apache.coyote.http11.Http11Protocol" enableLookups="false" maxThreads="8" executor="tomcatThreadPool" connectionTimeout="20000" keepAliveTimeout="300000" disableUploadTimeout="true" compression="on" URIEncoding="UTF-8" />
    	<Connector port="8081" protocol="org.apache.coyote.http11.Http11Protocol" enableLookups="false" maxThreads="8" executor="tomcatThreadPool" connectionTimeout="20000" keepAliveTimeout="300000" disableUploadTimeout="true" compression="on" URIEncoding="UTF-8" SSLEnabled="true" scheme="https" secure="true" clientAuth="false" sslProtocol="TLS" keystoreFile="conf/.keystore" />
		<Engine name="Catalina" defaultHost="localhost">

			<Host name="localhost" appBase="webapps" unpackWARs="true" autoDeploy="true">

				<!-- Access log processes all example. Documentation at: /docs/config/valve.html Note: The pattern used is equivalent to using pattern="common" -->
				<Valve className="org.apache.catalina.valves.AccessLogValve" directory="logs" prefix="localhost_access_log." suffix=".txt" pattern="%h %l %u %t &quot;%r&quot; %s %b" resolveHosts="false" />

			</Host>
		</Engine>
	</Service>
</Server>
